"""
Test endpoints for verifying the complete application workflow
- Analysis generation (all four steps)  
- RAG retrieval
- CosmosDB storage
- Message summarization
"""

from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import logging
import uuid
import asyncio
import time
from datetime import datetime

from app.core.config import settings
from app.services.enhanced_context_service import context_service, ConversationMessage, MessageMetadata
from app.services.rag_service import rag_service
from app.services.llm_tcc_service import llm_tcc_service

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/workflow-test", tags=["workflow-test"])

# Request and response models
class TestScenarioRequest(BaseModel):
    """Request model for testing analysis steps."""
    scenario: str = Field(..., description="The scenario or context situation")
    reaction: str = Field(..., description="The user's reaction to the scenario")
    user_id: Optional[str] = Field(None, description="Optional user ID")
    session_id: Optional[str] = Field(None, description="Optional session ID")
    parameters: Optional[Dict[str, Any]] = Field(None, description="Optional parameters")

class SummarizationTestRequest(BaseModel):
    """Request model for testing summarization."""
    content: str = Field(..., description="Message content to summarize")
    message_type: str = Field("analysis", description="Type of message")
    step: Optional[str] = Field("analyze", description="Analysis step")
    user_id: Optional[str] = Field(None, description="Optional user ID")
    session_id: Optional[str] = Field(None, description="Optional session ID")

class WorkflowResponse(BaseModel):
    """Response model for workflow tests."""
    status: str
    message: str
    data: Dict[str, Any]

@router.post("/analysis/{step}", response_model=WorkflowResponse)
async def test_analysis_step(
    step: str = Path(..., description="Analysis step (analyze, impacts, sources, strategies)"),
    request: TestScenarioRequest = Body(...)
):
    """Test the complete workflow for a specific analysis step."""
    # Validate the step
    valid_steps = ["analyze", "impacts", "sources", "strategies"]
    if step not in valid_steps:
        raise HTTPException(status_code=400, detail=f"Invalid step: {step}. Must be one of {valid_steps}")
    
    # Generate user_id and session_id if not provided
    user_id = request.user_id or f"test_user_{uuid.uuid4().hex[:8]}"
    session_id = request.session_id or f"test_session_{uuid.uuid4().hex[:8]}"
    
    try:
        # Start timing
        start_time = time.time()
        
        # 1. Call LLM service to generate response
        llm_result = await llm_tcc_service.generate_response(
            scenario=request.scenario,
            reaction=request.reaction,
            step=step,
            user_id=user_id,
            session_id=session_id,
            parameters=request.parameters or {}
        )
        
        # 2. Get context to verify storage
        context = await context_service.get_context(user_id, session_id)
        context_stats = {
            "recent_messages": len(context.recent_messages),
            "summarized_messages": len(context.summarized_messages),
            "permanent_metadata": len(context.permanent_metadata)
        }
        
        # 3. Get conversation history
        history = await context_service.get_conversation_history(user_id, session_id)
        
        # Timing
        total_time = time.time() - start_time
        
        # Prepare response with all relevant data
        return WorkflowResponse(
            status="success",
            message=f"Analysis for step '{step}' completed successfully in {total_time:.2f}s",
            data={
                "llm_response": llm_result.get("response"),
                "token_usage": llm_result.get("metadata", {}).get("token_usage", {}),
                "processing_time_ms": llm_result.get("metadata", {}).get("processing_time_ms"),
                "context_stats": context_stats,
                "user_id": user_id,
                "session_id": session_id,
                "history_length": len(history),
                "rag_sources_used": len(llm_result.get("metadata", {}).get("rag_sources", [])),
            }
        )
    except Exception as e:
        logger.error(f"Error in analysis workflow test: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Workflow test failed: {str(e)}")

@router.post("/complete-workflow", response_model=WorkflowResponse)
async def test_complete_workflow(request: TestScenarioRequest = Body(...)):
    """Test the complete workflow through all four analysis steps."""
    # Generate user_id and session_id if not provided
    user_id = request.user_id or f"test_user_{uuid.uuid4().hex[:8]}"
    session_id = request.session_id or f"test_session_{uuid.uuid4().hex[:8]}"
    
    try:
        # Start timing
        start_time = time.time()
        
        results = {}
        steps = ["analyze", "impacts", "sources", "strategies"]
        
        # Run each step sequentially
        for step in steps:
            step_start = time.time()
            
            # Generate response for this step
            llm_result = await llm_tcc_service.generate_response(
                scenario=request.scenario,
                reaction=request.reaction,
                step=step,
                user_id=user_id,
                session_id=session_id,
                parameters=request.parameters or {}
            )
            
            step_time = time.time() - step_start
            
            # Add results for this step
            results[step] = {
                "response": llm_result.get("response", "")[:100] + "...",  # Truncate for brevity
                "token_usage": llm_result.get("metadata", {}).get("token_usage", {}),
                "processing_time_ms": llm_result.get("metadata", {}).get("processing_time_ms"),
                "rag_sources_count": len(llm_result.get("metadata", {}).get("rag_sources", [])),
                "step_time_seconds": step_time
            }
        
        # Get context after all steps
        context = await context_service.get_context(user_id, session_id)
        context_stats = {
            "recent_messages": len(context.recent_messages),
            "summarized_messages": len(context.summarized_messages),
            "permanent_metadata": len(context.permanent_metadata)
        }
        
        # Get conversation history
        history = await context_service.get_conversation_history(user_id, session_id)
        
        # Timing
        total_time = time.time() - start_time
        
        return WorkflowResponse(
            status="success",
            message=f"Complete workflow test finished successfully in {total_time:.2f}s",
            data={
                "step_results": results,
                "context_stats": context_stats,
                "user_id": user_id,
                "session_id": session_id,
                "history_length": len(history),
                "total_time_seconds": total_time
            }
        )
    except Exception as e:
        logger.error(f"Error in complete workflow test: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Complete workflow test failed: {str(e)}")

@router.post("/summarization", response_model=WorkflowResponse)
async def test_summarization(request: SummarizationTestRequest = Body(...)):
    """Test direct summarization and storage."""
    # Generate user_id and session_id if not provided
    user_id = request.user_id or f"test_user_{uuid.uuid4().hex[:8]}"
    session_id = request.session_id or f"test_session_{uuid.uuid4().hex[:8]}"
    
    try:
        # Start timing
        start_time = time.time()
        
        # 1. Create a message to summarize
        message_id = f"test_summarize_{uuid.uuid4().hex[:8]}"
        timestamp = datetime.now()
        
        metadata = MessageMetadata(
            timestamp=timestamp,
            user_id=user_id,
            session_id=session_id,
            message_type=request.message_type,
            step=request.step,
            tokens=len(request.content.split())  # Simple approximation
        )
        
        message = ConversationMessage(
            id=message_id,
            content=request.content,
            metadata=metadata,
            is_summarized=False
        )
        
        # 2. Direct summarization
        direct_summary = await context_service.summarize_message(message)
        logger.info(f"Direct summary: {direct_summary}")
        
        # 3. Update message with summary and mark as summarized
        message.summary = direct_summary
        message.is_summarized = True
        
        # 4. Store the message directly
        await context_service._save_message(message)
        logger.info(f"Saved message with summary: {message.id}")
        
        # 5. Get context to verify storage
        context = await context_service.get_context(user_id, session_id)
        
        # 6. Find the stored message
        found_message = None
        for msg in context.summarized_messages:
            if msg.id == message.id:
                found_message = msg
                break
        
        # Check if message was found and summary matches
        if found_message:
            summary_match = direct_summary == found_message.summary
            status = "success" if summary_match else "warning"
            message_text = "Summary stored successfully" if summary_match else "Retrieved summary differs from original"
        else:
            status = "error"
            message_text = "Stored message not found"
        
        # Timing
        total_time = time.time() - start_time
        
        return WorkflowResponse(
            status=status,
            message=message_text,
            data={
                "original_summary": direct_summary,
                "retrieved_summary": found_message.summary if found_message else None,
                "match": summary_match if found_message else False,
                "message_id": message_id,
                "user_id": user_id,
                "session_id": session_id,
                "summarization_time_seconds": total_time
            }
        )
    except Exception as e:
        logger.error(f"Error in summarization test: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Summarization test failed: {str(e)}")

@router.post("/context-workflow", response_model=WorkflowResponse)
async def test_context_workflow(request: TestScenarioRequest = Body(...)):
    """Test the context management workflow including forced summarization."""
    # Generate user_id and session_id if not provided
    user_id = request.user_id or f"test_user_{uuid.uuid4().hex[:8]}"
    session_id = request.session_id or f"test_session_{uuid.uuid4().hex[:8]}"
    
    try:
        # Start timing
        start_time = time.time()
        
        # 1. Get context and set low message limit to force summarization
        context = await context_service.get_context(user_id, session_id)
        original_max = context.max_recent_messages
        context.max_recent_messages = 2  # Set low to force summarization
        await context_service._save_context_metadata(context)
        
        # 2. Add scenario and reaction messages
        scenario_id = await context_service.add_message(
            user_id=user_id,
            session_id=session_id,
            content=request.scenario,
            message_type="scenario"
        )
        
        reaction_id = await context_service.add_message(
            user_id=user_id,
            session_id=session_id,
            content=request.reaction,
            message_type="reaction"
        )
        
        # 3. Run the first analysis step to trigger summarization
        llm_result = await llm_tcc_service.generate_response(
            scenario=request.scenario,
            reaction=request.reaction,
            step="analyze",
            user_id=user_id,
            session_id=session_id,
            parameters=request.parameters or {}
        )
        
        # 4. Get updated context to check for summaries
        updated_context = await context_service.get_context(user_id, session_id)
        
        # 5. Find stored summaries
        stored_summaries = []
        for msg in updated_context.summarized_messages:
            stored_summaries.append({
                "id": msg.id,
                "content_preview": msg.content[:50] + "..." if len(msg.content) > 50 else msg.content,
                "summary": msg.summary,
                "type": msg.metadata.message_type,
                "step": msg.metadata.step
            })
        
        # 6. Reset the context limits
        updated_context.max_recent_messages = original_max
        await context_service._save_context_metadata(updated_context)
        
        # Timing
        total_time = time.time() - start_time
        
        return WorkflowResponse(
            status="success",
            message=f"Context workflow test completed in {total_time:.2f}s with {len(stored_summaries)} summarized messages",
            data={
                "context_before": {
                    "max_recent_messages": 2  # The value we set
                },
                "context_after": {
                    "recent_messages": len(updated_context.recent_messages),
                    "summarized_messages": len(updated_context.summarized_messages),
                    "permanent_metadata": len(updated_context.permanent_metadata)
                },
                "stored_summaries": stored_summaries,
                "user_id": user_id,
                "session_id": session_id
            }
        )
    except Exception as e:
        logger.error(f"Error in context workflow test: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Context workflow test failed: {str(e)}")

@router.get("/rag-test", response_model=WorkflowResponse)
async def test_rag(
    query: str = Query(..., description="The query to use for RAG retrieval"),
    step: str = Query("analyze", description="Analysis step (analyze, impacts, sources, strategies)"),
    top_k: int = Query(3, description="Number of results to retrieve")
):
    """Test the RAG retrieval system."""
    try:
        # Start timing
        start_time = time.time()
        
        
        if not rag_service:
            raise HTTPException(status_code=500, detail="RAG service not available")
        
        # Perform retrieval
        context_str, rag_metadata = rag_service.get_context_and_rag_metadata(
            query=query, 
            step=step,
            top_k=top_k
        )
        
        # Get RAG metrics
        rag_metrics = rag_service.get_metrics()
        
        # Timing
        total_time = time.time() - start_time
        
        return WorkflowResponse(
            status="success",
            message=f"RAG retrieval completed in {total_time:.2f}s with {len(rag_metadata)} results",
            data={
                "query": query,
                "step": step,
                "context_length": len(context_str),
                "results_count": len(rag_metadata) - 1,  # Subtract 1 for metrics entry
                "rag_metadata": rag_metadata,
                "rag_metrics": rag_metrics,
                "retrieval_time_seconds": total_time
            }
        )
    except Exception as e:
        logger.error(f"Error in RAG test: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"RAG test failed: {str(e)}")

@router.get("/cosmos-status", response_model=WorkflowResponse)
async def check_cosmos_status():
    """Check the CosmosDB connection and container status."""
    try:
        # Get context service
        context_svc = get_context_service()
        if not context_svc:
            raise HTTPException(status_code=500, detail="Context service not available")
        
        # Check if CosmosDB is available
        cosmos_available = context_svc.use_cosmos_db
        
        if not cosmos_available:
            return WorkflowResponse(
                status="warning",
                message="CosmosDB is not available, using in-memory storage",
                data={
                    "cosmos_available": False,
                    "storage_mode": "in-memory"
                }
            )
        
        # Get container information
        containers = [
            "contexts_container",
            "recent_container",
            "summarized_container",
            "metadata_container",
            "users_container",
            "session_index_container"
        ]
        
        container_info = {}
        for container_name in containers:
            if hasattr(context_svc, container_name):
                container = getattr(context_svc, container_name)
                try:
                    # Try to get document count
                    query = "SELECT VALUE COUNT(1) FROM c"
                    items = list(container.query_items(
                        query=query,
                        enable_cross_partition_query=True
                    ))
                    item_count = items[0] if items else 0
                    
                    container_info[container_name] = {
                        "status": "available",
                        "document_count": item_count
                    }
                except Exception as e:
                    container_info[container_name] = {
                        "status": "error",
                        "error": str(e)
                    }
        
        return WorkflowResponse(
            status="success",
            message="CosmosDB status check completed",
            data={
                "cosmos_available": True,
                "cosmos_endpoint": settings.COSMOS_ENDPOINT,
                "cosmos_database": settings.COSMOS_DATABASE,
                "containers": container_info
            }
        )
    except Exception as e:
        logger.error(f"Error checking CosmosDB status: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"CosmosDB status check failed: {str(e)}")