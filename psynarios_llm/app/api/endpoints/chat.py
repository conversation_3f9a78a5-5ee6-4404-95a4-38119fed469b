"""
Chat API endpoints with strict CosmosDB storage - UPDATED VERSION with shutdown fixes and latency improvements.
All initialization and data must be stored in and retrieved from CosmosDB.
File: app/api/endpoints/chat.py - UPDATED VERSION WITH OPTIMIZATIONS
"""

import asyncio
import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime
import uuid
from contextlib import asynccontextmanager
from app.services.session_scenario_service import session_scenario_service

from app.services.llm_tcc_service import llm_tcc_service
from app.services.db_service import get_scenario_by_id
from app.monitoring.latency_tracker import track_async

logger = logging.getLogger(__name__)
router = APIRouter()

class ChatInitRequest(BaseModel):
    scenario_id: str

class ChatMessageRequest(BaseModel):
    session_id: str
    message: str

class ChatResponse(BaseModel):
    session_id: Optional[str] = None
    response: str
    metadata: Optional[Dict[str, Any]] = None
    timestamp: str = datetime.now().isoformat()

# Service health tracking to prevent requests to failing services
class ServiceHealthTracker:
    def __init__(self):
        self.service_status = {
            "cosmos_db": {"healthy": True, "error_count": 0, "last_error": 0},
            "context_service": {"healthy": True, "error_count": 0, "last_error": 0},
            "llm_service": {"healthy": True, "error_count": 0, "last_error": 0}
        }
        self.max_errors = 3
        self.reset_interval = 300  # 5 minutes
    
    def record_error(self, service_name: str):
        if service_name in self.service_status:
            status = self.service_status[service_name]
            status["error_count"] += 1
            status["last_error"] = datetime.now().timestamp()
            
            if status["error_count"] >= self.max_errors:
                status["healthy"] = False
                logger.warning(f"Service {service_name} marked as unhealthy after {status['error_count']} errors")
    
    def is_healthy(self, service_name: str) -> bool:
        if service_name not in self.service_status:
            return True
        
        status = self.service_status[service_name]
        current_time = datetime.now().timestamp()
        
        # Reset if enough time has passed
        if (current_time - status["last_error"]) > self.reset_interval:
            status["error_count"] = 0
            status["healthy"] = True
        
        return status["healthy"]

health_tracker = ServiceHealthTracker()

@asynccontextmanager
async def timeout_protection(operation_name: str, timeout_seconds: int):
    """Context manager for operations with timeout and health tracking."""
    try:
        async with asyncio.timeout(timeout_seconds):
            yield
    except asyncio.TimeoutError:
        logger.error(f"{operation_name} timed out after {timeout_seconds}s")
        health_tracker.record_error("cosmos_db" if "database" in operation_name.lower() else "context_service")
        raise HTTPException(status_code=504, detail=f"{operation_name} timeout")
    except Exception as e:
        logger.error(f"{operation_name} failed: {str(e)}")
        if "cosmos" in str(e).lower() or "database" in str(e).lower():
            health_tracker.record_error("cosmos_db")
        elif "context" in str(e).lower():
            health_tracker.record_error("context_service")
        elif "llm" in str(e).lower():
            health_tracker.record_error("llm_service")
        raise

async def safe_import_services():
    """Safely import services with error handling."""
    try:
        from app.services.chat_session_service import chat_session_service
        from app.services.enhanced_context_service import context_service
        
        return chat_session_service, context_service
    except ImportError as e:
        logger.error(f"Failed to import services: {str(e)}")
        raise HTTPException(status_code=503, detail="Required services not available")

@router.post("/init", 
             response_model=ChatResponse,
             summary="Initialize a new chat session")
async def initialize_chat(request: ChatInitRequest):
    """Initialize a new chat session with optimized error handling and concurrency."""
    async with track_async("chat", "init"):
        try:
            # Quick health checks
            if not health_tracker.is_healthy("cosmos_db"):
                raise HTTPException(status_code=503, detail="Database service temporarily unavailable")
            
            # Import services
            chat_session_service, context_service = await safe_import_services()
            
            # Verify CosmosDB availability with timeout
            async with timeout_protection("CosmosDB verification", 5):
                if not chat_session_service.use_cosmos_db:
                    raise HTTPException(
                        status_code=503, 
                        detail="CosmosDB not available. Chat sessions require database storage."
                    )
                
                if not context_service.use_cosmos_db:
                    raise HTTPException(
                        status_code=503, 
                        detail="CosmosDB not available. Context service requires database storage."
                    )
            
            # Get scenario content with timeout
            async with timeout_protection("Scenario retrieval", 3):
                scenario_content = f"Scénario ID: {request.scenario_id}"
                llm_scenario = await asyncio.to_thread(get_scenario_by_id, request.scenario_id)
                
                if llm_scenario:
                    scenario_content = f"Scénario ID: {request.scenario_id}\nContenu: {llm_scenario}"
                    logger.info(f"Using PostgreSQL scenario content for ID: {request.scenario_id}")
                else:
                    logger.warning(f"No scenario found in PostgreSQL for ID: {request.scenario_id}")
            
            # Create session with timeout
            async with timeout_protection("Session creation", 10):
                # Create a new session in CosmosDB via chat_session_service
                session_id = await chat_session_service.create_session(
                    scenario_id=request.scenario_id,
                    scenario_content=scenario_content
                )

                # Create session-scenario link asynchronously (non-blocking)
                asyncio.create_task(
                    asyncio.to_thread(
                        session_scenario_service.create_session_scenario_link,
                        session_id=session_id,
                        scenario_id=request.scenario_id
                    )
                )
                
                # Create welcome message
                welcome_message = """Bonjour et bienvenue ! 👋

                Je suis Lucien, votre expert en thérapie cognitivo-comportementale (TCC) et thérapie d'acceptation et d'engagement (ACT). Je suis là pour vous accompagner dans l'exploration de vos réactions professionnelles.

                **Comment ça fonctionne :**
                1. 📋 Je vous présenterai un scénario professionnel réaliste
                2. 💭 Vous partagerez vos réactions authentiques (pensées, émotions, comportements)
                3. 🔍 Nous analyserons ensemble vos réactions selon l'approche TCC/ACT
                4. 💡 Nous explorerons les impacts, sources et stratégies d'amélioration

                **Votre espace sûr :** Il n'y a aucun jugement ici. L'objectif est d'explorer vos réactions naturelles pour mieux les comprendre et développer des stratégies plus adaptées.

                Dites-moi quand vous êtes prêt(e) à découvrir le scénario d'aujourd'hui ! 🚀"""                
                # Add messages to both services concurrently
                session_message_task = chat_session_service.add_message_to_session(
                    session_id=session_id,
                    role="assistant",
                    content=welcome_message,
                    metadata={
                        "step": "init",
                        "is_tcc_mode": False,
                        "scenario_matched": llm_scenario is not None,
                        "scenario_id": request.scenario_id,
                        "scenario_content": scenario_content
                    }
                )
                
                context_message_task = context_service.add_message(
                    session_id=session_id,
                    content=scenario_content,
                    message_type="scenario",
                    step=None,
                    tokens=len(scenario_content.split()),
                    parameters={
                        "scenario_id": request.scenario_id,
                        "session_initialized": True,
                        "scenario_source": "postgresql" if llm_scenario else "fallback"
                    }
                )
                
                # Wait for both operations to complete
                session_result, context_result = await asyncio.gather(
                    session_message_task, context_message_task, return_exceptions=True
                )
                
                # Log any failures but don't fail the request
                if isinstance(session_result, Exception):
                    logger.error(f"Session message save failed: {session_result}")
                    health_tracker.record_error("cosmos_db")
                
                if isinstance(context_result, Exception):
                    logger.error(f"Context message save failed: {context_result}")
                    health_tracker.record_error("context_service")
            
            logger.info(f"Chat session {session_id} initialized and stored in CosmosDB")
            
            return ChatResponse(
                session_id=session_id,
                response=welcome_message,
                metadata={
                    "step": "init",
                    "is_tcc_mode": False,
                    "scenario_matched": llm_scenario is not None,
                    "scenario_id": request.scenario_id,
                    "scenario_content": scenario_content,
                    "stored_in_cosmosdb": True,
                    "context_service_initialized": True,
                    "optimized": True
                }
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error initializing chat: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Chat initialization failed: {str(e)}")

@router.post("/message", 
             response_model=ChatResponse,
             summary="Send a message in an existing chat session")
async def send_message(request: ChatMessageRequest):
    """Send a message with optimized concurrent processing and error handling."""
    async with track_async("chat", "send_message", session_id=request.session_id):
        try:
            # Quick health checks
            for service in ["cosmos_db", "context_service", "llm_service"]:
                if not health_tracker.is_healthy(service):
                    raise HTTPException(
                        status_code=503, 
                        detail=f"{service.replace('_', ' ').title()} temporarily unavailable"
                    )
            
            # Import services
            chat_session_service, context_service = await safe_import_services()
            
            # Verify CosmosDB availability with timeout
            async with timeout_protection("CosmosDB verification", 3):
                if not chat_session_service.use_cosmos_db or not context_service.use_cosmos_db:
                    raise HTTPException(
                        status_code=503, 
                        detail="CosmosDB not available. Cannot process messages without database storage."
                    )
            
            # Get session and save user message concurrently
            async with timeout_protection("Session retrieval and message save", 8):
                # Start operations concurrently
                session_task = chat_session_service.get_session(request.session_id)
                user_message_task = chat_session_service.add_message_to_session(
                    session_id=request.session_id,
                    role="user",
                    content=request.message
                )
                context_message_task = context_service.add_message(
                    session_id=request.session_id,
                    content=request.message,
                    message_type="reaction",
                    step=None,
                    tokens=len(request.message.split()),
                    parameters={
                        "message_source": "user_input",
                        "session_message_id": f"user_{datetime.now().isoformat()}"
                    }
                )
                
                # Wait for all operations
                session, user_saved, context_saved = await asyncio.gather(
                    session_task, user_message_task, context_message_task, return_exceptions=True
                )
                
                # Check session retrieval
                if isinstance(session, Exception) or not session:
                    logger.error(f"Session retrieval failed: {session}")
                    health_tracker.record_error("cosmos_db")
                    raise HTTPException(status_code=404, detail="Chat session not found in database")
                
                # Log save failures but continue
                if isinstance(user_saved, Exception):
                    logger.error(f"User message save failed: {user_saved}")
                    health_tracker.record_error("cosmos_db")
                
                if isinstance(context_saved, Exception):
                    logger.error(f"Context message save failed: {context_saved}")
                    health_tracker.record_error("context_service")
            
            # Determine current step from session history
            current_step = "chat"
            messages = session.get("messages", [])
            if messages:
                for msg in reversed(messages):
                    if msg.get("role") == "assistant" and "metadata" in msg:
                        current_step = msg.get("metadata", {}).get("step", "chat")
                        break
            
            # Get conversation context and detect mode concurrently
            conversation_context = ""
            step = current_step
            
            async with timeout_protection("Context and mode detection", 10):
                try:
                    # Start both operations concurrently
                    context_task = asyncio.create_task(
                        context_service.get_formatted_context_for_llm(request.session_id)
                    )
                    mode_task = asyncio.create_task(
                        llm_tcc_service.detect_mode_by_llm(
                            message=request.message,
                            scenario=session["scenarioContent"],
                            current_step=current_step,
                            conversation_context=""  # Will be filled later if needed
                        )
                    )
                    
                    # Get context first (shorter timeout)
                    try:
                        conversation_context = await asyncio.wait_for(context_task, timeout=5)
                        logger.debug(f"Context retrieved ({len(conversation_context)} chars)")
                    except asyncio.TimeoutError:
                        logger.warning("Context retrieval timed out")
                        context_task.cancel()
                    
                    # Get mode detection (longer timeout)
                    try:
                        step = await asyncio.wait_for(mode_task, timeout=8)
                        logger.info(f"Mode detected: {step}")
                    except asyncio.TimeoutError:
                        logger.warning("Mode detection timed out, using current step")
                        mode_task.cancel()
                        step = current_step
                
                except Exception as e:
                    logger.warning(f"Context/mode detection failed: {str(e)}")
                    health_tracker.record_error("context_service")
            
            # Generate LLM response with timeout
            async with timeout_protection("LLM response generation", 30):
                parameters = {
                    "session_id": request.session_id,
                    "message_history": session.get("messages", []),
                    "detected_mode": step,
                    "scenario_id": session["scenarioId"],
                    "has_full_scenario": "Contenu:" in session["scenarioContent"],
                    "stored_in_cosmosdb": True,
                    "context_service_used": True,
                    "rich_context_used": len(conversation_context) > 0,
                    "context_length": len(conversation_context),
                    "optimized": True
                }
                
                try:
                    result = await llm_tcc_service.generate_response(
                        scenario=session["scenarioContent"],
                        reaction=request.message,
                        step=step,
                        session_id=request.session_id,
                        parameters=parameters
                    )
                except Exception as llm_error:
                    logger.error(f"LLM response generation failed: {str(llm_error)}")
                    health_tracker.record_error("llm_service")
                    raise HTTPException(status_code=500, detail="LLM service temporarily unavailable")
            
            # Add metadata to the response
            result["metadata"].update({
                "scenario_id": session["scenarioId"],
                "scenario_matched": "Contenu:" in session["scenarioContent"],
                "stored_in_cosmosdb": True,
                "context_service_used": True,
                "rich_context_used": len(conversation_context) > 0,
                "context_length": len(conversation_context),
                "optimized": True
            })
            
            # Save assistant response asynchronously (non-blocking)
            async def save_assistant_response():
                try:
                    await asyncio.gather(
                        chat_session_service.add_message_to_session(
                            session_id=request.session_id,
                            role="assistant",
                            content=result["response"],
                            metadata=result["metadata"]
                        ),
                        context_service.add_message(
                            session_id=request.session_id,
                            content=result["response"],
                            message_type="analysis",
                            step=step,
                            tokens=len(result["response"].split()),
                            parameters={
                                "message_source": "assistant_response",
                                "llm_model": result["metadata"].get("model"),
                                "processing_time_ms": result["metadata"].get("processing_time_ms"),
                                "rag_sources": result["metadata"].get("rag_sources", []),
                                "rich_context_length": len(conversation_context)
                            },
                            rag_sources=result["metadata"].get("rag_sources", [])
                        ),
                        return_exceptions=True
                    )
                except Exception as e:
                    logger.error(f"Failed to save assistant response: {str(e)}")
                    health_tracker.record_error("cosmos_db")
            
            # Start background save (don't wait)
            asyncio.create_task(save_assistant_response())
            
            logger.info(f"Message processed with rich context ({len(conversation_context)} chars) and stored in CosmosDB for session {request.session_id}")
            
            return ChatResponse(
                response=result["response"],
                metadata=result["metadata"],
                session_id=request.session_id
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Message processing failed: {str(e)}")

@router.get("/history/{session_id}")
async def get_chat_history(session_id: str):
    """Retrieve chat history from CosmosDB with timeout protection."""
    try:
        async with timeout_protection("Chat history retrieval", 10):
            chat_session_service, context_service = await safe_import_services()
            
            # Get session and context history concurrently
            session_task = chat_session_service.get_session(session_id)
            context_task = context_service.get_conversation_history(session_id)
            
            session_data, context_history = await asyncio.gather(
                session_task, context_task, return_exceptions=True
            )
            
            # Handle context history exceptions
            if isinstance(context_history, Exception):
                logger.warning(f"Could not retrieve context history from CosmosDB: {str(context_history)}")
                health_tracker.record_error("context_service")
                context_history = []
        
        return {
            "session_id": session_id,
            "session_found": session_data is not None and not isinstance(session_data, Exception),
            "session_data": session_data if not isinstance(session_data, Exception) else None,
            "context_history": context_history,
            "total_messages": len(session_data.get("messages", [])) if session_data and not isinstance(session_data, Exception) else 0,
            "context_messages": len(context_history),
            "storage_info": {
                "session_storage": "CosmosDB" if session_data and not isinstance(session_data, Exception) else "Not found",
                "context_storage": "CosmosDB Enhanced Context Service",
                "no_global_variables": True,
                "database_only": True,
                "optimized": True
            }
        }
        
    except Exception as e:
        logger.error(f"Error retrieving chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/session/{session_id}")
async def clear_chat_session(session_id: str):
    """Delete a chat session and all associated data from CosmosDB with timeout protection."""
    try:
        async with timeout_protection("Session deletion", 15):
            chat_session_service, context_service = await safe_import_services()
            
            session_data = await chat_session_service.get_session(session_id)
            user_id = session_data.get("scenarioId", "unknown") if session_data else "unknown"
            
            # Delete session and context concurrently
            session_task = chat_session_service.delete_session(session_id)
            context_task = context_service.clear_context(session_id=session_id)
            
            session_deleted, context_cleared = await asyncio.gather(
                session_task, context_task, return_exceptions=True
            )
            
            # Check results
            session_success = not isinstance(session_deleted, Exception) and session_deleted
            context_success = not isinstance(context_cleared, Exception)
            
            if isinstance(session_deleted, Exception):
                logger.error(f"Session deletion failed: {session_deleted}")
                health_tracker.record_error("cosmos_db")
            
            if isinstance(context_cleared, Exception):
                logger.warning(f"Could not clear context service data from CosmosDB: {str(context_cleared)}")
                health_tracker.record_error("context_service")
        
        return {
            "status": "success",
            "message": f"Session {session_id} cleared from database",
            "details": {
                "session_deleted": session_success,
                "context_cleared": context_success,
                "user_id": user_id,
                "storage_type": "CosmosDB",
                "no_global_variables_used": True,
                "optimized": True
            }
        }
        
    except Exception as e:
        logger.error(f"Error clearing chat session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sessions")
async def list_chat_sessions(limit: int = 10):
    """List recent chat sessions from CosmosDB with timeout protection."""
    try:
        async with timeout_protection("Sessions listing", 8):
            chat_session_service, _ = await safe_import_services()
            
            sessions = await chat_session_service.list_user_sessions(limit=limit)
        
        return {
            "status": "success",
            "sessions": sessions,
            "total_returned": len(sessions),
            "limit": limit,
            "storage_type": "CosmosDB",
            "data_source": "database_only",
            "optimized": True
        }
        
    except Exception as e:
        logger.error(f"Error listing chat sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/cleanup")
async def cleanup_old_sessions(max_age_days: int = 7):
    """Clean up old sessions from CosmosDB with timeout protection."""
    try:
        async with timeout_protection("Session cleanup", 30):
            chat_session_service, _ = await safe_import_services()
            
            deleted_count = await chat_session_service.cleanup_old_sessions(max_age_days=max_age_days)
        
        return {
            "status": "success",
            "message": f"Cleaned up {deleted_count} old sessions from database",
            "deleted_count": deleted_count,
            "max_age_days": max_age_days,
            "storage_type": "CosmosDB",
            "optimized": True
        }
        
    except Exception as e:
        logger.error(f"Error during session cleanup: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def get_service_health():
    """Get health status of all chat-related services."""
    return {
        "service_health": health_tracker.service_status,
        "optimizations_enabled": True,
        "status": "healthy",
        "version": "optimized"
    }