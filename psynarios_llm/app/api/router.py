"""
Router configuration to include the new test and fixed endpoints.
Update this in app/api/router.py
"""

from fastapi import APIRouter
from app.api.endpoints.workflow_test_endpoints import router as workflow_test_router
from app.api.endpoints.chat import router as chat_router

api_router = APIRouter()

api_router.include_router(workflow_test_router)
api_router.include_router(chat_router, prefix="/chat", tags=["chat"])
