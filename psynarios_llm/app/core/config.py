"""
Application configuration settings loaded from environment variables.
"""
from typing import List, Optional
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""
    
    # Project metadata
    PROJECT_NAME: str = "DeepSeek TCC Analysis Service"
    
    # API settings
    API_PREFIX: str = "/api"
    
    # PostgreSQL Configuration
    POSTGRES_HOST: str = "llm-project.postgres.database.azure.com"
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str = "llm_scenarios"
    POSTGRES_PORT: int = 5432


    # Azure DeepSeek LLM settings
    AZURE_ENDPOINT: str
    AZURE_API_KEY: str
    LLM_DEPLOYMENT_NAME: str
    API_VERSION: str = "2024-05-01-preview"
    
    # Azure Embeddings settings
    EMBED_ENDPOINT: str
    AZURE_OPENAI_API_KEY: str
    AZURE_OPENAI_API_VERSION: str
    AZURE_OPENAI_EMBEDDING_DEPLOYMENT: str
    
    # Azure AI Search settings
    AZURE_SEARCH_SERVICE: str = "new-vectorstore"
    AZURE_SEARCH_API_KEY: str
    AZURE_SEARCH_ENDPOINT: str = ""

    OPENAI_API_KEY: str 
    OPENAI_ENDPOINT:str
    OPENAI_MODEL: str  # or "gpt-4o" for higher quality
    OPENAI_MAX_TOKENS: int = 150
    OPENAI_TEMPERATURE: float = 0.0
    OPENAI_API_VERSION: str = "2025-01-01-preview"
    

    # Azure Cosmos DB settings for context service
    COSMOS_ENDPOINT: str = ""
    COSMOS_KEY: str = ""
    COSMOS_DATABASE: str = "ContextDatabase"
    ENABLE_CONTEXT_SERVICE: bool = True
    CONTEXT_MAX_RECENT_MESSAGES: int = 10
    CONTEXT_SUMMARIZATION_AGE_HOURS: int = 24
    AZURE_STORAGE_CONNECTION_STRING: Optional[str] = None
    
    # Optional: Additional LLM settings
    DEFAULT_MAX_TOKENS: int = 850
    DEFAULT_TEMPERATURE: float = 0.1
    DEFAULT_TOP_K: int = 3
    
    # Cache settings (optional)
    ENABLE_RESPONSE_CACHE: bool = False
    CACHE_TTL_SECONDS: int = 3600
    
    # Error tracking (optional)
    SENTRY_DSN: str = ""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.AZURE_SEARCH_ENDPOINT and self.AZURE_SEARCH_SERVICE:
            self.AZURE_SEARCH_ENDPOINT = f"https://{self.AZURE_SEARCH_SERVICE}.search.windows.net"
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
    )

settings = Settings()