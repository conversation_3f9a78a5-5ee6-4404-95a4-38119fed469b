"""
Database service for PostgreSQL connections with improved error handling and optimizations.
File: app/services/db_service.py - OPTIMIZED VERSION
"""

import logging
import psycopg2
from psycopg2 import pool
from psycopg2.extras import RealDictCursor
from functools import lru_cache
from typing import Optional, List, Dict, Any
import threading
import time

from app.core.config import settings
from app.monitoring.latency_tracker import track_sync

logger = logging.getLogger(__name__)

class DatabaseService:
    """PostgreSQL database service with connection pooling and optimization."""
    
    def __init__(self):
        """Initialize the database service with connection pooling."""
        # Use settings from config
        self.db_config = {
            "user": settings.POSTGRES_USER,
            "password": settings.POSTGRES_PASSWORD,
            "host": settings.POSTGRES_HOST,
            "port": settings.POSTGRES_PORT,
            "database": settings.POSTGRES_DB
        }
        
        # Create connection pool for better performance
        try:
            self.connection_pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=2,  # Minimum connections
                maxconn=10,  # Maximum connections
                **self.db_config
            )
            logger.info("Database connection pool created successfully")
            self.use_pool = True
        except Exception as e:
            logger.error(f"Failed to create connection pool: {str(e)}")
            self.connection_pool = None
            self.use_pool = False
        
        # In-memory cache for scenarios
        self._cache_lock = threading.Lock()
        self._scenario_cache = {}
        self._cache_timestamps = {}
        self.cache_ttl = 300  # 5 minutes TTL
        
        logger.info(f"Database service initialized for {self.db_config['host']}:{self.db_config['port']}")
    
    def get_db_connection(self):
        """Get a database connection with pooling support."""
        if self.use_pool and self.connection_pool:
            try:
                return self.connection_pool.getconn()
            except Exception as e:
                logger.error(f"Error getting connection from pool: {str(e)}")
                # Fallback to direct connection
                return psycopg2.connect(**self.db_config)
        else:
            return psycopg2.connect(**self.db_config)
    
    def return_connection(self, conn):
        """Return connection to pool."""
        if self.use_pool and self.connection_pool:
            try:
                self.connection_pool.putconn(conn)
            except Exception as e:
                logger.error(f"Error returning connection to pool: {str(e)}")
        else:
            conn.close()
    
    def _is_cache_valid(self, scenario_id: str) -> bool:
        """Check if cached scenario is still valid."""
        if scenario_id not in self._cache_timestamps:
            return False
        
        cache_time = self._cache_timestamps[scenario_id]
        return (time.time() - cache_time) < self.cache_ttl
    
    def test_database_connection(self) -> Dict[str, Any]:
        """Test the database connection and return status."""
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # Test basic connectivity
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            # Test table existence
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'psynarios_scenarios'
                )
            """)
            table_exists = cursor.fetchone()[0]
            
            # Get table stats if table exists
            stats = {}
            if table_exists:
                cursor.execute("SELECT COUNT(*) FROM psynarios_scenarios")
                stats["total_scenarios"] = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM psynarios_scenarios WHERE llm_scenario IS NOT NULL")
                stats["scenarios_with_content"] = cursor.fetchone()[0]
            
            cursor.close()
            self.return_connection(conn)
            
            return {
                "status": "success",
                "connectivity": True,
                "table_exists": table_exists,
                "stats": stats
            }
            
        except Exception as e:
            logger.error(f"Database connection test failed: {str(e)}")
            return {
                "status": "failed",
                "connectivity": False,
                "error": str(e)
            }
    
    def get_scenario_by_id(self, scenario_id) -> Optional[str]:
        """
        Get a scenario by ID with caching and connection pooling for performance.
        
        Args:
            scenario_id: The ID of the scenario to retrieve
        
        Returns:
            str: The scenario content or None if not found
        """
        with track_sync("database", "get_scenario", scenario_id=str(scenario_id)):
            # Handle None or empty scenario_id
            if scenario_id is None or scenario_id == "":
                logger.warning("Scenario ID is None or empty")
                return None
            
            # Check in-memory cache first
            with self._cache_lock:
                if scenario_id in self._scenario_cache and self._is_cache_valid(scenario_id):
                    logger.debug(f"Cache hit for scenario {scenario_id}")
                    return self._scenario_cache[scenario_id]
            
            conn = None
            try:
                logger.debug(f"Fetching scenario with ID: {scenario_id}")
                conn = self.get_db_connection()
                cursor = conn.cursor(cursor_factory=RealDictCursor)
                
                # Convert scenario_id to appropriate type
                try:
                    # If scenario_id is numeric string, convert to int
                    if isinstance(scenario_id, str) and scenario_id.isdigit():
                        scenario_id = int(scenario_id)
                    elif isinstance(scenario_id, str):
                        # If it's a string but not numeric, try to use it as is
                        logger.debug(f"Using scenario_id as string: {scenario_id}")
                    # If it's already an int, use it as is
                except (ValueError, TypeError) as e:
                    logger.error(f"Error converting scenario_id {scenario_id}: {str(e)}")
                    return None
                
                # Optimized query with LIMIT for faster execution
                cursor.execute(
                    "SELECT llm_scenario FROM psynarios_scenarios WHERE psynarios_scenario_id = %s LIMIT 1", 
                    (scenario_id,)
                )
                
                result = cursor.fetchone()
                cursor.close()
                
                scenario_content = result["llm_scenario"] if result else None
                
                # Update cache
                with self._cache_lock:
                    self._scenario_cache[str(scenario_id)] = scenario_content
                    self._cache_timestamps[str(scenario_id)] = time.time()
                
                if result:
                    logger.debug(f"Found scenario with ID: {scenario_id}")
                    return scenario_content
                else:
                    logger.warning(f"No scenario found with ID: {scenario_id}")
                    return None
                    
            except Exception as e:
                logger.error(f"Database error fetching scenario {scenario_id}: {str(e)}")
                return None
            finally:
                if conn:
                    self.return_connection(conn)
    
    def preload_scenarios(self, scenario_ids: List[str]):
        """Preload multiple scenarios into cache for better performance."""
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # Use IN clause for batch loading
            placeholders = ','.join(['%s'] * len(scenario_ids))
            cursor.execute(
                f"SELECT psynarios_scenario_id, llm_scenario FROM psynarios_scenarios WHERE psynarios_scenario_id IN ({placeholders})", 
                scenario_ids
            )
            
            results = cursor.fetchall()
            cursor.close()
            
            # Cache all results
            with self._cache_lock:
                current_time = time.time()
                for scenario_id, content in results:
                    self._scenario_cache[str(scenario_id)] = content
                    self._cache_timestamps[str(scenario_id)] = current_time
            
            logger.info(f"Preloaded {len(results)} scenarios into cache")
            
        except Exception as e:
            logger.error(f"Error preloading scenarios: {str(e)}")
        finally:
            if conn:
                self.return_connection(conn)
    
    def clear_cache(self):
        """Clear the scenario cache."""
        with self._cache_lock:
            self._scenario_cache.clear()
            self._cache_timestamps.clear()
        logger.info("Scenario cache cleared")
    
    def get_available_scenarios(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get a list of available scenarios with previews.
        
        Args:
            limit: Maximum number of scenarios to return
            
        Returns:
            List of scenario dictionaries with id and preview
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get scenarios with content
            cursor.execute("""
                SELECT 
                    psynarios_scenario_id,
                    LEFT(llm_scenario, 100) as preview,
                    CHAR_LENGTH(llm_scenario) as content_length
                FROM psynarios_scenarios 
                WHERE llm_scenario IS NOT NULL 
                    AND CHAR_LENGTH(llm_scenario) > 10
                ORDER BY psynarios_scenario_id 
                LIMIT %s
            """, (limit,))
            
            results = cursor.fetchall()
            cursor.close()
            
            scenarios = []
            for row in results:
                scenarios.append({
                    "psynarios_scenario_id": row["psynarios_scenario_id"],
                    "preview": row["preview"],
                    "content_length": row["content_length"]
                })
            
            logger.info(f"Retrieved {len(scenarios)} available scenarios")
            return scenarios
            
        except Exception as e:
            logger.error(f"Error retrieving available scenarios: {str(e)}")
            return []
        finally:
            if conn:
                self.return_connection(conn)
    
    def search_scenarios(self, search_term: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search scenarios by content.
        
        Args:
            search_term: Term to search for in scenario content
            limit: Maximum number of results
            
        Returns:
            List of matching scenarios
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # Search in scenario content
            cursor.execute("""
                SELECT 
                    psynarios_scenario_id,
                    LEFT(llm_scenario, 150) as preview,
                    CHAR_LENGTH(llm_scenario) as content_length
                FROM psynarios_scenarios 
                WHERE llm_scenario IS NOT NULL 
                    AND LOWER(llm_scenario) LIKE LOWER(%s)
                ORDER BY psynarios_scenario_id 
                LIMIT %s
            """, (f"%{search_term}%", limit))
            
            results = cursor.fetchall()
            cursor.close()
            
            scenarios = []
            for row in results:
                scenarios.append({
                    "psynarios_scenario_id": row["psynarios_scenario_id"],
                    "preview": row["preview"],
                    "content_length": row["content_length"],
                    "search_term": search_term
                })
            
            logger.info(f"Found {len(scenarios)} scenarios matching '{search_term}'")
            return scenarios
            
        except Exception as e:
            logger.error(f"Error searching scenarios: {str(e)}")
            return []
        finally:
            if conn:
                self.return_connection(conn)
    
    def get_scenario_stats(self) -> Dict[str, Any]:
        """
        Get statistics about scenarios in the database.
        
        Returns:
            Dictionary with scenario statistics
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # Get basic stats
            cursor.execute("SELECT COUNT(*) FROM psynarios_scenarios")
            total_scenarios = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM psynarios_scenarios WHERE llm_scenario IS NOT NULL")
            scenarios_with_content = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT AVG(CHAR_LENGTH(llm_scenario)) 
                FROM psynarios_scenarios 
                WHERE llm_scenario IS NOT NULL
            """)
            avg_length = cursor.fetchone()[0]
            
            cursor.execute("""
                SELECT MIN(psynarios_scenario_id), MAX(psynarios_scenario_id) 
                FROM psynarios_scenarios
            """)
            min_id, max_id = cursor.fetchone()
            
            cursor.close()
            
            return {
                "total_scenarios": total_scenarios,
                "scenarios_with_content": scenarios_with_content,
                "scenarios_without_content": total_scenarios - scenarios_with_content,
                "average_content_length": int(avg_length) if avg_length else 0,
                "id_range": {"min": min_id, "max": max_id}
            }
            
        except Exception as e:
            logger.error(f"Error getting scenario stats: {str(e)}")
            return {"error": str(e)}
        finally:
            if conn:
                self.return_connection(conn)

# Create singleton instance
db_service = DatabaseService()

# Legacy function for backward compatibility
def get_scenario_by_id(scenario_id) -> Optional[str]:
    """Legacy function for backward compatibility."""
    return db_service.get_scenario_by_id(scenario_id)

def get_db_connection():
    """Legacy function for backward compatibility."""
    return db_service.get_db_connection()