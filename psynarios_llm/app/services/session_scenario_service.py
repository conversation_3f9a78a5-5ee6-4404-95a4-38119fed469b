"""
Session Scenario Database Service
Manages the session_scenarios table to link sessions with scenarios.
File: app/services/session_scenario_service.py
"""

import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from typing import Optional, List, Dict, Any

from app.core.config import settings

logger = logging.getLogger(__name__)

class SessionScenarioService:
    """PostgreSQL service for managing session-scenario relationships."""
    
    def __init__(self):
        """Initialize the session scenario service."""
        self.db_config = {
            "user": settings.POSTGRES_USER,
            "password": settings.POSTGRES_PASSWORD,
            "host": settings.POSTGRES_HOST,
            "port": settings.POSTGRES_PORT,
            "database": settings.POSTGRES_DB
        }
        logger.info(f"Session Scenario service initialized for {self.db_config['host']}:{self.db_config['port']}")
    
    def get_db_connection(self):
        """Get a database connection with error handling."""
        try:
            conn = psycopg2.connect(**self.db_config)
            return conn
        except Exception as e:
            logger.error(f"Error connecting to PostgreSQL: {str(e)}")
            raise
    
    def create_session_scenario_link(self, session_id: str, scenario_id: str) -> bool:
        """
        Create a link between session and scenario in the session_scenarios table.
        
        Args:
            session_id: Unique session identifier
            scenario_id: ID of the scenario (foreign key to psynarios_scenarios.id)
            
        Returns:
            True if successful, False otherwise
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # First, validate that the scenario exists
            cursor.execute("SELECT id FROM psynarios_scenarios WHERE id = %s", (scenario_id,))
            if not cursor.fetchone():
                logger.error(f"Scenario with id {scenario_id} does not exist in psynarios_scenarios table")
                return False
            
            # Insert the session-scenario link
            cursor.execute("""
                INSERT INTO session_scenarios (session_id, scenario_id) 
                VALUES (%s, %s)
                ON CONFLICT (session_id) DO UPDATE SET
                    scenario_id = EXCLUDED.scenario_id
            """, (session_id, scenario_id))
            
            conn.commit()
            cursor.close()
            
            logger.info(f"Created session-scenario link: {session_id} -> {scenario_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error creating session-scenario link for {session_id}: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()
    
    def get_scenario_for_session(self, session_id: str) -> Optional[str]:
        """
        Get the scenario ID for a given session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Scenario ID as string or None if not found
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT scenario_id 
                FROM session_scenarios 
                WHERE session_id = %s
            """, (session_id,))
            
            result = cursor.fetchone()
            cursor.close()
            
            if result:
                scenario_id = str(result[0])
                logger.info(f"Found scenario {scenario_id} for session {session_id}")
                return scenario_id
            
            logger.warning(f"No scenario found for session {session_id}")
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving scenario for session {session_id}: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()
    
    def get_sessions_for_scenario(self, scenario_id: str) -> List[str]:
        """
        Get all session IDs that use a specific scenario.
        
        Args:
            scenario_id: Scenario identifier
            
        Returns:
            List of session IDs
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT session_id 
                FROM session_scenarios 
                WHERE scenario_id = %s
                ORDER BY session_id
            """, (scenario_id,))
            
            results = cursor.fetchall()
            cursor.close()
            
            session_ids = [row[0] for row in results]
            logger.info(f"Found {len(session_ids)} sessions for scenario {scenario_id}")
            return session_ids
            
        except Exception as e:
            logger.error(f"Error retrieving sessions for scenario {scenario_id}: {str(e)}")
            return []
        finally:
            if conn:
                conn.close()
    
    def delete_session_scenario_link(self, session_id: str) -> bool:
        """
        Delete a session-scenario link.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if successful, False otherwise
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM session_scenarios WHERE session_id = %s", (session_id,))
            rows_affected = cursor.rowcount
            
            conn.commit()
            cursor.close()
            
            if rows_affected > 0:
                logger.info(f"Deleted session-scenario link for {session_id}")
                return True
            else:
                logger.warning(f"No session-scenario link found for {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting session-scenario link for {session_id}: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()
    
    def get_all_session_scenarios(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get all session-scenario links with scenario details.
        
        Args:
            limit: Maximum number of records to return
            
        Returns:
            List of dictionaries with session and scenario information
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            cursor.execute("""
                SELECT 
                    ss.session_id,
                    ss.scenario_id,
                    ps.llm_scenario,
                    CASE 
                        WHEN LENGTH(ps.llm_scenario) > 100 
                        THEN LEFT(ps.llm_scenario, 100) || '...'
                        ELSE ps.llm_scenario
                    END as scenario_preview
                FROM session_scenarios ss
                LEFT JOIN psynarios_scenarios ps ON ss.scenario_id = ps.id
                ORDER BY ss.session_id
                LIMIT %s
            """, (limit,))
            
            results = cursor.fetchall()
            cursor.close()
            
            # Convert to list of dictionaries
            session_scenarios = [dict(row) for row in results]
            
            logger.info(f"Retrieved {len(session_scenarios)} session-scenario links")
            return session_scenarios
            
        except Exception as e:
            logger.error(f"Error retrieving session-scenario links: {str(e)}")
            return []
        finally:
            if conn:
                conn.close()
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about session-scenario relationships.
        
        Returns:
            Dictionary with statistics
        """
        conn = None
        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # Get basic counts
            cursor.execute("SELECT COUNT(*) FROM session_scenarios")
            total_links = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT scenario_id) FROM session_scenarios")
            unique_scenarios = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT session_id) FROM session_scenarios")
            unique_sessions = cursor.fetchone()[0]
            
            # Get most used scenarios
            cursor.execute("""
                SELECT 
                    ss.scenario_id,
                    COUNT(*) as session_count,
                    LEFT(ps.llm_scenario, 50) as scenario_preview
                FROM session_scenarios ss
                LEFT JOIN psynarios_scenarios ps ON ss.scenario_id = ps.id
                GROUP BY ss.scenario_id, ps.llm_scenario
                ORDER BY session_count DESC
                LIMIT 5
            """)
            
            top_scenarios = [
                {
                    "scenario_id": row[0],
                    "session_count": row[1],
                    "scenario_preview": row[2]
                }
                for row in cursor.fetchall()
            ]
            
            cursor.close()
            
            stats = {
                "total_session_scenario_links": total_links,
                "unique_scenarios_used": unique_scenarios,
                "unique_sessions": unique_sessions,
                "top_scenarios": top_scenarios
            }
            
            logger.info("Retrieved session-scenario statistics")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting session-scenario statistics: {str(e)}")
            return {"error": str(e)}
        finally:
            if conn:
                conn.close()

# Create singleton instance
session_scenario_service = SessionScenarioService()