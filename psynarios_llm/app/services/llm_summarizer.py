"""
LLM-based message summarization service with Azure OpenAI integration.
This version implements an ADVANCED HYBRID summarization strategy (Extraction + Abstraction)
optimized for TCC/ACT context, returning structured summaries.
"""

import logging
import asyncio
import re
import random
import time
from typing_extensions import Optional, Dict, Any, List, TypedDict
from datetime import datetime
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

# --- New Structured Output --- 
class HybridSummaryOutput(TypedDict):
    narrative_summary: str
    key_insights: List[str]

class LLMSummarizer:
    """Service for generating structured hybrid summaries using Azure OpenAI."""

    def __init__(self, max_retries=2, timeout_seconds=45): # Increased timeout for 2 steps
        """
        Initialize the LLM summarizer with Azure OpenAI client.
        
        Args:
            max_retries: Number of retries for each summarization step
            timeout_seconds: Timeout for combined API calls
        """
        from app.core.config import settings
        
        self.max_retries = max_retries
        self.timeout_seconds = timeout_seconds
        
        # Get credentials from settings
        self.azure_endpoint = settings.OPENAI_ENDPOINT
        self.api_key = settings.OPENAI_API_KEY
        self.api_version = settings.OPENAI_API_VERSION
        self.model = settings.OPENAI_MODEL
        self.max_narrative_summary_length = 250  # Increased slightly for narrative
        self.max_key_insights = 5 # Limit number of insights
        
        # Validate required settings
        if not self.api_key:
            logger.error("OPENAI_API_KEY not configured")
            raise ValueError("OPENAI_API_KEY not configured - summarization unavailable")
            
        if not self.azure_endpoint:
            logger.error("OPENAI_ENDPOINT not configured")
            raise ValueError("OPENAI_ENDPOINT not configured - summarization unavailable")
        
        if not self.model:
            logger.error("OPENAI_MODEL not configured")
            raise ValueError("OPENAI_MODEL not configured - summarization unavailable")
        
        # Initialize the Azure OpenAI client
        try:
            from openai import AsyncAzureOpenAI
            self.client = AsyncAzureOpenAI(
                azure_endpoint=self.azure_endpoint,
                api_key=self.api_key,
                api_version=self.api_version
            )
            
            logger.info(f"LLM Summarizer (Hybrid) initialized with Azure OpenAI endpoint {self.azure_endpoint}")
            logger.info(f"Using model: {self.model}, API version: {self.api_version}")
        except Exception as e:
            logger.error(f"Failed to initialize Azure OpenAI client: {str(e)}", exc_info=True)
            raise RuntimeError(f"LLM Summarizer initialization failed: {str(e)}")

    async def summarize_message_hybrid(self, message) -> HybridSummaryOutput:
        """
        Generate a structured hybrid summary (narrative + key insights) using a two-step LLM process.
        
        Args:
            message: Message object with content and metadata
            
        Returns:
            HybridSummaryOutput dictionary
        """
        if not message or not hasattr(message, 'content') or not message.content:
            raise ValueError("Cannot summarize empty message")
            
        message_id = message.id if hasattr(message, 'id') else "unknown"
        content = message.content
        message_type = getattr(message.metadata, 'message_type', 'unknown')
        step = getattr(message.metadata, 'step', None)

        logger.info(f"Starting HYBRID summarization for message ID: {message_id}, type: {message_type}, step: {step}")
        logger.info(f"Original content length: {len(content)} characters")

        total_start_time = time.time()
        key_insights = []
        narrative_summary = ""

        try:
            # --- Step 1: Extract Key Insights --- 
            logger.info(f"[Hybrid Step 1] Extracting key insights for message {message_id}")
            extraction_prompt = self._create_extraction_prompt(content, message_type, step)
            extracted_text = await self._call_llm_with_retry(
                prompt=extraction_prompt,
                system_message=self._get_extraction_system_message(),
                max_tokens=300, # Allow more tokens for insights list
                temperature=0.1 # Low temp for factual extraction
            )
            key_insights = self._parse_key_insights(extracted_text)
            logger.info(f"[Hybrid Step 1] Extracted {len(key_insights)} insights for message {message_id}")

            # --- Step 2: Generate Narrative Summary (using insights) --- 
            logger.info(f"[Hybrid Step 2] Generating narrative summary for message {message_id}")
            abstraction_prompt = self._create_abstraction_prompt(content, key_insights, message_type, step)
            narrative_summary = await self._call_llm_with_retry(
                prompt=abstraction_prompt,
                system_message=self._get_abstraction_system_message(),
                max_tokens=200, # Max tokens for the narrative summary itself
                temperature=0.3 # Slightly higher temp for narrative generation
            )
            narrative_summary = self._clean_narrative_summary(narrative_summary)
            logger.info(f"[Hybrid Step 2] Generated narrative summary (len: {len(narrative_summary)}) for message {message_id}")

            total_elapsed = time.time() - total_start_time
            logger.info(f"Hybrid summarization complete for {message_id} in {total_elapsed:.2f}s")

            return {
                "narrative_summary": narrative_summary,
                "key_insights": key_insights[:self.max_key_insights] # Limit insights count
            }

        except Exception as e:
            logger.error(f"Hybrid summarization failed for message {message_id}: {str(e)}", exc_info=True)
            # Fallback: return empty structure or potentially a simple summary
            return {
                "narrative_summary": f"Erreur de résumé: {str(e)}",
                "key_insights": []
            }

    # --- Prompt Creation --- 

    def _create_extraction_prompt(self, content: str, message_type: str, step: Optional[str]) -> str:
        """
        Create prompt for extracting key TCC/ACT insights.
        """
        # Tailor instructions based on message type/step if needed
        tcc_context_hint = ""
        if message_type == 'reaction':
            tcc_context_hint = "Focus on expressed emotions, core beliefs, behavioral urges, and key user statements."
        elif message_type == 'analysis':
            tcc_context_hint = f"Focus on the core findings of the TCC analysis (step: {step or 'general'}), such as identified patterns, impacts, sources, or proposed strategies."
        
        prompt = f"""
        Analysez le texte suivant et extrayez les points clés les plus importants dans le contexte d'une conversation TCC/ACT. 
        {tcc_context_hint}

        INSTRUCTIONS CRITIQUES:
        1. Identifiez 3 à 5 éléments essentiels MAXIMUM (croyances, émotions clés, stratégies, faits saillants, décisions).
        2. Formulez chaque point clé comme une affirmation courte et directe.
        3. Utilisez UNIQUEMENT des termes neutres et factuels.
        4. NE PAS inclure d'introduction, de conclusion ou de commentaires.
        5. Listez chaque point clé sur une nouvelle ligne, commençant par un tiret (-).
        6. Soyez extrêmement concis.

        TEXTE À ANALYSER:
        {content}

        POINTS CLÉS (format: - Point clé 1\n- Point clé 2\n...):
        """
        return prompt

    def _get_extraction_system_message(self) -> str:
        return """
        Vous êtes un expert en TCC/ACT chargé d'extraire les informations factuelles clés d'un texte.
        Votre unique tâche est de lister les points essentiels demandés, sans ajout.
        Format de sortie: Liste de points clés commençant par '-', un par ligne.
        """

    def _create_abstraction_prompt(self, content: str, key_insights: List[str], message_type: str, step: Optional[str]) -> str:
        """
        Create prompt for generating narrative summary incorporating key insights.
        """
        insights_str = "\n".join(f"- {insight}" for insight in key_insights)
        if not insights_str:
            insights_str = "(Aucun point clé spécifique extrait)"

        prompt = f"""
        Rédigez un résumé narratif très concis (1-2 phrases MAXIMUM) du texte suivant, en vous assurant d'intégrer l'essence des points clés fournis.

        CONTEXTE:
        - Type de message: {message_type}
        - Étape TCC (si applicable): {step or 'N/A'}

        POINTS CLÉS À INTÉGRER:
        {insights_str}

        TEXTE ORIGINAL COMPLET (pour référence):
        {content}

        INSTRUCTIONS CRITIQUES:
        1. Produisez un résumé fluide et cohérent en 1 ou 2 phrases.
        2. Le résumé DOIT refléter l'idée principale du texte ET l'essence des points clés.
        3. Utilisez un langage neutre et objectif.
        4. NE PAS lister les points clés à nouveau.
        5. Soyez extrêmement concis (max {self.max_narrative_summary_length} caractères).
        6. NE PAS ajouter d'introduction, de conclusion ou de commentaires.

        RÉSUMÉ NARRATIF (1-2 phrases):
        """
        return prompt

    def _get_abstraction_system_message(self) -> str:
        return """
        Vous êtes un rédacteur expert chargé de créer des résumés narratifs ultra-concis.
        Votre réponse doit être UNIQUEMENT le résumé demandé, en 1-2 phrases maximum, intégrant les points clés fournis.
        Pas de texte superflu.
        """

    # --- LLM Call & Parsing --- 

    async def _call_llm_with_retry(self, prompt: str, system_message: str, max_tokens: int, temperature: float) -> str:
        """
        Call Azure OpenAI with retry logic and exponential backoff.
        """
        last_exception = None
        for attempt in range(self.max_retries + 1):
            try:
                logger.debug(f"LLM call attempt {attempt+1}/{self.max_retries+1}")
                start_time = time.time()
                
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": system_message},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=max_tokens,
                    temperature=temperature,
                    top_p=0.9,
                    frequency_penalty=0.0,
                    presence_penalty=0.0
                )
                
                elapsed = time.time() - start_time
                logger.debug(f"LLM call completed in {elapsed:.2f}s")

                if not response or not response.choices or not response.choices[0].message:
                    raise ValueError("Invalid response structure from Azure OpenAI")
                
                result_text = response.choices[0].message.content.strip()
                if not result_text:
                    raise ValueError("Empty response content from LLM")
                
                # Log token usage if available
                if hasattr(response, 'usage') and response.usage:
                    logger.debug(f"Token usage: input={response.usage.prompt_tokens}, output={response.usage.completion_tokens}")
                
                return result_text

            except Exception as e:
                logger.warning(f"LLM call attempt {attempt+1} failed: {str(e)}")
                last_exception = e
                if attempt < self.max_retries:
                    backoff_time = min(15, (2 ** attempt) + (random.random() * 0.5))
                    logger.info(f"Retrying LLM call in {backoff_time:.2f}s")
                    await asyncio.sleep(backoff_time)
                else:
                    logger.error(f"All {self.max_retries+1} LLM call attempts failed.")
                    raise RuntimeError(f"Failed to get valid response from LLM after multiple retries: {last_exception}") from last_exception
        
        # Should not be reached if exception handling is correct
        raise RuntimeError("LLM call failed unexpectedly.")

    def _parse_key_insights(self, extracted_text: str) -> List[str]:
        """
        Parse the LLM output for key insights (lines starting with '-').
        """
        insights = []
        lines = extracted_text.strip().split('\n')
        for line in lines:
            cleaned_line = line.strip()
            if cleaned_line.startswith('-'):
                insight = cleaned_line[1:].strip()
                if insight: # Avoid empty insights
                    insights.append(insight)
        
        if not insights and len(lines) == 1 and not lines[0].startswith('-'):
             # Handle case where LLM might return a single insight without the dash
             single_insight = lines[0].strip()
             if len(single_insight) > 5: # Basic check for meaningful content
                 logger.warning("LLM extraction returned single line without dash, treating as insight.")
                 insights.append(single_insight)

        return insights

    def _clean_narrative_summary(self, summary_text: str) -> str:
        """
        Clean the generated narrative summary.
        """
        # Remove potential leading/trailing quotes or labels
        summary = re.sub(r'^["\`]*(?:Résumé narratif:|Summary:)?\s*', '', summary_text, flags=re.IGNORECASE)
        summary = re.sub(r'["\`]*$', '', summary)
        summary = summary.strip()

        # Truncate if necessary (should be handled by max_tokens, but as fallback)
        if len(summary) > self.max_narrative_summary_length:
            logger.warning(f"Narrative summary exceeded length ({len(summary)}), truncating.")
            summary = summary[:self.max_narrative_summary_length-3] + "..."
            
        return summary

# --- Keep old summarizer for compatibility if needed, or remove --- 
# (Code for the old `summarize_message` can be kept here temporarily or removed)
# ... (old code omitted for brevity in this example) ...


