"""
Enhanced RAG Service using Azure AI Search with shutdown fixes and async improvements.
"""

import asyncio
import logging
import time
import hashlib
import random
from typing import Dict, Any, Optional, List, Tuple
from functools import lru_cache

from app.monitoring.latency_tracker import track_sync
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.prompts import Cha<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>plate
from langchain_core.runnables import RunnablePassthrough, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_community.vectorstores import AzureSearch
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

from azure.search.documents import SearchClient
from azure.search.documents.models import QueryType
from azure.core.credentials import AzureKeyCredential
from app.core.config import settings

logger = logging.getLogger(__name__)

class LangChainRAGService:
    """RAG service using Azure AI Search with improved shutdown handling."""
    
    def __init__(self):
        """Initialize the RAG service with Azure Search."""
        self.search_endpoint = settings.AZURE_SEARCH_ENDPOINT
        self.search_api_key = settings.AZURE_SEARCH_API_KEY
        self.embed_endpoint = settings.EMBED_ENDPOINT
        self.embed_api_key = settings.AZURE_OPENAI_API_KEY
        self.deployment = settings.AZURE_OPENAI_EMBEDDING_DEPLOYMENT
        
        # Add shutdown tracking
        self._shutdown = False
        self._operation_timeout = 10  # Default timeout for operations
        
        # Track if vector search is available
        self.vector_search_available = False
        
        # List of indexes to search across with weights
        self.index_configs = {
            "echelle-impacts-index-fr": {
                "weight": 1.0,
                "semantic_config": "echelle-semantic-config"
            },
            "emotions-index-fr": {
                "weight": 1.2,
                "semantic_config": "emotion-semantic-config"
            },
            "impacts-index-fr1": {
                "weight": 1.0,
                "semantic_config": "impacts-semantic-config"
            },
            "schemas-cognitifs-2-index-fr": {
                "weight": 1.1,
                "semantic_config": "schemas-cognitifs-2-semantic-configuration"
            },
            "schemas-cognitifs-1-index-fr": {
                "weight": 1.1,
                "semantic_config": "schemas-cognitifs-1-semantic-configuration"
            },
            "comportements-index-fr": {
                "weight": 1.3,
                "semantic_config": "semantic-comportement"
            },
            "comportement-nuances-index-fr": {
                "weight": 1.2,
                "semantic_config": "nuances-semantic-configuration"
            }
        }
        
        # The name of your vector field in the indexes
        self.vector_field_name = "embeddings"
        
        # The name of your content field for text search
        self.content_field = "content"
        
        # Step-specific index weighting adjustments
        self.step_index_weights = {
            "analyze": {
                "comportements-index-fr": 1.5,
                "schemas-cognitifs-1-index-fr": 1.2
            },
            "impacts": {
                "impacts-index-fr1": 1.6,
                "echelle-impacts-index-fr": 1.4
            },
            "sources": {
                "schemas-cognitifs-2-index-fr": 1.5,
                "schemas-cognitifs-1-index-fr": 1.4
            },
            "strategies": {
                "comportement-nuances-index-fr": 1.5,
                "emotions-index-fr": 1.3
            }
        }
        
        # Initialize Azure Search clients
        self.search_clients = {}
        self.vector_stores = {}
        
        # Circuit breaker for error handling
        self._error_count = 0
        self._max_errors = 5
        self._last_error_time = 0
        self._error_reset_interval = 300  # 5 minutes
        
        for index_name in self.index_configs.keys():
            try:
                # Initialize search client for direct keyword searches
                self.search_clients[index_name] = SearchClient(
                    endpoint=self.search_endpoint,
                    index_name=index_name,
                    credential=AzureKeyCredential(self.search_api_key)
                )
                
                logger.info(f"Successfully initialized search client for index: {index_name}")
                
            except Exception as e:
                logger.error(f"Error initializing search client for index '{index_name}': {str(e)}")
                continue
        
        # Try to set up vector search if possible
        try:
            from openai import AzureOpenAI
            
            self.azure_client = AzureOpenAI(
                api_key=self.embed_api_key,
                api_version=settings.AZURE_OPENAI_API_VERSION,
                azure_endpoint=self.embed_endpoint
            )
            
            # Test embedding generation with timeout
            test_query = "Test query"
            test_embedding = self._generate_embeddings_sync(test_query)
            
            if test_embedding and len(test_embedding) > 0:
                self.vector_search_available = True
                logger.info(f"Vector search enabled: {len(test_embedding)} embedding dimensions")
                
                # Initialize vector stores
                for index_name in self.index_configs.keys():
                    try:
                        self.vector_stores[index_name] = AzureSearch(
                            azure_search_endpoint=self.search_endpoint,
                            azure_search_key=self.search_api_key,
                            index_name=index_name,
                            embedding_function=self._generate_embeddings_sync,
                            semantic_configuration_name=self.index_configs[index_name]["semantic_config"],
                            fields={"embeddings": self.vector_field_name}
                        )
                        logger.info(f"Successfully initialized vector store for index: {index_name}")
                    except Exception as e:
                        logger.error(f"Error initializing vector store for index '{index_name}': {str(e)}")
                        continue
                
        except Exception as e:
            logger.warning(f"Vector search not available. Using keyword search only: {str(e)}")
            self.vector_search_available = False
        
        # Track usage metrics
        self.metrics = {
            "total_queries": 0,
            "cache_hits": 0,
            "errors": 0,
            "vector_searches": 0,
            "keyword_searches": 0,
            "latency_ms": []
        }
        
        logger.info(f"RAG service initialized with {len(self.index_configs)} indexes")
        logger.info(f"Vector search available: {self.vector_search_available}")
    
    def _check_shutdown(self):
        """Check if service has been shutdown."""
        if self._shutdown:
            raise RuntimeError("RAG service has been shutdown")
    
    def _is_circuit_breaker_open(self):
        """Check if circuit breaker is open due to errors."""
        current_time = time.time()
        
        # Reset error count if enough time has passed
        if (current_time - self._last_error_time) > self._error_reset_interval:
            self._error_count = 0
        
        return self._error_count >= self._max_errors
    
    def _record_error(self):
        """Record an error for circuit breaker."""
        self._error_count += 1
        self._last_error_time = time.time()
        logger.warning(f"RAG error count: {self._error_count}/{self._max_errors}")
    
    def _generate_embeddings_sync(self, text: str) -> List[float]:
        """
        Generate embeddings using Azure OpenAI client with error handling (synchronous).
        """
        try:
            self._check_shutdown()
            
            if not self.vector_search_available:
                return None
            
            if self._is_circuit_breaker_open():
                logger.warning("RAG circuit breaker open, skipping embeddings")
                return None
                
            response = self.azure_client.embeddings.create(
                model=self.deployment,
                input=text
            )
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {str(e)}")
            self._record_error()
            return None
    
    async def generate_embeddings(self, text: str) -> List[float]:
        """
        Generate embeddings using Azure OpenAI client with error handling (async wrapper).
        """
        try:
            self._check_shutdown()
            
            if not self.vector_search_available:
                return None
            
            if self._is_circuit_breaker_open():
                logger.warning("RAG circuit breaker open, skipping embeddings")
                return None
            
            # Use asyncio.to_thread for the synchronous call
            result = await asyncio.wait_for(
                asyncio.to_thread(self._generate_embeddings_sync, text),
                timeout=5  # 5 second timeout for embeddings
            )
            return result
            
        except asyncio.TimeoutError:
            logger.error("Embedding generation timed out")
            self._record_error()
            return None
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {str(e)}")
            self._record_error()
            return None
    
    def _generate_cache_key(self, query: str, step: str, top_k: int) -> str:
        """Generate a cache key for the given query parameters."""
        key_components = f"{query}|{step}|{top_k}|{self.vector_search_available}"
        return hashlib.md5(key_components.encode()).hexdigest()
    
    def _enrich_query_for_step(self, query: str, step: str) -> str:
        """Enrich the query with step-specific prefixes."""
        step_prefixes = {
            "analyze": "Explication et classification du comportement :",
            "impacts": "Impacts professionnels, émotionnels et fonctionnels à court et long terme :",
            "sources": "Croyances fondamentales, facteurs déclencheurs et facteurs de maintien :",
            "strategies": "Stratégies de régulation en contexte professionnel :"
        }
        
        prefix = step_prefixes.get(step, "")
        return f"{prefix} {query}".strip()
    
    def _get_current_weights(self, step: str) -> Dict[str, float]:
        """Get the current index weights for a specific step."""
        current_weights = {}
        for index_name, config in self.index_configs.items():
            weight = config["weight"]
            
            if step in self.step_index_weights and index_name in self.step_index_weights[step]:
                weight = self.step_index_weights[step][index_name]
                
            current_weights[index_name] = weight
        
        return current_weights
    
    def _perform_keyword_search(self, query: str, index_name: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Perform keyword search using Azure Search with timeout protection."""
        try:
            self._check_shutdown()
            
            if self._is_circuit_breaker_open():
                logger.warning("RAG circuit breaker open, skipping keyword search")
                return []
            
            search_client = self.search_clients[index_name]
            semantic_config = self.index_configs[index_name].get("semantic_config")
            
            # Execute search with timeout (synchronous, but fast)
            if semantic_config:
                search_results = search_client.search(
                    search_text=query,
                    top=top_k,
                    query_type=QueryType.SEMANTIC,
                    semantic_configuration_name=semantic_config,
                    query_caption="extractive",
                    query_answer="none"
                )
            else:
                search_results = search_client.search(
                    search_text=query,
                    top=top_k
                )
            
            # Convert to standardized format
            results = []
            for doc in search_results:
                result = {
                    "id": doc.get("id", doc.get("name", "N/A")),
                    "index": index_name,
                    "@search.score": doc.get("@search.score", 0.5),
                    "content": doc.get(self.content_field, "")
                }
                
                # Add all other fields from the document
                for key, value in doc.items():
                    if key not in ["id", "@search.score", self.vector_field_name] and key not in result:
                        result[key] = value
                
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error during keyword search for index '{index_name}': {str(e)}")
            self._record_error()
            return []
    
    def _convert_langchain_results(self, docs_with_scores: List[Tuple[Document, float]], 
                                 index_name: str, weight: float) -> List[Dict[str, Any]]:
        """Convert LangChain document results to our standardized format."""
        results = []
        
        try:
            self._check_shutdown()
            
            for doc, score in docs_with_scores:
                weighted_score = score * weight
                doc_metadata = doc.metadata.copy()
                
                result = {
                    "id": doc_metadata.get("id", doc_metadata.get("name", "N/A")),
                    "index": index_name,
                    "@search.score": weighted_score,
                    "weight_applied": weight,
                    "content": doc.page_content
                }
                
                for key, value in doc_metadata.items():
                    if key not in ["id"]:
                        result[key] = value
                
                results.append(result)
        
        except Exception as e:
            logger.error(f"Error converting LangChain results: {str(e)}")
            self._record_error()
        
        return results
    
    def retrieve(self, query: str, step: str, top_k: int) -> List[Dict[str, Any]]:
        """
        Synchronous retrieval function with improved error handling.
        """
        try:
            self._check_shutdown()
            
            if self._is_circuit_breaker_open():
                logger.warning("RAG circuit breaker open, returning empty results")
                return []
            
            # Update metrics
            self.metrics["total_queries"] += 1
            
            # Enrich query with step-specific prefix
            enriched_query = self._enrich_query_for_step(query, step)
            logger.debug(f"Step: {step}, Enriched Query: {enriched_query}")
            
            # Get current index weights for this step
            current_weights = self._get_current_weights(step)
            
            all_results = []
            
            # Try vector search first if available
            if self.vector_search_available and self.vector_stores:
                try:
                    logger.debug(f"Attempting vector search across {len(self.vector_stores)} indexes...")
                    self.metrics["vector_searches"] += 1
                    
                    for index_name, vector_store in self.vector_stores.items():
                        try:
                            weight = current_weights.get(index_name, 1.0)
                            
                            # Perform hybrid search with timeout protection
                            docs = vector_store.similarity_search_with_relevance_scores(
                                query=enriched_query,
                                k=top_k,
                                search_type="hybrid",
                            )
                            
                            results = self._convert_langchain_results(docs, index_name, weight)
                            all_results.extend(results)
                            
                        except Exception as e:
                            logger.error(f"Vector search failed for index '{index_name}': {str(e)}")
                            self._record_error()
                            
                            # Fall back to keyword search for this index
                            keyword_results = self._perform_keyword_search(enriched_query, index_name, top_k)
                            for result in keyword_results:
                                result["weight_applied"] = weight
                                result["@search.score"] = result["@search.score"] * weight
                            all_results.extend(keyword_results)
                    
                    if all_results:
                        logger.debug(f"Vector search successful, retrieved {len(all_results)} results")
                        all_results.sort(key=lambda x: x.get("@search.score", 0), reverse=True)
                        return all_results[:top_k]
                
                except Exception as e:
                    logger.error(f"All vector searches failed, falling back to keyword search: {str(e)}")
                    self._record_error()
                    all_results = []
            
            # Use keyword search as fallback
            logger.debug("Using keyword search...")
            self.metrics["keyword_searches"] += 1
            
            for index_name in self.search_clients.keys():
                weight = current_weights.get(index_name, 1.0)
                keyword_results = self._perform_keyword_search(enriched_query, index_name, top_k)
                
                for result in keyword_results:
                    result["weight_applied"] = weight
                    result["@search.score"] = result.get("@search.score", 0.5) * weight
                
                all_results.extend(keyword_results)
            
            # Sort and return top results
            all_results.sort(key=lambda x: x.get("@search.score", 0), reverse=True)
            return all_results[:top_k]
            
        except Exception as e:
            logger.error(f"RAG retrieval failed: {str(e)}")
            self._record_error()
            return []
    
    async def retrieve_async(self, query: str, step: str, top_k: int) -> List[Dict[str, Any]]:
        """
        Async wrapper for retrieval with timeout protection.
        """
        try:
            self._check_shutdown()
            
            # Use asyncio.to_thread with timeout for the synchronous retrieve method
            result = await asyncio.wait_for(
                asyncio.to_thread(self.retrieve, query, step, top_k),
                timeout=self._operation_timeout
            )
            return result
            
        except asyncio.TimeoutError:
            logger.error(f"RAG retrieval timed out after {self._operation_timeout}s")
            self._record_error()
            return []
        except Exception as e:
            logger.error(f"Async RAG retrieval failed: {str(e)}")
            self._record_error()
            return []
    
    def get_context_and_rag_metadata(self, query: str, step: str, top_k: int = 3) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Retrieve context and metadata from knowledge base with improved error handling.
        """
        try:
            with track_sync("rag", "retrieve", step=step, query_length=len(query)):
                self._check_shutdown()
                
                if self._is_circuit_breaker_open():
                    logger.warning("RAG circuit breaker open, returning empty context")
                    return "", []
                
                # Retrieve documents
                retrieved_docs = self.retrieve(query, step, top_k)
                
                context_blocks = []
                rag_metadata = []
                
                for doc in retrieved_docs:
                    index_name = doc.get("index", "N/A")
                    doc_id = doc.get("id", "N/A")
                    score = doc.get("@search.score", 0)
                    weight = doc.get("weight_applied", 1.0)
                    
                    # Exclude metadata fields for the content block
                    content_fields = {k: v for k, v in doc.items() 
                                    if k not in ["id", "index", "@search.score", "weight_applied", self.vector_field_name]}
                    
                    # Format content with field names
                    content_block = "\n".join([f"- {k.capitalize()}: {v}" for k, v in content_fields.items()])
                    
                    # Format context block with metadata
                    context_blocks.append(f"[Document {len(context_blocks)+1}] {index_name} | ID: {doc_id} | Score: {round(score, 2)} | Weight: {weight}\n{content_block}")
                    
                    # Add metadata for response
                    rag_metadata.append({
                        "RAG_Index": index_name,
                        "RAG_ID": doc_id,
                        "RAG_Score": round(score, 2),
                        "RAG_Weight": weight,
                        "RAG_Fields": ", ".join(content_fields.keys())
                    })
                
                # Format context string with clear section markers
                context_str = "\n\n".join(context_blocks)
                
                # Add a summary of retrieval metrics
                metrics_summary = {
                    "query_count": self.metrics["total_queries"],
                    "cache_hits": self.metrics["cache_hits"],
                    "avg_latency_ms": sum(self.metrics["latency_ms"][-10:]) / min(len(self.metrics["latency_ms"]), 10) if self.metrics["latency_ms"] else 0,
                    "error_count": self.metrics["errors"],
                    "result_count": len(retrieved_docs)
                }
                
                rag_metadata.append({"RAG_Metrics": metrics_summary})
                
                return context_str, rag_metadata
        
        except Exception as e:
            logger.error(f"Error in get_context_and_rag_metadata: {str(e)}")
            self._record_error()
            return "", []
    
    async def get_context_and_rag_metadata_async(self, query: str, step: str, top_k: int = 3) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Async version of get_context_and_rag_metadata with timeout protection.
        """
        try:
            self._check_shutdown()
            
            # Use asyncio.to_thread with timeout
            result = await asyncio.wait_for(
                asyncio.to_thread(self.get_context_and_rag_metadata, query, step, top_k),
                timeout=self._operation_timeout
            )
            return result
            
        except asyncio.TimeoutError:
            logger.error(f"RAG context retrieval timed out after {self._operation_timeout}s")
            self._record_error()
            return "", []
        except Exception as e:
            logger.error(f"Async RAG context retrieval failed: {str(e)}")
            self._record_error()
            return "", []
    
    def clear_cache(self):
        """Clear any internal caches."""
        # Remove any cached data
        self.metrics["cache_hits"] = 0
        logger.info("RAG service cache cleared")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get the current RAG service metrics."""
        return {
            "total_queries": self.metrics["total_queries"],
            "cache_hits": self.metrics["cache_hits"],
            "cache_hit_rate": self.metrics["cache_hits"] / max(1, self.metrics["total_queries"]),
            "error_count": self.metrics["errors"],
            "avg_latency_ms": sum(self.metrics["latency_ms"]) / max(1, len(self.metrics["latency_ms"])),
            "p95_latency_ms": sorted(self.metrics["latency_ms"])[int(len(self.metrics["latency_ms"]) * 0.95)] if self.metrics["latency_ms"] else 0,
            "index_count": len(self.index_configs),
            "vector_search_available": self.vector_search_available,
            "circuit_breaker_open": self._is_circuit_breaker_open(),
            "error_count_current": self._error_count
        }
    
    def shutdown(self):
        """Shutdown the RAG service."""
        self._shutdown = True
        logger.info("RAG service shutdown initiated")

# Create a singleton instance
rag_service = LangChainRAGService()