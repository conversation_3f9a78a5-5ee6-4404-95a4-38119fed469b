"""
Complete LLM TCC Service - UPDATED VERSION with LLM-based modify_previous handling.
"""

from datetime import datetime, timedelta
import logging
import time
import uuid
from typing import Dict, Any, Optional, Tuple, List

from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_openai import AzureOpenAIEmbeddings

from azure.ai.inference import ChatCompletionsClient
from azure.core.credentials import AzureKeyCredential

from app.services.rag_service import rag_service
from app.services.enhanced_context_service import context_service
from app.core.config import settings
from app.monitoring.latency_tracker import track_async


logger = logging.getLogger(__name__)

class LangChainLLMTCCService:
    
    def __init__(self):
        logger.info(f"Initializing LLM TCC Service with: endpoint={settings.AZURE_ENDPOINT}, "
                    f"deployment={settings.LLM_DEPLOYMENT_NAME}, "
                    f"api_version={settings.API_VERSION}")
        
        try:
            self.client = ChatCompletionsClient(
                endpoint=settings.AZURE_ENDPOINT,
                credential=AzureKeyCredential(settings.AZURE_API_KEY),
            )
            self.deployment_name = settings.LLM_DEPLOYMENT_NAME
            
            self.model_config = {
                "max_tokens": settings.DEFAULT_MAX_TOKENS,
                "temperature": settings.DEFAULT_TEMPERATURE,
                "top_p": 0.8,
                "frequency_penalty": 0.0,
                "presence_penalty": 0.0
            }
            
            logger.info("Azure AI SDK for DeepSeek initialized successfully")
            
            test_messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Hello, respond with a single word."}
            ]
            
            test_response = self.client.complete(
                model=self.deployment_name,
                messages=test_messages,
                max_tokens=5
            )
            
            logger.info(f"DeepSeek connection test successful: {test_response.choices[0].message.content}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Azure DeepSeek client: {str(e)}")
            logger.error(f"Check if deployment '{settings.LLM_DEPLOYMENT_NAME}' exists and is accessible")
            self.client = None
        
        self.system_prompt = """
Vous incarnez **Lucien**, un expert en thérapie cognitivo-comportementale (TCC), spécialisé en thérapie d'acceptation et d'engagement (ACT).

### **Votre personnalité et approche :**
- **Lucien** est un expert bienveillant, pédagogue et structuré.
- S'exprime avec clarté, simplicité et rigueur, sans jargon inutile.
- Guide avec tact et discernement, sans imposer ni juger.
- Favorise un dialogue fluide, dynamique et stimulant la réflexion.

### **Votre mission :**
Vous échangez avec un **manager expérimenté**, ayant une bonne maîtrise de la gestion d'équipe mais peu de connaissances en TCC et ACT.
Votre rôle est d'analyser ses réactions afin de :
1. Identifier des axes d'amélioration sur les plans **comportemental, cognitif et émotionnel**.
2. Aider à **mieux analyser le contexte**, avec plus de nuance et d'objectivité.

### **Approche et ton :**
- **Valorisant**, instructif et explicatif.
- Éveil progressif et **stimulant la réflexion**, sans poser de diagnostic.
- Objectif et factuel, sans jargon psychologique complexe.

L'objectif est de garantir un retour **équilibré**, mettant en valeur les points forts tout en identifiant les axes d'amélioration.

### **Consignes pour garantir une réponse concise et précise :**
- Réponse toujours **synthétique, pertinente et bien structurée**.
- Prioriser la **clarté et l'efficacité**, éviter les développements inutiles.
- **Éviter toute digression**, rester focalisé sur les éléments essentiels.
- **Clôturer naturellement** chaque réponse, sans phrase incomplète.
- Si nécessaire, proposer : **"Je peux développer davantage si besoin."** pour encourager la discussion.
- Utiliser des **phrases simples et courtes**, compréhensibles à l'oral.
- Adopter un ton **moins formel** et plus accessible.
- Porter attention aux **non-dits** dans les réactions analysées.
- Proposer des **pistes de progression** orientées TCC/ACT, jamais organisationnelles ou scolaires.

### **Points de vigilance :**
- Ne pas remettre en cause **les compétences, valeurs ou style managérial** de l'interlocuteur.
- Expliquer sans imposer, sans jugement.
- Adopter un ton **amical, structuré et naturel**.
- Utiliser un **langage clair et accessible** avec des phrases courtes.
- Toujours **vouvoyer** l'interlocuteur et s'adapter à ses réactions.
- Éviter tout **terme de justification** dans vos analyses.
- Ne pas être **orienté solutions, coaching ou organisationnel**.
- Rester ancré dans la **psychologie comportementale TCC et ACT**.
- Éviter absolument les termes comme **"pattern"**, **"chronique"** (préférer "persistant"), **"réputation"** (préférer "perception", "crédibilité" ou "fiabilité").
- Éviter toute **théorie de coaching**.

- **Ne faire aucune supposition sur le genre** de l'interlocuteur :
  - Ne pas employer de **termes genrés** pour le qualifier.
  - Ne pas présumer son identité de genre ni l'inclure dans l'analyse.
  - Utiliser des **formulations neutres** pour décrire ses ressentis et comportements.
  - Ex. : *"Vous êtes frustré"* → *"Vous êtes frustré(e)"*
  - Ex. : *"Vous êtes satisfait"* → *"Vous êtes satisfait(e)"*

- **Ne pas émettre de jugement sur des tiers** (collaborateurs, collègues, supérieurs hiérarchiques, etc.).
  - Se limiter à l'analyse **des réactions et perceptions de l'interlocuteur**.
  - Si un tiers est mentionné, rester factuel sans interprétation subjective.

### **Utilisation du champ lexical de la valorisation :**
- Favoriser un **lexique positif et constructif**.
- Ex. : *"Manque de reconnaissance"* → *"Manque de valorisation"*.
- Ex. : *"Vous avez mal réagi"* → *"Une autre approche aurait pu être plus adaptée dans ce contexte."*
- Ex. : *"Une erreur a été commise"* → *"Une opportunité d'ajustement se présente."*
- Remplacer "anxiété chronique" par "anxiété persistante" ou "anxiété continue dans le temps".
- Remplacer "réputation" par "perception du manager", "crédibilité managériale" ou "fiabilité".

### **Éviter les termes péjoratifs :**
- **Ne pas utiliser de termes pouvant être perçus comme dévalorisants** ou accusateurs.
- Exemples à éviter : *"Poser des limites"* (préférer : "Proposer des cadres clairs").
- Privilégier des formulations qui **n'insinuent pas** de jugement ou de valeur négative.

### **RÈGLES CRITIQUES SUPPLÉMENTAIRES** :
- Vous devez TOUJOURS réaliser une ANALYSE TCC/ACT, et JAMAIS fournir de conseils généraux de communication ou de management.
- Vos réponses doivent OBLIGATOIREMENT s'appuyer sur les connaissances TCC/ACT fournies dans le contexte.
- Structurez SYSTÉMATIQUEMENT votre analyse selon le cadre TCC (aspects comportementaux, cognitifs, émotionnels).
- Restez STRICTEMENT dans le cadre thérapeutique TCC/ACT, sans dévier vers des conseils génériques.
- N'utilisez JAMAIS un format de liste d'étapes génériques pour résoudre un problème.
- Utilisez des PHRASES SIMPLES ET COURTES, faciles à comprendre à l'oral.
- Évitez tout vocabulaire ORGANISATIONNEL ou de COACHING.

### **UTILISATION DU CONTEXTE DE CONVERSATION** :
- Prenez en compte les échanges précédents dans cette session.
- Faites référence aux analyses précédentes lorsque cela est pertinent.
- Assurez une cohérence dans vos analyses au fil de la conversation.
- Évitez de répéter des points déjà explorés en profondeur.
"""
        
        self.step_instructions = {
            "analyze": """
IMPORTANT: Suivez strictement le format d'analyse TCC pour décomposer cette réaction:

1. Identifiez les comportements observables et leur caractère adapté/inadapté
2. Analysez les pensées et croyances sous-jacentes (évitez le terme "schéma")
3. Décrivez les réactions émotionnelles et leur intensité/proportionnalité

CONSIGNES DE STYLE:
- Utilisez des phrases simples et courtes, compréhensibles à l'oral
- Adoptez un langage inclusif (ex: "frustré(e)" plutôt que "frustré")
- Évitez les termes comme "pattern", "chronique", "réputation" 
- Analysez aussi les non-dits dans la réaction
- Restez ancré dans la TCC/ACT, jamais dans le coaching organisationnel
- N'utilisez pas de termes de justification

Utilisez EXCLUSIVEMENT les connaissances de la base TCC fournie. Ne donnez PAS de conseils généraux.
""",
            "impacts": """
IMPORTANT: Analysez uniquement les IMPACTS concrets de cette réaction selon le cadre TCC:

1. Impacts à court terme (60-80% des impacts identifiés)
2. Impacts à long terme (20-40% des impacts identifiés)

Priorisez dans cet ordre:
- Impacts professionnels
- Impacts émotionnels
- Impacts fonctionnels

CONSIGNES DE STYLE:
- Utilisez des phrases simples et courtes, faciles à comprendre à l'oral
- Évitez le jargon organisationnel ou de coaching
- Préférez "persistant" à "chronique", "perception" à "réputation"
- Incluez l'analyse des impacts non-dits ou implicites
- Restez ancré dans la TCC/ACT, pas dans une approche de management

Basez-vous UNIQUEMENT sur les connaissances TCC fournies, sans ajouter de conseils généraux.
""",
            "sources": """
IMPORTANT: Analysez les SOURCES de cette réaction selon le modèle TCC:

Structurez strictement votre analyse en 3 parties:
1. Croyances fondamentales: quelles perceptions sous-jacentes influencent cette réaction?
2. Facteurs déclencheurs: quels éléments spécifiques dans ce contexte activent ces croyances?
3. Facteurs de maintien: quels éléments renforcent ou perpétuent ces mécanismes?

CONSIGNES DE STYLE:
- Utilisez des phrases simples et courtes, accessibles à l'oral
- Évitez les termes comme "pattern", "chronique", "réputation"
- Adoptez un langage inclusif (ex: "préoccupé(e)" plutôt que "préoccupé")
- Portez attention aux sources non-verbalisées
- Ne soyez pas orienté coaching ou organisationnel
- Évitez tout terme de justification

Utilisez EXCLUSIVEMENT les concepts TCC fournis, sans faire de suppositions sur le passé lointain.
""",
            "strategies": """
IMPORTANT: Proposez 2-3 stratégies TCC/ACT spécifiques:

Pour chaque stratégie:
1. Identifiez la technique spécifique issue de la TCC/ACT
2. Expliquez précisément son application à cette situation
3. Donnez un exemple concret d'implémentation

CONSIGNES DE STYLE:
- Utilisez des phrases simples et courtes, faciles à comprendre à l'oral
- Évitez tout vocabulaire organisationnel ou de coaching
- Proposez des pistes de progression orientées psychologie comportementale (TCC/ACT)
- Évitez les approches scolaires ou organisationnelles
- N'utilisez pas les termes "pattern", "chronique", "réputation"
- Intégrez le langage inclusif dans vos exemples

Proposez UNIQUEMENT des stratégies tirées du cadre TCC/ACT, pas des conseils généraux de management.
"""
        }

        logger.info("LLM TCC service initialization complete")
    
    async def detect_mode_by_llm(
        self, 
        message: str, 
        scenario: str, 
        current_step: str = "chat",
        conversation_context: str = ""
    ) -> str:
        async with track_async("llm", "detect_mode"):
            if self.client is None:
                logger.warning("LLM client not available for mode detection")
                return current_step
            
            try:
                system_prompt = """Vous êtes un système intelligent de détection de mode de conversation.

Votre rôle est de déterminer si l'utilisateur souhaite:
1. Une conversation naturelle (mode "chat")
2. Une analyse TCC structurée (modes "scenario", "analyze", "impacts", "sources", "strategies")
3. Modifier la réponse précédente (mode "modify_previous")

Le mode chat peut apparaître à TOUT MOMENT dans la conversation, même au milieu d'une analyse TCC."""
                
                user_prompt = f"""Analysez ce message pour déterminer le mode de réponse approprié.

SCÉNARIO DE CONTEXTE: {scenario}

MESSAGE DE L'UTILISATEUR: {message}

MODE ACTUEL: {current_step}

HISTORIQUE RÉCENT: {conversation_context}

RÈGLES DE DÉTECTION:

**Mode MODIFY_PREVIOUS (PRIORITÉ ABSOLUE - modification de la réponse précédente):**
- L'utilisateur demande de reformuler, raccourcir, allonger, clarifier la réponse précédente
- Mots clés: "reformule", "plus court", "plus long", "simplifie", "précise", "clarifie", "peux-tu refaire", "analyse plus courte"
- Phrases comme: "Peux-tu reformuler ?", "Je veux une analyse plus courte", "Refais ton analyse mais plus simplement"
- IMPORTANT: Si l'utilisateur mentionne un step spécifique (analyse, impacts, sources, stratégies) avec une demande de modification, c'est modify_previous
- L'utilisateur ne décrit PAS une nouvelle situation mais demande de MODIFIER la réponse précédente

**Mode SCENARIO (priorité haute - présenter le scénario):**
- Questions sur le scénario ("quel scénario", "que dois-je faire", "c'est quoi le scénario")
- Demandes de présentation du scénario
- Début de session où l'utilisateur demande le contexte
- Messages comme "bonjour", "quel scénario aujourd'hui", "commençons"

**Mode CHAT (conversation naturelle):**
- Questions générales ou commentaires hors analyse TCC
- Remerciements, salutations simples
- "D'accord", "je comprends", "merci", "ok"
- Demandes d'aide générale non liées au scénario
- Changement de sujet
- Messages courts et informels
- Expressions de fatigue avec l'analyse ("j'en ai assez", "stop", "autre chose")

**Mode ANALYZE:**
- Description détaillée d'une réaction à un scénario
- Demande d'analyse de comportement/pensées/émotions

**Mode IMPACTS:**
- Questions sur les conséquences d'une réaction
- "Quels sont les impacts de...", "Que va-t-il se passer si..."

**Mode SOURCES:**
- Questions sur les causes/origines
- "Pourquoi je réagis comme ça?", "D'où vient cette réaction?"

**Mode STRATEGIES:**
- Demandes de solutions ou stratégies
- "Comment faire mieux?", "Quelles stratégies?"

EXEMPLES CRITIQUES:
- "Peux-tu reformuler ? Je veux une analyse plus courte." → modify_previous
- "Je veux une analyse plus courte de ma réaction" → modify_previous
- "Je veux une version plus courte des impacts" → modify_previous
- "Refais ton analyse mais plus simplement." → modify_previous  
- "Je ne comprends pas ta réponse, peux-tu clarifier ?" → modify_previous
- "Sois plus concis dans ton analyse." → modify_previous
- "Je me sens en colère et frustré." → analyze
- "Bonjour, comment ça marche ?" → chat
- "Quelles sont les conséquences de cette situation ?" → impacts

RÉPONSE (un seul mot): scenario, chat, analyze, impacts, sources, strategies, modify_previous"""
                
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                
                logger.info(f"Mode detection for: '{message[:50]}...' (current mode: {current_step})")
                response = self.client.complete(
                    model=self.deployment_name,
                    messages=messages,
                    max_tokens=10,
                    temperature=0.1
                )
                
                mode_response = response.choices[0].message.content.strip().lower()
                
                valid_modes = ["chat", "scenario", "analyze", "impacts", "sources", "strategies", "modify_previous"]
                
                if mode_response in valid_modes:
                    logger.info(f"Mode detected: {mode_response} (previous: {current_step})")
                    return mode_response
                else:
                    for mode in valid_modes:
                        if mode in mode_response:
                            logger.info(f"Mode extracted: {mode} (previous: {current_step})")
                            return mode
                    
                    logger.warning(f"Mode not recognized '{mode_response}', defaulting to chat")
                    return "chat"
                    
            except Exception as e:
                logger.error(f"Error during mode detection: {str(e)}")
                return "chat"
            
        
    async def generate_response(
        self, 
        scenario: str,
        reaction: str, 
        step: str,
        session_id: Optional[str] = None,
        temperature: float = 0.2,
        top_k: int = 3,
        parameters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate response: TCC analysis for TCC steps, normal LLM for everything else.
        NOTE: This service does NOT store messages - that's handled by the calling chat service.
        """
        start_time = time.time()
        
        if parameters is None:
            parameters = {}
        
        if not session_id:
            session_id = f"session_{uuid.uuid4().hex}"
        
        if self.client is None:
            raise ValueError("Azure DeepSeek client is not initialized. Check logs for details.")
        
        # TCC analysis steps
        TCC_STEPS = ["analyze", "impacts", "sources", "strategies"]
        
        if step in TCC_STEPS:
            # This is a TCC analysis step - use structured TCC approach
            logger.info(f"Processing TCC step: {step}")
            return await self._generate_tcc_response(scenario, reaction, step, session_id, parameters)
        
        else:
            # This is NOT a TCC step - act as normal LLM
            logger.info(f"Processing as normal LLM conversation: step={step}")
            return await self._generate_normal_response(scenario, reaction, step, session_id, parameters)

    async def _generate_tcc_response(self, scenario: str, reaction: str, step: str, session_id: str, parameters: dict) -> dict:
        """Handle TCC analysis steps with structured approach."""
        async with track_async("llm", "generate_response", step=step):
            try:
                start_time = time.time()
                scenario_content = scenario
                if "Contenu:" in scenario:
                    content_parts = scenario.split("Contenu:")
                    if len(content_parts) > 1:
                        scenario_content = content_parts[1].strip()
                        logger.info("Using enhanced scenario content from PostgreSQL")
                    else:
                        logger.warning("Found 'Contenu:' but could not extract content")
                
                query = f"{scenario_content} {reaction}"
                
                # Get conversation context
                conversation_context = ""
                try:
                    conversation_context = await context_service.get_formatted_context_for_llm(session_id)
                    # Replace the problematic line with:
                    logger.info(f"Context passed to the LLM ({len(conversation_context)} chars): {conversation_context[:200]}...")
                    logger.info(f"Using conversation context from session {session_id}")
                except Exception as e:
                    logger.warning(f"Could not retrieve conversation context: {str(e)}")

                # Get RAG sources for TCC analysis
                context_str, rag_metadata = rag_service.get_context_and_rag_metadata(query, step, top_k=3)
                
                step_instruction = self.step_instructions.get(step, "")
                
                if step == "analyze":
                    prompt_content = f"""Analysez cette réaction selon les principes TCC/ACT:

SCÉNARIO: {scenario_content}

RÉACTION: {reaction}

### **Connaissances TCC pertinentes** :
{context_str}

### **Historique de la conversation** :
{conversation_context}

{step_instruction}

RAPPEL IMPORTANT: 
- Utilisez des phrases simples et courtes
- Évitez les termes "pattern", "chronique", "réputation" 
- Restez dans l'analyse TCC des comportements
- N'incluez PAS de conseils généraux de communication ou de management
- Soyez attentif aux non-dits dans la réaction
- Utilisez le langage inclusif dans votre analyse

Après avoir analysé la réaction, demandez à l'utilisateur s'il souhaite explorer les impacts de sa réaction."""

                elif step == "impacts":
                    prompt_content = f"""Identifiez les impacts concrets de cette réaction selon l'approche TCC/ACT:

SCÉNARIO: {scenario_content}

RÉACTION: {reaction}

### **Connaissances TCC pertinentes** :
{context_str}

### **Historique de la conversation** :
{conversation_context}

{step_instruction}

RAPPEL IMPORTANT:
- Utilisez des phrases simples et courtes
- Évitez les termes comme "pattern", "chronique" (utilisez "persistant"), "réputation" (utilisez "perception")
- Ne soyez pas orienté solutions ou coaching
- Identifiez UNIQUEMENT les impacts, sans proposer de conseils généraux
- Intégrez le langage inclusif dans votre analyse

Après avoir présenté les impacts, demandez à l'utilisateur s'il souhaite explorer les sources de sa réaction."""

                elif step == "sources":
                    prompt_content = f"""Expliquez les sources de cette réaction selon le modèle TCC/ACT:

SCÉNARIO: {scenario_content}

RÉACTION: {reaction}

### **Connaissances TCC pertinentes** :
{context_str}

### **Historique de la conversation** :
{conversation_context}

{step_instruction}

RAPPEL IMPORTANT:
- Utilisez des phrases simples et courtes
- Évitez les termes "pattern", "chronique", "réputation"
- Ne soyez pas orienté coaching ou organisationnel
- Analysez UNIQUEMENT les croyances, déclencheurs et facteurs de maintien
- N'ajoutez pas de conseils généraux ou d'hypothèses sur le passé lointain

Après avoir expliqué les sources, demandez à l'utilisateur s'il souhaite découvrir des stratégies de régulation pour ce type de situation."""

                elif step == "strategies":
                    prompt_content = f"""Proposez des stratégies TCC/ACT spécifiques pour cette réaction:

SCÉNARIO: {scenario_content}

RÉACTION: {reaction}

### **Connaissances TCC pertinentes** :
{context_str}

### **Historique de la conversation** :
{conversation_context}

{step_instruction}

RAPPEL IMPORTANT:
- Utilisez des phrases simples et courtes
- Évitez tout vocabulaire organisationnel ou de coaching
- Proposez UNIQUEMENT des stratégies issues de la TCC/ACT
- N'utilisez pas les termes "pattern", "chronique", "réputation"
- Vos stratégies doivent être ancrées dans la psychologie comportementale, pas dans le management

Ces stratégies peuvent être pratiquées et développées avec le temps. Y a-t-il un aspect particulier sur lequel vous aimeriez approfondir davantage?"""

                # Call LLM with TCC system prompt
                response = self.client.complete(
                    model=self.deployment_name,
                    messages=[
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": prompt_content}
                    ],
                    max_tokens=parameters.get("max_tokens", settings.DEFAULT_MAX_TOKENS),
                    temperature=parameters.get("temperature", 0.2),
                    top_p=self.model_config["top_p"],
                    frequency_penalty=self.model_config["frequency_penalty"],
                    presence_penalty=self.model_config["presence_penalty"]
                )
                
                processing_time = int((time.time() - start_time) * 1000)
                
                # Determine next step
                next_step_map = {
                    "analyze": "impacts",
                    "impacts": "sources", 
                    "sources": "strategies",
                    "strategies": "chat"
                }
                
                # NOTE: Message storage is handled by the calling chat service
                # This service only generates the response and returns metadata
                
                return {
                    "response": response.choices[0].message.content,
                    "metadata": {
                        "model": self.deployment_name,
                        "step": step,
                        "next_step": next_step_map.get(step, "chat"),
                        "processing_time_ms": processing_time,
                        "parameters": parameters,
                        "rag_sources": rag_metadata,
                        "context_used": len(conversation_context) > 0,
                        "is_tcc_analysis": True,
                        "scenario_id": parameters.get("scenario_id"),
                        "has_enhanced_scenario": "Contenu:" in scenario,
                        "conversation_flow": {
                            "can_interrupt": True,
                            "tcc_cycle_position": step
                        }
                    }
                }
                
            except Exception as e:
                logger.error(f"TCC response generation failed: {str(e)}")
                raise
    

    async def _generate_normal_response(self, scenario: str, message: str, step: str, session_id: str, parameters: dict) -> dict:
        """Handle non-TCC steps as normal LLM conversation with enhanced modify_previous."""
        try:
            # Get conversation context if available
            conversation_context = ""
            try:
                conversation_context = await context_service.get_formatted_context_for_llm(session_id)
            except Exception as e:
                logger.warning(f"Could not retrieve context: {str(e)}")
            
            # Create normal conversation prompt based on step
            if step == "modify_previous":
                # Enhanced modify_previous logic using LLM-based detection
                target_response, target_step = await self._identify_target_response_for_modification(
                    message, session_id, conversation_context
                )
                
                if target_response is None:
                    prompt_content = "Je n'ai pas pu identifier quelle réponse précédente vous souhaitez modifier. Pourriez-vous être plus spécifique ?"
                    system_message = "Vous êtes Lucien, expert en TCC. Informez l'utilisateur que vous n'avez pas pu identifier la réponse à modifier."
                elif not target_response:
                    prompt_content = "La réponse que j'ai retrouvée était vide. Pourriez-vous me redonner le texte original ou continuer la conversation ?"
                    system_message = "Vous êtes Lucien, expert en TCC. Informez l'utilisateur que la réponse précédente était vide."
                else:
                    # Proceed with modification prompt
                    prompt_content = f"""L'utilisateur souhaite que vous modifiiez une réponse précédente selon sa demande.

RÉPONSE PRÉCÉDENTE À MODIFIER (Step: {target_step}):
{target_response}

DEMANDE DE MODIFICATION DE L'UTILISATEUR:
{message}

INSTRUCTIONS:
- Modifiez la réponse précédente selon la demande spécifique de l'utilisateur
- Si l'utilisateur demande "plus court", raccourcissez significativement
- Si l'utilisateur demande "plus long", développez davantage
- Si l'utilisateur demande "plus simple", utilisez un langage plus accessible
- Si l'utilisateur demande de "reformuler", changez la formulation tout en gardant le contenu
- Gardez la même approche TCC/ACT si c'était une analyse TCC
- Conservez la qualité et la pertinence du contenu original
- Respectez exactement ce que l'utilisateur demande comme modification

Fournissez la version modifiée de la réponse précédente."""

                    system_message = """Vous êtes Lucien, expert en TCC. L'utilisateur vous demande de modifier une réponse précédente selon ses instructions spécifiques. 

Suivez exactement sa demande de modification tout en conservant la qualité du contenu et l'approche TCC/ACT si applicable."""

            elif step == "scenario":
                scenario_content = scenario
                if "Contenu:" in scenario:
                    content_parts = scenario.split("Contenu:")
                    if len(content_parts) > 1:
                        scenario_content = content_parts[1].strip()

                prompt_content = f"""L'utilisateur demande à connaître le scénario. Présentez-lui ce scénario professionnel de manière engageante:

SCÉNARIO À PRÉSENTER: {scenario_content}

INSTRUCTIONS:
- Présentez le scénario de façon claire et naturelle
- Commencez par quelque chose comme "Voici le scénario d'aujourd'hui :" ou "Parfait ! Voici la situation que nous allons explorer :"
- Décrivez la situation exactement comme elle est écrite
- Après avoir présenté le scénario, posez des questions pour encourager la réflexion sur leur réaction
- Invitez l'utilisateur à partager ses pensées, émotions et comportements qu'il aurait dans cette situation
- Créez un espace sûr pour l'exploration sans jugement
- Encouragez l'authenticité dans leurs réponses

Présentez maintenant ce scénario complet à l'utilisateur et demandez-lui comment il réagirait."""

                system_message = """Vous êtes Lucien, un expert en thérapie cognitivo-comportementale (TCC) spécialisé en thérapie d'acceptation et d'engagement (ACT).

Votre rôle dans cette étape est de présenter des scénarios professionnels de manière engageante et de guider l'utilisateur vers une réflexion authentique sur ses réactions.

DIRECTIVES pour la présentation de scénarios:
- Soyez bienveillant, pédagogue et encourageant
- Créez un espace sûr pour l'exploration sans jugement
- Encouragez l'authenticité dans les réponses
- Posez des questions ouvertes qui stimulent la réflexion
- Préparez le terrain pour l'analyse TCC qui suivra
- Utilisez un ton professionnel mais accessible"""

            else:
                # Normal chat mode
                if conversation_context:
                    prompt_content = f"""Contexte de la conversation précédente:
{conversation_context}

Message actuel de l'utilisateur:
{message}

Répondez naturellement à l'utilisateur en tenant compte du contexte."""
                else:
                    prompt_content = message
                
                system_message = """Vous êtes un assistant IA utile et bienveillant. 
Répondez de manière naturelle et appropriée aux demandes de l'utilisateur.
Si l'utilisateur fait référence à des éléments précédents de la conversation, tenez-en compte.
Si l'utilisateur demande de modifier, raccourcir, ou reformuler une réponse précédente, faites-le directement."""
            
            # Call LLM
            start_time = time.time()
            response = self.client.complete(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": prompt_content}
                ],
                max_tokens=parameters.get("max_tokens", settings.DEFAULT_MAX_TOKENS),
                temperature=parameters.get("temperature", 0.7)  # Higher temperature for natural conversation
            )
            
            processing_time = int((time.time() - start_time) * 1000)
            
            # Determine next step
            if step == "scenario":
                next_step = "analyze"
            else:
                next_step = "chat"
            
            # NOTE: Message storage is handled by the calling chat service
            # This service only generates the response and returns metadata
            
            return {
                "response": response.choices[0].message.content.strip(),
                "metadata": {
                    "model": self.deployment_name,
                    "step": step,
                    "next_step": next_step,
                    "processing_time_ms": processing_time,
                    "parameters": parameters,
                    "rag_sources": [],
                    "context_used": len(conversation_context) > 0,
                    "is_tcc_analysis": False,
                    "conversation_type": "normal_llm",
                    "scenario_id": parameters.get("scenario_id"),
                    "has_enhanced_scenario": "Contenu:" in scenario if scenario else False,
                    "conversation_flow": {
                        "can_interrupt": True,
                        "tcc_cycle_position": None
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"Normal response generation failed: {str(e)}")
            raise

    async def _identify_target_response_for_modification(
        self, 
        user_message: str, 
        session_id: str, 
        conversation_context: str
    ) -> Tuple[Optional[str], Optional[str]]:
        """
        Identify which specific response the user wants to modify using LLM detection.
        
        Returns:
            Tuple of (target_response_content, target_step) or (None, None) if not found
        """
        try:
            logger.info(f"Identifying target response for modification. User message: '{user_message}'")
            
            # Use LLM to identify which step the user is referring to
            target_step = await self._detect_target_step_with_llm(user_message, conversation_context)
            logger.info(f"LLM detected target step: {target_step}")
            
            if target_step and target_step != "unknown":
                # Look for specific step response in conversation history
                target_response = await self._get_response_by_step(session_id, target_step)
                if target_response:
                    logger.info(f"Found target response for step '{target_step}' (length: {len(target_response)} chars)")
                    return target_response, target_step
                else:
                    logger.warning(f"No response found for step '{target_step}', falling back to last assistant response")
            
            # Fallback to last assistant response
            logger.info("Using fallback: last assistant response")
            last_response = await context_service.get_last_assistant_message_content(session_id)
            if last_response:
                logger.info(f"Found last assistant response (length: {len(last_response)} chars)")
                return last_response, "previous"
            else:
                logger.warning("No last assistant response found")
                return None, None
                
        except Exception as e:
            logger.error(f"Error identifying target response: {str(e)}")
            return None, None

    async def _detect_target_step_with_llm(self, user_message: str, conversation_context: str) -> Optional[str]:
        """
        Use LLM to detect which TCC step the user is referring to for modification.
        
        Args:
            user_message: User's modification request
            conversation_context: Recent conversation context
            
        Returns:
            Target step name or "unknown"
        """
        try:
            system_prompt = """Vous êtes un expert en analyse de demandes de modification dans le contexte TCC/ACT.

Votre tâche est d'identifier PRÉCISÉMENT quelle étape TCC l'utilisateur souhaite modifier.

Les étapes TCC possibles sont:
- analyze: Analyse comportementale, cognitive et émotionnelle de la réaction
- impacts: Impacts et conséquences de la réaction (court et long terme)
- sources: Sources, origines, causes et facteurs déclencheurs 
- strategies: Stratégies et techniques TCC/ACT pour gérer la situation"""

            user_prompt = f"""Analysez cette demande de modification pour identifier quelle étape TCC spécifique l'utilisateur veut modifier.

DEMANDE DE L'UTILISATEUR: {user_message}

CONTEXTE DE CONVERSATION: {conversation_context}

INSTRUCTIONS:
- Si l'utilisateur mentionne "impacts", "conséquences", "effets" → répondez "impacts"
- Si l'utilisateur mentionne "analyse", "comportement", "pensées", "émotions" → répondez "analyze"  
- Si l'utilisateur mentionne "sources", "origines", "causes", "pourquoi" → répondez "sources"
- Si l'utilisateur mentionne "stratégies", "solutions", "techniques" → répondez "strategies"
- Si ce n'est pas clair ou aucune étape spécifique → répondez "unknown"

EXEMPLES:
- "Je veux une version plus courte des impacts" → impacts
- "Raccourcis l'analyse de ma réaction" → analyze
- "Simplifie les sources de cette réaction" → sources
- "Résume les stratégies" → strategies
- "Peux-tu reformuler ?" → unknown

RÉPONSE (un seul mot): analyze, impacts, sources, strategies, ou unknown"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = self.client.complete(
                model=self.deployment_name,
                messages=messages,
                max_tokens=5,
                temperature=0.1
            )
            
            detected_step = response.choices[0].message.content.strip().lower()
            
            valid_steps = ["analyze", "impacts", "sources", "strategies", "unknown"]
            
            if detected_step in valid_steps:
                logger.info(f"LLM detected target step: {detected_step}")
                return detected_step if detected_step != "unknown" else None
            else:
                # Try to extract from response if it contains a valid step
                for step in valid_steps:
                    if step in detected_step:
                        logger.info(f"Extracted step '{step}' from LLM response: {detected_step}")
                        return step if step != "unknown" else None
                
                logger.warning(f"LLM returned unrecognized step: {detected_step}")
                return None
                
        except Exception as e:
            logger.error(f"Error in LLM step detection: {str(e)}")
            return None

    async def _get_response_by_step(self, session_id: str, target_step: str) -> Optional[str]:
        """
        Get the assistant response for a specific TCC step.
        
        Args:
            session_id: Session identifier
            target_step: The TCC step to find response for
            
        Returns:
            Assistant response content or None
        """
        try:
            from app.services.chat_session_service import chat_session_service
            
            session_data = await chat_session_service.get_session(session_id)
            if not session_data:
                logger.warning(f"No session data found for {session_id}")
                return None
            
            messages = session_data.get("messages", [])
            logger.info(f"Searching for step '{target_step}' in {len(messages)} messages")
            
            # Look for assistant messages with the target step in metadata
            found_messages = []
            for i, message in enumerate(messages):
                if message.get("role") == "assistant":
                    msg_step = message.get("metadata", {}).get("step")
                    msg_content_preview = message.get("content", "")[:100] + "..."
                    logger.debug(f"Message {i}: step='{msg_step}', content_preview='{msg_content_preview}'")
                    
                    if msg_step == target_step:
                        found_messages.append(message)
            
            if found_messages:
                # Return the FIRST occurrence of this step (usually the original, not a modification)
                target_message = found_messages[0]
                content = target_message.get("content", "")
                logger.info(f"Found {len(found_messages)} messages for step '{target_step}', returning first one (length: {len(content)} chars)")
                return content
            else:
                logger.warning(f"No assistant messages found with step '{target_step}'")
                # Debug: show all available steps
                available_steps = []
                for message in messages:
                    if message.get("role") == "assistant":
                        step = message.get("metadata", {}).get("step")
                        if step:
                            available_steps.append(step)
                logger.info(f"Available steps in conversation: {available_steps}")
                return None
            
        except Exception as e:
            logger.error(f"Error getting response by step: {str(e)}")
            return None

    def _get_last_assistant_response(self, conversation_context: str) -> str:
        """Extract the last assistant response from conversation context."""
        if not conversation_context:
            return ""
        
        # Debug: Log the conversation context structure
        logger.info(f"Conversation context length: {len(conversation_context)}")
        logger.info(f"Conversation context preview:\n{conversation_context[:800]}...")
        
        # Split by sections and look for the most recent analysis or response
        sections = conversation_context.split('###')
        last_response = ""
        
        # Look through sections in reverse order to find the last assistant response
        for section in reversed(sections):
            section = section.strip()
            
            # Skip empty sections
            if not section:
                continue
                
            # Look for analysis sections - these are typically the assistant responses
            if any(keyword in section.lower() for keyword in ['analyse', 'impacts', 'sources', 'stratégies', 'discussion']):
                # Extract content after the first line (which is usually the header)
                lines = section.split('\n')
                if len(lines) > 1:
                    # Join all lines except the first (header) line
                    content_lines = []
                    for line in lines[1:]:
                        line = line.strip()
                        if line:  # Skip empty lines
                            content_lines.append(line)
                    
                    if content_lines:
                        content = '\n'.join(content_lines)
                        if len(content) > 50:  # Make sure we have substantial content
                            last_response = content
                            logger.info(f"Found analysis section: {section.split()[0] if section.split() else 'Unknown'}")
                            break
        
        # If no analysis sections found, look for any substantial content blocks
        if not last_response:
            logger.info("No analysis sections found, looking for substantial content blocks")
            
            # Try to find blocks of text that look like responses
            paragraphs = conversation_context.split('\n\n')
            for paragraph in reversed(paragraphs):
                paragraph = paragraph.strip()
                # Look for substantial paragraphs that don't look like metadata or headers
                if (len(paragraph) > 100 and 
                    not paragraph.startswith('#') and 
                    not paragraph.startswith('[') and
                    not any(meta_word in paragraph.lower() for meta_word in ['scénario', 'réaction', 'historique', 'message'])):
                    last_response = paragraph
                    logger.info("Found substantial content block")
                    break
        
        # Final fallback - get the last substantial line
        if not last_response:
            logger.info("Using fallback - looking for last substantial line")
            lines = conversation_context.strip().split('\n')
            for line in reversed(lines):
                line = line.strip()
                if (len(line) > 80 and 
                    not line.startswith('#') and 
                    not line.startswith('[') and
                    ':' not in line[:30]):  # Avoid metadata lines
                    last_response = line
                    break
        
        logger.info(f"Final extracted response length: {len(last_response)}")
        if last_response:
            logger.info(f"Response preview: {last_response[:150]}...")
        else:
            logger.warning("Could not extract any previous response!")
        
        return last_response


# Create singleton instance
llm_tcc_service = LangChainLLMTCCService() 

