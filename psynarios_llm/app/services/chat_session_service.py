"""
Chat Session Service with full CosmosDB storage - no global variables.
All session data is stored in and retrieved from CosmosDB.
File: app/services/chat_session_service.py
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from azure.cosmos import Cosmos<PERSON>lient, PartitionKey, exceptions

from app.core.config import settings

logger = logging.getLogger(__name__)

class ChatSessionService:
    """Service for managing chat sessions with full CosmosDB persistence."""
    
    def __init__(self):
        """Initialize the chat session service with CosmosDB."""
        self.use_cosmos_db = False
        self.cosmos_client = None
        self.database = None
        self.chat_sessions_container = None
        
        # Initialize CosmosDB connection
        try:
            if (hasattr(settings, 'COSMOS_ENDPOINT') and settings.COSMOS_ENDPOINT and
                hasattr(settings, 'COSMOS_KEY') and settings.COSMOS_KEY):
                
                self.cosmos_client = CosmosClient(
                    url=settings.COSMOS_ENDPOINT,
                    credential=settings.COSMOS_KEY
                )
                
                database_name = settings.COSMOS_DATABASE or "ContextDatabase"
                self.database = self.cosmos_client.get_database_client(database_name)
                
                # Initialize the chat sessions container
                self.chat_sessions_container = self.database.get_container_client("ChatSessions")
                
                self.use_cosmos_db = True
                logger.info("Chat Session Service initialized with CosmosDB")
                # Test the connection
                # NOTE: Cannot use 'await' in __init__, so call synchronously for now
                try:
                    self._test_connection_sync()
                except Exception as e:
                    logger.warning(f"CosmosDB connection test failed: {str(e)}")
                
            else:
                logger.error("CosmosDB credentials not configured. Chat sessions will not be persistent.")
        except Exception as e:
            logger.error(f"Failed to initialize CosmosDB for chat sessions: {str(e)}")
            self.use_cosmos_db = False
    
    async def _test_connection(self):
        """Test CosmosDB connection and container access."""
        try:
            # Try to query the container to test connectivity
            query = "SELECT TOP 1 * FROM c"
            list(self.chat_sessions_container.query_items(
                query=query,
                enable_cross_partition_query=True,
                max_item_count=1
            ))
            logger.info("CosmosDB connection test successful")
        except Exception as e:
            logger.warning(f"CosmosDB connection test failed: {str(e)}")
    
    async def create_session(self, scenario_id: str, scenario_content: str) -> str:
        """
        Create a new chat session in CosmosDB.
        
        Args:
            scenario_id: ID of the scenario
            scenario_content: Content of the scenario
            
        Returns:
            Session ID of the created session
        """
        if not self.use_cosmos_db:
            raise RuntimeError("CosmosDB not available for session creation")
        
        try:
            # Generate unique session ID
            session_id = f"chat_{uuid.uuid4().hex}"
            
            # Create session document
            session_doc = {
                "id": session_id,
                "sessionId": session_id,
                "scenarioId": scenario_id,
                "scenarioContent": scenario_content,
                "messages": [],
                "createdAt": datetime.now().isoformat(),
                "lastActiveAt": datetime.now().isoformat(),
                "status": "active",
                "metadata": {
                    "totalMessages": 0,
                    "userMessages": 0,
                    "assistantMessages": 0,
                    "sessionType": "chat",
                    "tccModeEnabled": False
                }
            }
            
            # Save to CosmosDB
            self.chat_sessions_container.create_item(body=session_doc)
            
            logger.info(f"Created new chat session {session_id} with scenario {scenario_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating chat session: {str(e)}")
            raise RuntimeError(f"Failed to create chat session: {str(e)}")
    
    async def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a chat session from CosmosDB.
        
        Args:
            session_id: ID of the session to retrieve
            
        Returns:
            Session data or None if not found
        """
        if not self.use_cosmos_db:
            logger.warning("CosmosDB not available for session retrieval")
            return None
        
        try:
            # Read session from CosmosDB
            session_doc = self.chat_sessions_container.read_item(
                item=session_id,
                partition_key=session_id
            )
            
            logger.info(f"Retrieved session {session_id} from CosmosDB")
            return session_doc
            
        except exceptions.CosmosResourceNotFoundError:
            logger.warning(f"Session {session_id} not found in CosmosDB")
            return None
        except Exception as e:
            logger.error(f"Error retrieving session {session_id}: {str(e)}")
            return None
    
    async def add_message_to_session(
        self, 
        session_id: str, 
        role: str, 
        content: str, 
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Add a message to an existing session in CosmosDB.
        
        Args:
            session_id: ID of the session
            role: Role of the message sender ("user" or "assistant")
            content: Content of the message
            metadata: Optional metadata for the message
            
        Returns:
            True if successful, False otherwise
        """
        if not self.use_cosmos_db:
            logger.warning("CosmosDB not available for adding message")
            return False
        
        try:
            # Get current session
            session_doc = await self.get_session(session_id)
            if not session_doc:
                logger.error(f"Session {session_id} not found for adding message")
                return False
            
            # Create message object
            message = {
                "id": f"msg_{uuid.uuid4().hex[:8]}",
                "role": role,
                "content": content,
                "timestamp": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            
            # Add message to session
            session_doc["messages"].append(message)
            
            # Update session metadata
            session_doc["lastActiveAt"] = datetime.now().isoformat()
            session_doc["metadata"]["totalMessages"] += 1
            
            if role == "user":
                session_doc["metadata"]["userMessages"] += 1
            elif role == "assistant":
                session_doc["metadata"]["assistantMessages"] += 1
            
            # Check if TCC mode is enabled based on metadata
            if metadata and metadata.get("step") in ["analyze", "impacts", "sources", "strategies"]:
                session_doc["metadata"]["tccModeEnabled"] = True
            
            # Update session in CosmosDB
            self.chat_sessions_container.replace_item(
                item=session_id,
                body=session_doc
            )
            
            logger.info(f"Added {role} message to session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding message to session {session_id}: {str(e)}")
            return False
    
    async def update_session_metadata(
        self, 
        session_id: str, 
        metadata_updates: Dict[str, Any]
    ) -> bool:
        """
        Update session metadata in CosmosDB.
        
        Args:
            session_id: ID of the session
            metadata_updates: Metadata fields to update
            
        Returns:
            True if successful, False otherwise
        """
        if not self.use_cosmos_db:
            return False
        
        try:
            session_doc = await self.get_session(session_id)
            if not session_doc:
                return False
            
            # Update metadata
            session_doc["metadata"].update(metadata_updates)
            session_doc["lastActiveAt"] = datetime.now().isoformat()
            
            # Save to CosmosDB
            self.chat_sessions_container.replace_item(
                item=session_id,
                body=session_doc
            )
            
            logger.info(f"Updated metadata for session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating session metadata: {str(e)}")
            return False
    
    async def delete_session(self, session_id: str) -> bool:
        """
        Delete a chat session from CosmosDB.
        
        Args:
            session_id: ID of the session to delete
            
        Returns:
            True if successful, False otherwise
        """
        if not self.use_cosmos_db:
            return False
        
        try:
            self.chat_sessions_container.delete_item(
                item=session_id,
                partition_key=session_id
            )
            
            logger.info(f"Deleted session {session_id} from CosmosDB")
            return True
            
        except exceptions.CosmosResourceNotFoundError:
            logger.warning(f"Session {session_id} not found for deletion")
            return False
        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {str(e)}")
            return False
    
    async def list_user_sessions(
        self, 
        scenario_id: Optional[str] = None, 
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        List chat sessions, optionally filtered by scenario ID.
        
        Args:
            scenario_id: Optional scenario ID to filter by
            limit: Maximum number of sessions to return
            
        Returns:
            List of session summaries
        """
        if not self.use_cosmos_db:
            return []
        
        try:
            # Build query
            if scenario_id:
                query = """
                SELECT c.id, c.sessionId, c.scenarioId, c.createdAt, 
                       c.lastActiveAt, c.status, c.metadata
                FROM c 
                WHERE c.scenarioId = @scenarioId
                ORDER BY c.lastActiveAt DESC
                """
                parameters = [{"name": "@scenarioId", "value": scenario_id}]
            else:
                query = """
                SELECT c.id, c.sessionId, c.scenarioId, c.createdAt, 
                       c.lastActiveAt, c.status, c.metadata
                FROM c 
                ORDER BY c.lastActiveAt DESC
                """
                parameters = []
            
            # Execute query
            sessions = list(self.chat_sessions_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True,
                max_item_count=limit
            ))
            
            logger.info(f"Retrieved {len(sessions)} sessions")
            return sessions
            
        except Exception as e:
            logger.error(f"Error listing sessions: {str(e)}")
            return []
    
    async def cleanup_old_sessions(self, max_age_days: int = 7) -> int:
        """
        Clean up old inactive sessions from CosmosDB.
        
        Args:
            max_age_days: Maximum age of sessions to keep
            
        Returns:
            Number of sessions deleted
        """
        if not self.use_cosmos_db:
            return 0
        
        try:
            # Calculate cutoff date
            cutoff_date = datetime.now() - timedelta(days=max_age_days)
            cutoff_iso = cutoff_date.isoformat()
            
            # Find old sessions
            query = """
            SELECT c.id, c.sessionId 
            FROM c 
            WHERE c.lastActiveAt < @cutoffDate
            """
            parameters = [{"name": "@cutoffDate", "value": cutoff_iso}]
            
            old_sessions = list(self.chat_sessions_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True
            ))
            
            # Delete old sessions
            deleted_count = 0
            for session in old_sessions:
                try:
                    await self.delete_session(session["sessionId"])
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"Error deleting session {session['sessionId']}: {str(e)}")
            
            logger.info(f"Cleaned up {deleted_count} old sessions")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Error during session cleanup: {str(e)}")
            return 0
    
    async def get_session_stats(self) -> Dict[str, Any]:
        """
        Get statistics about chat sessions.
        
        Returns:
            Dictionary with session statistics
        """
        if not self.use_cosmos_db:
            return {"error": "CosmosDB not available"}
        
        try:
            # Total sessions
            total_query = "SELECT VALUE COUNT(1) FROM c"
            total_sessions = list(self.chat_sessions_container.query_items(
                query=total_query,
                enable_cross_partition_query=True
            ))[0]
            
            # Active sessions (last 24 hours)
            yesterday = (datetime.now() - timedelta(days=1)).isoformat()
            active_query = """
            SELECT VALUE COUNT(1) 
            FROM c 
            WHERE c.lastActiveAt >= @yesterday
            """
            active_sessions = list(self.chat_sessions_container.query_items(
                query=active_query,
                parameters=[{"name": "@yesterday", "value": yesterday}],
                enable_cross_partition_query=True
            ))[0]
            
            # TCC mode sessions
            tcc_query = """
            SELECT VALUE COUNT(1) 
            FROM c 
            WHERE c.metadata.tccModeEnabled = true
            """
            tcc_sessions = list(self.chat_sessions_container.query_items(
                query=tcc_query,
                enable_cross_partition_query=True
            ))[0]
            
            return {
                "total_sessions": total_sessions,
                "active_sessions_24h": active_sessions,
                "tcc_mode_sessions": tcc_sessions,
                "cosmos_db_enabled": True
            }
            
        except Exception as e:
            logger.error(f"Error getting session stats: {str(e)}")
            return {"error": str(e)}

# Create singleton instance
chat_session_service = ChatSessionService()