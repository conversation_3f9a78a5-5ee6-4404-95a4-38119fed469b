"""
Enhanced Context Service with strict CosmosDB-only storage and ADVANCED HYBRID summarization.
Integrates with the new LLMSummarizer returning structured (narrative + insights) summaries.
Manages rolling summaries and session-wide key insights.
"""

import logging
import time
import asyncio
import random
import json
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from azure.cosmos import CosmosClient, PartitionKey, exceptions

# Import configuration settings
from app.core.config import settings
# Import the new summarizer and its output type
from app.services.llm_summarizer import LLMSummarizer, HybridSummaryOutput

logger = logging.getLogger(__name__)


class MessageMetadata(BaseModel):
    """Metadata for a message in the conversation context."""
    timestamp: datetime = Field(default_factory=datetime.now)
    session_id: str
    message_type: str  # 'scenario', 'reaction', 'analysis', 'user_input', etc.
    step: Optional[str] = None
    tokens: Optional[int] = None
    parameters: Optional[Dict[str, Any]] = None
    rag_sources: Optional[List[Dict[str, Any]]] = None
    retry_count: int = 0

class ConversationMessage(BaseModel):
    """A message in the conversation with its metadata."""
    id: str
    content: str
    metadata: MessageMetadata
    # Store structured summary directly
    hybrid_summary: Optional[HybridSummaryOutput] = None 
    is_summarized: bool = False
    # Keep expiry if it was used in the original logic, otherwise remove
    # expiry: Optional[datetime] = None 

class SessionContextData(BaseModel):
    """Core data stored for a session context in CosmosDB."""
    id: str # Use session_id as the document ID
    sessionId: str
    rolling_narrative_summary: str = ""
    key_insights_list: List[str] = []
    last_updated: datetime = Field(default_factory=datetime.now)
    # Configuration (can be adjusted per session if needed)
    max_recent_messages_trigger: int = 6 # Trigger summarization sooner
    max_tokens_trigger: int = 4000 # Trigger if recent messages exceed this token count
    summarization_batch_size: int = 2 # Summarize pairs (e.g., reaction + analysis)
    keep_recent_count: int = 4 # Keep last 4 full messages
    protected_message_types: List[str] = ["scenario"] # Only protect the initial scenario
    max_key_insights_stored: int = 30 # Limit total stored insights

# --- Enhanced Context Service --- 

class EnhancedContextService:
    """
    Manages conversation context using CosmosDB and the advanced hybrid summarizer.
    Uses ONLY session_id for all database operations.
    """
    
    def __init__(self):
        """Initialize the enhanced context service with strict CosmosDB requirements."""
        self.use_cosmos_db = False
        self.summarization_available = False
        self.llm_summarizer = None

        # Initialize summarization stats (using new keys)
        self.summarization_stats = {
            "total_summarizations_attempted": 0,
            "successful_summarizations": 0,
            "failed_summarizations": 0,
            "total_insights_extracted": 0,
            "last_summarization_time": None,
            "errors": 0
        }

        # Initialize LLM Summarizer (using the updated class)
        try:
            # Increased timeout for hybrid summarizer
            self.llm_summarizer = LLMSummarizer(max_retries=2, timeout_seconds=60) 
            self.summarization_available = True
            logger.info("Hybrid LLM Summarizer initialized successfully.")
        except Exception as e:
            logger.error(f"Failed to initialize Hybrid LLM summarizer: {str(e)}", exc_info=True)
            self.summarization_available = False
            self.llm_summarizer = None
        
        # Configure Azure Cosmos DB - REQUIRED for operation
        try:
            if (settings.COSMOS_ENDPOINT and settings.COSMOS_KEY):
                self.cosmos_client = CosmosClient(url=settings.COSMOS_ENDPOINT, credential=settings.COSMOS_KEY)
                database_name = settings.COSMOS_DATABASE or "ContextDatabase"
                self.database = self.cosmos_client.get_database_client(database_name)
                
                # Container for session context data (rolling summary, insights)
                self.session_data_container = self.database.get_container_client("ConversationContext") 
                # Container for individual messages (recent, full content)
                self.messages_container = self.database.get_container_client("SessionMessages")
                self.summarized_container = self.database.get_container_client("SummarizedOlderMessages")
                self.metadata_container = self.database.get_container_client("MetaData")
                self.users_container = self.database.get_container_client("Users")
                self.session_index_container = self.database.get_container_client("Sessions")
                # Container for summarized messages (optional, could just mark in SessionMessages)
                # For simplicity, we'll mark messages as summarized in SessionMessages for now.
                # self.summarized_container = self.database.get_container_client("SummarizedMessages")
                
                self.use_cosmos_db = True
                logger.info("Enhanced Context Service initialized with Azure Cosmos DB.")
                self._test_cosmos_connection()
            else:
                logger.error("Azure Cosmos DB connection info not configured.")
                raise RuntimeError("CosmosDB configuration required.")
        except Exception as e:
            logger.error(f"Failed to initialize Azure Cosmos DB: {str(e)}", exc_info=True)
            raise RuntimeError(f"Enhanced Context Service requires CosmosDB: {str(e)}")

        # Remove the redundant/old direct LLM client initialization if it existed
        # self.llm_client = None 

    def _test_cosmos_connection(self):
        """Test CosmosDB connection and container access."""
        try:
            # Test NEW containers
            containers_to_test = [
                ("ConversationContext", self.session_data_container),
                ("SessionMessages", self.messages_container),
            ]
            for container_name, container in containers_to_test:
                try:
                    list(container.query_items("SELECT TOP 1 * FROM c", enable_cross_partition_query=True, max_item_count=1))
                    logger.info(f"CosmosDB container 	'{container_name}	' accessible.")
                except Exception as e:
                    # Log as error, these containers are essential now
                    logger.error(f"CosmosDB container 	'{container_name}	' test failed: {str(e)}") 
                    raise RuntimeError(f"Essential CosmosDB container 	'{container_name}	' not accessible.")
        except Exception as e:
            logger.error(f"CosmosDB connection test failed: {str(e)}")
            raise RuntimeError("CosmosDB connection test failed.")

    def _generate_message_id(self, session_id: str, timestamp: datetime) -> str:
        """Generate a unique ID for a message within a session."""
        # Using a simpler generation method consistent with the new logic
        return f"msg_{session_id}_{timestamp.isoformat()}_{random.randint(1000, 9999)}"

    # --- NEW: Session Context Data Management --- 

    async def _get_session_context_data(self, session_id: str, user_id: Optional[str] = "unknown") -> SessionContextData:
        """Retrieve or create session context data (summary, insights)."""
        if not self.use_cosmos_db:
            raise RuntimeError("CosmosDB not available")
        try:
            item = self.session_data_container.read_item(item=session_id, partition_key=session_id)
            logger.debug(f"Loaded session context data for {session_id}")
            # Ensure loaded data conforms to the model, providing defaults if fields are missing
            return SessionContextData(
                id=item.get("id", session_id),
                sessionId=item.get("sessionId", session_id),
                userId=item.get("userId", user_id),
                rolling_narrative_summary=item.get("rolling_narrative_summary", ""),
                key_insights_list=item.get("key_insights_list", []),
                last_updated=datetime.fromisoformat(item.get("last_updated", datetime.now().isoformat())),
                # Load config, falling back to defaults
                max_recent_messages_trigger=item.get("max_recent_messages_trigger", 6),
                max_tokens_trigger=item.get("max_tokens_trigger", 4000),
                summarization_batch_size=item.get("summarization_batch_size", 2),
                keep_recent_count=item.get("keep_recent_count", 4),
                protected_message_types=item.get("protected_message_types", ["scenario"]),
                max_key_insights_stored=item.get("max_key_insights_stored", 30)
            )
        except exceptions.CosmosResourceNotFoundError:
            logger.info(f"No session context data found for {session_id}. Creating new entry.")
            # Use provided or default user_id when creating
            effective_user_id = user_id if user_id != "unknown" else session_id # Fallback user_id
            new_context_data = SessionContextData(id=session_id, sessionId=session_id, userId=effective_user_id)
            await self._save_session_context_data(new_context_data)
            return new_context_data
        except Exception as e:
            logger.error(f"Error retrieving session context data for {session_id}: {str(e)}", exc_info=True)
            # Return default empty context on error
            effective_user_id = user_id if user_id != "unknown" else session_id
            return SessionContextData(id=session_id, sessionId=session_id)

    async def _save_session_context_data(self, context_data: SessionContextData):
        """Save session context data (summary, insights) to CosmosDB."""
        if not self.use_cosmos_db:
            return
        try:
            context_data.last_updated = datetime.now()
            # Use model_dump for Pydantic v2
            data_to_save = context_data.model_dump(mode='json') 
            self.session_data_container.upsert_item(body=data_to_save)
            logger.debug(f"Saved session context data for {context_data.sessionId}")
        except Exception as e:
            logger.error(f"Error saving session context data for {context_data.sessionId}: {str(e)}", exc_info=True)

    # --- Message Handling and Summarization --- 

    async def add_message(
        self,
        session_id: str,
        content: str,
        message_type: str,
        step: Optional[str] = None,
        tokens: Optional[int] = None,
        parameters: Optional[Dict[str, Any]] = None,
        rag_sources: Optional[List[Dict[str, Any]]] = None
    ):
        """Add a message to the context and trigger hybrid summarization if needed."""
        if not self.use_cosmos_db:
            logger.warning("Cannot add message, CosmosDB not available.")
            return

        try:
            timestamp = datetime.now()
            # Use the new ID generation based on session and timestamp
            message_id = self._generate_message_id(session_id, timestamp) 
            
            metadata = MessageMetadata(
                timestamp=timestamp,
                session_id=session_id,
                message_type=message_type,
                step=step,
                tokens=tokens,
                parameters=parameters,
                rag_sources=rag_sources
            )
            
            message = ConversationMessage(
                id=message_id,
                content=content,
                metadata=metadata,
                is_summarized=False # New messages are never summarized initially
            )
            
            # Save the full message to the NEW messages container
            # Use model_dump for Pydantic v2
            message_data_to_save = message.model_dump(mode='json')
            # Add sessionId for partitioning/querying
            message_data_to_save['sessionId'] = session_id 
            # Ensure id field matches the document id required by CosmosDB
            message_data_to_save['id'] = message_id 
            self.messages_container.create_item(body=message_data_to_save)
            logger.info(f"Added message {message_id} (type: {message_type}) to session {session_id} in SessionMessages container.")

            # Check if summarization should be triggered using the new logic
            await self._trigger_summarization_if_needed(session_id)

        except Exception as e:
            logger.error(f"Error adding message to session {session_id}: {str(e)}", exc_info=True)

    async def _trigger_summarization_if_needed(self, session_id: str):
        """Check conditions and run the new hybrid summarization process."""
        if not self.summarization_available or not self.llm_summarizer:
            logger.debug("Hybrid summarization not available, skipping trigger check.")
            return

        try:
            session_data = await self._get_session_context_data(session_id )
            # Load only unsummarized messages from the new container
            recent_unsummarized_messages = await self._load_recent_unsummarized_messages(session_id)

            # Filter out protected types before checking triggers
            unsummarized_non_protected = [
                msg for msg in recent_unsummarized_messages 
                if msg.metadata.message_type not in session_data.protected_message_types
            ]

            should_summarize = False
            trigger_reason = ""
            if len(unsummarized_non_protected) >= session_data.max_recent_messages_trigger:
                trigger_reason = f"message count ({len(unsummarized_non_protected)} >= {session_data.max_recent_messages_trigger})"
                should_summarize = True
            # else: # Add token count trigger if needed (requires token info)
                # current_token_count = sum(msg.metadata.tokens or 0 for msg in unsummarized_non_protected)
                # if current_token_count >= session_data.max_tokens_trigger:
                #     trigger_reason = f"token count ({current_token_count} >= {session_data.max_tokens_trigger})"
                #     should_summarize = True

            if should_summarize:
                logger.info(f"Hybrid summarization triggered for session {session_id} due to {trigger_reason}")
                await self._run_summarization_process(session_id, session_data, recent_unsummarized_messages)
            else:
                logger.debug(f"Summarization not triggered for session {session_id}. Count: {len(unsummarized_non_protected)}/{session_data.max_recent_messages_trigger}")

        except Exception as e:
            logger.error(f"Error during summarization trigger check for session {session_id}: {str(e)}", exc_info=True)

    async def _load_recent_unsummarized_messages(self, session_id: str) -> List[ConversationMessage]:
        """Load recent messages that haven't been summarized yet from the new container."""
        if not self.use_cosmos_db:
            return []
        
        try:
            query = """
            SELECT * FROM c 
            WHERE c.sessionId = @sessionId AND c.is_summarized = false
            ORDER BY c.metadata.timestamp ASC
            """
            parameters = [{"name": "@sessionId", "value": session_id}]
            
            items = list(self.messages_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True
            ))
            
            messages = []
            for item in items:
                try:
                    # Parse metadata carefully
                    metadata_dict = item.get("metadata", {})
                    if isinstance(metadata_dict, str):
                        metadata_dict = json.loads(metadata_dict)
                    if "timestamp" in metadata_dict and isinstance(metadata_dict["timestamp"], str):
                        metadata_dict["timestamp"] = datetime.fromisoformat(metadata_dict["timestamp"])
                    
                    metadata = MessageMetadata(**metadata_dict)
                    
                    # Parse hybrid summary if present
                    hybrid_summary_data = item.get("hybrid_summary")
                    hybrid_summary = HybridSummaryOutput(**hybrid_summary_data) if hybrid_summary_data else None

                    message = ConversationMessage(
                        id=item.get("id"),
                        content=item.get("content", ""),
                        metadata=metadata,
                        hybrid_summary=hybrid_summary,
                        is_summarized=item.get("is_summarized", False)
                    )
                    messages.append(message)
                except Exception as parse_error:
                    logger.warning(f"Skipping message due to parsing error: {parse_error} - Item: {item.get('id')}")
                    continue # Skip malformed messages

            logger.debug(f"Loaded {len(messages)} unsummarized messages for session {session_id}")
            return messages
            
        except Exception as e:
            logger.error(f"Error loading unsummarized messages from Cosmos DB for session {session_id}: {str(e)}", exc_info=True)
            return []

    async def _run_summarization_process(self, session_id: str, session_data: SessionContextData, messages_to_process: List[ConversationMessage]):
        """Perform hybrid summarization with simple token management."""
        if not self.summarization_available or not self.llm_summarizer:
            return

        logger.info(f"Running hybrid summarization for {len(messages_to_process)} messages in session {session_id}")
        self.summarization_stats["total_summarizations_attempted"] += 1
        start_time = time.time()
        summarized_count = 0
        insights_extracted_count = 0
        failed_count = 0

        # Filter out protected types
        messages_to_summarize = [
            msg for msg in messages_to_process 
            if msg.metadata.message_type not in session_data.protected_message_types
        ]

        # Process messages individually
        new_narrative_parts = []
        new_insights = []
        
        for message in messages_to_summarize:
            try:
                hybrid_summary: HybridSummaryOutput = await self.llm_summarizer.summarize_message_hybrid(message)
                
                # Update the message in CosmosDB with the summary and mark as summarized
                message.hybrid_summary = hybrid_summary
                message.is_summarized = True
                
                updated_message_data = message.model_dump(mode='json')
                updated_message_data['sessionId'] = session_id 
                self.messages_container.upsert_item(body=updated_message_data)
                
                # Collect new summaries and insights
                new_narrative_parts.append(hybrid_summary['narrative_summary'])
                new_insights.extend(hybrid_summary['key_insights'])
                
                summarized_count += 1
                insights_extracted_count += len(hybrid_summary['key_insights'])
                logger.debug(f"Successfully summarized message {message.id}")

            except Exception as e:
                logger.error(f"Failed to summarize message {message.id}: {str(e)}", exc_info=True)
                failed_count += 1

        # Print summarization results to console
        if summarized_count > 0:
            print("\n" + "="*60)
            print(f"📝 SUMMARIZATION RESULTS - Session: {session_id}")
            print("="*60)
            
            if new_narrative_parts:
                print(f"\n🔸 NEW NARRATIVE SUMMARIES ({len(new_narrative_parts)}):")
                for i, narrative in enumerate(new_narrative_parts, 1):
                    print(f"   {i}. {narrative}")
            
            if new_insights:
                print(f"\n💡 NEW KEY INSIGHTS ({len(new_insights)}):")
                for i, insight in enumerate(new_insights, 1):
                    print(f"   {i}. {insight}")
            
            print(f"\n📊 STATS: {summarized_count} messages summarized, {insights_extracted_count} insights extracted")
            print("="*60)

        # Content-aware token management
        if new_narrative_parts:
            # Add new narrative parts
            session_data.rolling_narrative_summary += "\n" + "\n".join(new_narrative_parts)
            
            # Content-aware truncation: keep only last 2000 characters
            if len(session_data.rolling_narrative_summary) > 2000:
                text = session_data.rolling_narrative_summary
                target_length = 1800  # Leave room for prefix
                
                # Try to find semantic breaks in order of preference
                break_markers = [
                    '\n- ',      # New insight/point
                    '. ',        # End of sentence
                    ', ',        # Comma pause
                    ' '          # Word boundary
                ]
                
                best_break = None
                for marker in break_markers:
                    # Look for marker in the portion we want to keep
                    search_start = len(text) - target_length - 200  # Look a bit before target
                    search_end = len(text) - target_length + 200    # And a bit after
                    break_pos = text.rfind(marker, max(0, search_start), min(len(text), search_end))
                    
                    if break_pos > 0:
                        best_break = break_pos + len(marker)
                        break
                
                if best_break:
                    session_data.rolling_narrative_summary = "...[contexte précédent]...\n" + text[best_break:]
                else:
                    # Fallback: simple character truncation
                    session_data.rolling_narrative_summary = "...[contexte précédent]...\n" + text[-target_length:]

        # Add new insights with content-aware deduplication
        if new_insights:
            # Remove duplicate insights before adding
            existing_insights_lower = [insight.lower().strip() for insight in session_data.key_insights_list]
            
            for new_insight in new_insights:
                new_insight_clean = new_insight.strip()
                new_insight_lower = new_insight_clean.lower()
                
                # Check for duplicates or very similar insights
                is_duplicate = False
                for existing in existing_insights_lower:
                    # Check for exact match or high similarity
                    if new_insight_lower == existing or (
                        len(new_insight_lower) > 10 and 
                        new_insight_lower in existing or existing in new_insight_lower
                    ):
                        is_duplicate = True
                        break
                
                if not is_duplicate and len(new_insight_clean) > 5:
                    session_data.key_insights_list.append(new_insight_clean)
            
            # Keep only the most recent and diverse insights
            if len(session_data.key_insights_list) > 15:
                # Prioritize recent insights but avoid duplicates
                unique_insights = []
                seen_lower = set()
                
                # Process from newest to oldest
                for insight in reversed(session_data.key_insights_list):
                    insight_lower = insight.lower().strip()
                    if insight_lower not in seen_lower and len(insight.strip()) > 5:
                        unique_insights.append(insight)
                        seen_lower.add(insight_lower)
                        if len(unique_insights) >= 15:
                            break
                
                # Reverse to get chronological order again
                session_data.key_insights_list = list(reversed(unique_insights))

        # Save the updated session context data
        await self._save_session_context_data(session_data)

        # Print final context state to console
        if summarized_count > 0:
            print("\n" + "🔄"*20)
            print(f"📋 FINAL CONTEXT STATE - Session: {session_id}")
            print("🔄"*20)
            
            print(f"\n📖 ROLLING NARRATIVE ({len(session_data.rolling_narrative_summary)} chars):")
            print(f"   {session_data.rolling_narrative_summary}")
            
            print(f"\n🗝️ ALL KEY INSIGHTS ({len(session_data.key_insights_list)} total):")
            for i, insight in enumerate(session_data.key_insights_list, 1):
                print(f"   {i:2d}. {insight}")
            
            print("🔄"*20)

        elapsed_time = time.time() - start_time
        self.summarization_stats["successful_summarizations"] += summarized_count
        self.summarization_stats["failed_summarizations"] += failed_count
        self.summarization_stats["total_insights_extracted"] += insights_extracted_count
        self.summarization_stats["last_summarization_time"] = datetime.now().isoformat()
        if failed_count > 0:
            self.summarization_stats["errors"] += 1

        logger.info(f"Hybrid summarization complete for session {session_id} in {elapsed_time:.2f}s. Summarized: {summarized_count}, Failed: {failed_count}, Insights: {insights_extracted_count}")
        # --- Context Formatting for LLM --- 

    async def _load_recent_messages_for_prompt(self, session_id: str, count: int) -> List[ConversationMessage]:
        """Load the most recent 'count' messages (summarized or not) for the LLM prompt."""
        if not self.use_cosmos_db:
            return []
        
        try:
            # Query for the latest messages by timestamp DESC
            query = """
            SELECT TOP @count * FROM c 
            WHERE c.sessionId = @sessionId
            ORDER BY c.metadata.timestamp DESC
            """
            parameters = [
                {"name": "@sessionId", "value": session_id},
                {"name": "@count", "value": count}
            ]
            
            items = list(self.messages_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True
            ))
            
            messages = []
            for item in items:
                try:
                    # Parse metadata carefully
                    metadata_dict = item.get("metadata", {})
                    if isinstance(metadata_dict, str):
                        metadata_dict = json.loads(metadata_dict)
                    if "timestamp" in metadata_dict and isinstance(metadata_dict["timestamp"], str):
                        metadata_dict["timestamp"] = datetime.fromisoformat(metadata_dict["timestamp"])
                    
                    metadata = MessageMetadata(**metadata_dict)
                    
                    # Parse hybrid summary if present
                    hybrid_summary_data = item.get("hybrid_summary")
                    hybrid_summary = HybridSummaryOutput(**hybrid_summary_data) if hybrid_summary_data else None

                    message = ConversationMessage(
                        id=item.get("id"),
                        content=item.get("content", ""),
                        metadata=metadata,
                        hybrid_summary=hybrid_summary,
                        is_summarized=item.get("is_summarized", False)
                    )
                    messages.append(message)
                except Exception as parse_error:
                    logger.warning(f"Skipping message in prompt load due to parsing error: {parse_error} - Item: {item.get('id')}")
                    continue # Skip malformed messages

            # Reverse to get chronological order for the prompt
            messages.reverse()
            logger.debug(f"Loaded {len(messages)} messages for LLM prompt for session {session_id}")
            return messages
            
        except Exception as e:
            logger.error(f"Error loading messages for prompt from Cosmos DB for session {session_id}: {str(e)}", exc_info=True)
            return []

    async def get_formatted_context_for_llm(self, session_id: str) -> str:
        """
        Format the conversation context for the main LLM, using the new hybrid structure.
        Includes rolling summary, key insights, and recent full messages.
        """
        if not self.use_cosmos_db:
            return "Contexte non disponible (Base de données inaccessible)."

        try:
            session_data = await self._get_session_context_data(session_id)
            recent_messages = await self._load_recent_messages_for_prompt(session_id, session_data.keep_recent_count)

            context_str = "### CONTEXTE DE LA CONVERSATION ###\n\n"
            
            # 1. Rolling Narrative Summary
            if session_data.rolling_narrative_summary:
                context_str += "**Résumé Narratif Continu:**\n"
                context_str += session_data.rolling_narrative_summary.strip() + "\n\n"
            
            # 2. Key Insights
            if session_data.key_insights_list:
                context_str += "**Points Clés Identifiés Durant la Session:**\n"
                for insight in session_data.key_insights_list:
                    context_str += f"- {insight}\n"
                context_str += "\n"
            
            # 3. Recent Messages (Full Content)
            if recent_messages:
                context_str += "**Messages Récents (Contenu Complet):**\n"
                for msg in recent_messages:
                    role = "Utilisateur" if msg.metadata.message_type in ["reaction", "user_input"] else "Assistant"
                    context_str += f"{role} ({msg.metadata.timestamp.strftime('%H:%M:%S')} - Type: {msg.metadata.message_type}): {msg.content}\n"
            
            context_str += "\n### FIN DU CONTEXTE ###\n"

            logger.info(f"Formatted context for LLM (len: {len(context_str)}) for session {session_id}")
            return context_str.strip()

        except Exception as e:
            logger.error(f"Error formatting context for LLM for session {session_id}: {str(e)}", exc_info=True)
            return "Erreur lors de la récupération du contexte."

    # --- Other Utility Methods (Adapted or New) --- 

    async def get_last_assistant_message_content(self, session_id: str) -> Optional[str]:
        """Retrieve the content of the most recent assistant message from the new container."""
        if not self.use_cosmos_db:
            return None
        try:
            # Query for the latest message with an assistant-like type
            # Adjust message_type check based on actual types used for assistant responses
            query = """
            SELECT TOP 1 c.content FROM c 
            WHERE c.sessionId = @sessionId AND c.metadata.message_type = 'analysis' 
            ORDER BY c.metadata.timestamp DESC
            """
            # Alternative query if multiple types represent assistant:
            # query = """
            # SELECT TOP 1 c.content FROM c 
            # WHERE c.sessionId = @sessionId AND c.metadata.message_type IN ('analysis', 'assistant_response', '...') 
            # ORDER BY c.metadata.timestamp DESC
            # """
            parameters = [{"name": "@sessionId", "value": session_id}]
            
            items = list(self.messages_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True,
                max_item_count=1
            ))
            
            if items:
                return items[0].get("content")
            else:
                logger.warning(f"No last assistant message found for session {session_id}")
                return None
        except Exception as e:
            logger.error(f"Error retrieving last assistant message for session {session_id}: {str(e)}", exc_info=True)
            return None

    async def _load_all_messages(self, session_id: str) -> List[ConversationMessage]:
        """Load ALL messages for a session from the new container, ordered by timestamp."""
        if not self.use_cosmos_db:
            return []
        
        try:
            query = """
            SELECT * FROM c 
            WHERE c.sessionId = @sessionId
            ORDER BY c.metadata.timestamp ASC
            """
            parameters = [{"name": "@sessionId", "value": session_id}]
            
            items = list(self.messages_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True
            ))
            
            messages = []
            for item in items:
                try:
                    # Parse metadata carefully
                    metadata_dict = item.get("metadata", {})
                    if isinstance(metadata_dict, str):
                        metadata_dict = json.loads(metadata_dict)
                    if "timestamp" in metadata_dict and isinstance(metadata_dict["timestamp"], str):
                        metadata_dict["timestamp"] = datetime.fromisoformat(metadata_dict["timestamp"])
                    
                    metadata = MessageMetadata(**metadata_dict)
                    
                    # Parse hybrid summary if present
                    hybrid_summary_data = item.get("hybrid_summary")
                    hybrid_summary = HybridSummaryOutput(**hybrid_summary_data) if hybrid_summary_data else None

                    message = ConversationMessage(
                        id=item.get("id"),
                        content=item.get("content", ""),
                        metadata=metadata,
                        hybrid_summary=hybrid_summary,
                        is_summarized=item.get("is_summarized", False)
                    )
                    messages.append(message)
                except Exception as parse_error:
                    logger.warning(f"Skipping message in history load due to parsing error: {parse_error} - Item: {item.get('id')}")
                    continue # Skip malformed messages

            logger.debug(f"Loaded {len(messages)} total messages for history view for session {session_id}")
            return messages
            
        except Exception as e:
            logger.error(f"Error loading full message history from Cosmos DB for session {session_id}: {str(e)}", exc_info=True)
            return []

    async def get_conversation_history(self, session_id: str) -> Dict[str, Any]:
        """Retrieve conversation history details using the new structure."""
        if not self.use_cosmos_db:
            return {"error": "CosmosDB not available"}
        
        try:
            session_data = await self._get_session_context_data(session_id)
            all_messages = await self._load_all_messages(session_id)
            
            # Use model_dump for Pydantic v2
            history = {
                "session_id": session_id,
                "rolling_summary": session_data.rolling_narrative_summary,
                "key_insights": session_data.key_insights_list,
                "messages": [msg.model_dump(mode='json') for msg in all_messages],
                "last_updated": session_data.last_updated.isoformat(),
                "summarization_stats": self.summarization_stats
            }
            return history
        except Exception as e:
            logger.error(f"Error retrieving conversation history for session {session_id}: {str(e)}", exc_info=True)
            return {"error": f"Failed to retrieve history: {str(e)}"}

    async def clear_context(self, session_id: str):
        """Clear all context data for a session from the new containers."""
        if not self.use_cosmos_db:
            logger.warning("Cannot clear context, CosmosDB not available.")
            return
        
        logger.info(f"Clearing context for session {session_id} ")
        deleted_messages_count = 0
        session_data_cleared = False

        try:
            # Delete session context data document
            try:
                self.session_data_container.delete_item(item=session_id, partition_key=session_id)
                session_data_cleared = True
                logger.info(f"Deleted session context data document for {session_id}")
            except exceptions.CosmosResourceNotFoundError:
                logger.info(f"No session context data document found to delete for {session_id}")
                session_data_cleared = True # Considered cleared if not found
            except Exception as e:
                 logger.error(f"Error deleting session context data for {session_id}: {str(e)}")

            # Delete all messages for the session
            query = "SELECT c.id FROM c WHERE c.sessionId = @sessionId"
            parameters = [{"name": "@sessionId", "value": session_id}]
            
            items_to_delete = list(self.messages_container.query_items(
                query=query,
                parameters=parameters,
                enable_cross_partition_query=True
            ))
            
            for item in items_to_delete:
                try:
                    # Assuming session_id is the partition key for SessionMessages container
                    self.messages_container.delete_item(item=item['id'], partition_key=session_id)
                    deleted_messages_count += 1
                except exceptions.CosmosResourceNotFoundError:
                    pass # Already deleted
                except Exception as e:
                    logger.error(f"Error deleting message {item['id']} for session {session_id}: {str(e)}")
            
            logger.info(f"Cleared context for session {session_id}. Deleted messages: {deleted_messages_count}. Session data cleared: {session_data_cleared}")

        except Exception as e:
            logger.error(f"Error during context clearing for session {session_id}: {str(e)}", exc_info=True)

# Instantiate the service
context_service = EnhancedContextService()

