"""
Entry point for the FastAPI application with enhanced services integration and proper shutdown handling.
"""

import asyncio
import logging
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import time
import traceback
import signal
import sys

from app.api.router import api_router
from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global service references for proper shutdown
services_to_shutdown = []

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle startup and shutdown events with proper service management."""
    global services_to_shutdown
    
    # Startup
    logger.info("Starting TCC Analysis Service with Enhanced Services...")
    
    # Initialize enhanced services
    try:
        # Import services and store references for shutdown
        from app.services.enhanced_context_service import context_service
        from app.services.rag_service import rag_service
        from app.services.llm_tcc_service import llm_tcc_service
        
        # Store service references for shutdown
        services_to_shutdown = [context_service, rag_service, llm_tcc_service]
        
        # Log service status
        logger.info(f"Enhanced context service initialized: {type(context_service).__name__}")
        logger.info(f"RAG service initialized: {type(rag_service).__name__}")
        logger.info(f"LLM service initialized: {type(llm_tcc_service).__name__}")
        
        # Check for summarization availability
        if hasattr(context_service, 'summarization_available'):
            logger.info(f"Summarization {'available' if context_service.summarization_available else 'NOT available'}")
    
    except Exception as e:
        logger.error(f"Error during service initialization: {str(e)}")
        logger.error(traceback.format_exc())
    
    # Initialize enhanced services integration
    try:
        # Check if enhanced services config exists
        try:
            from app.init_enhanced_services import init_enhanced_services
            init_enhanced_services(app, config_path="app/config/enhanced_services.json")
            logger.info("Enhanced services configured with app integration")
        except (ImportError, FileNotFoundError):
            logger.info("Enhanced services integration not found, using basic setup")
    except Exception as e:
        logger.error(f"Error during enhanced services integration: {str(e)}")
    
    logger.info("All services started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down TCC Analysis Service...")
    await shutdown_services()

async def shutdown_services():
    """Properly shutdown all services to prevent 'cannot schedule new futures' errors."""
    global services_to_shutdown
    
    logger.info("Initiating graceful shutdown of all services...")
    
    # Shutdown services in parallel with timeout
    shutdown_tasks = []
    
    for service in services_to_shutdown:
        if service and hasattr(service, 'shutdown'):
            try:
                # Create shutdown task for each service
                async def shutdown_service(svc, name):
                    try:
                        if asyncio.iscoroutinefunction(svc.shutdown):
                            await svc.shutdown()
                        else:
                            svc.shutdown()
                        logger.info(f"Service {name} shutdown completed")
                    except Exception as e:
                        logger.error(f"Error shutting down service {name}: {str(e)}")
                
                service_name = type(service).__name__
                task = asyncio.create_task(shutdown_service(service, service_name))
                shutdown_tasks.append(task)
                
            except Exception as e:
                logger.error(f"Error creating shutdown task for {type(service).__name__}: {str(e)}")
    
    # Wait for all shutdown tasks to complete with timeout
    if shutdown_tasks:
        try:
            await asyncio.wait_for(
                asyncio.gather(*shutdown_tasks, return_exceptions=True),
                timeout=10  # 10 second timeout for all shutdowns
            )
            logger.info("All services shutdown completed")
        except asyncio.TimeoutError:
            logger.warning("Service shutdown timed out, forcing exit")
            # Cancel remaining tasks
            for task in shutdown_tasks:
                if not task.done():
                    task.cancel()
    
    # Clear service references
    services_to_shutdown.clear()
    logger.info("Service shutdown process completed")

def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.
    """
    application = FastAPI(
        title=settings.PROJECT_NAME,
        description="TCC Analysis Service API with Enhanced Context and RAG",
        version="1.2.1",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )

    # Add CORS middleware
    application.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Specify your allowed origins in production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include API router
    application.include_router(api_router)
    
    # Add middleware for request timing and logging
    @application.middleware("http")
    async def add_process_time_header(request: Request, call_next):
        start_time = time.time()
        try:
            response = await call_next(request)
            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(process_time)
            
            # Log with different levels based on response time
            if process_time > 2.0:
                logger.warning(f"SLOW REQUEST: {request.url.path} processed in {process_time:.4f} seconds")
            else:
                logger.info(f"Request to {request.url.path} processed in {process_time:.4f} seconds")
            
            return response
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(f"Request to {request.url.path} failed after {process_time:.4f} seconds: {str(e)}")
            raise

    return application

app = create_application()

# Signal handlers for graceful shutdown
def signal_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    
    # This will trigger the lifespan shutdown
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

@app.get("/health")
async def health_check():
    """Enhanced health check endpoint."""
    # Check for enhanced services
    has_enhanced_context = False
    has_summarizer = False
    has_rag_service = False
    service_health = {}
    
    try:
        from app.services.enhanced_context_service import context_service
        has_enhanced_context = True
        has_summarizer = hasattr(context_service, 'summarization_available') and context_service.summarization_available
        service_health["context_service"] = {
            "available": True,
            "summarization": has_summarizer
        }
    except Exception as e:
        service_health["context_service"] = {
            "available": False,
            "error": str(e)
        }
    
    try:
        from app.services.rag_service import rag_service
        has_rag_service = True
        metrics = rag_service.get_metrics()
        service_health["rag_service"] = {
            "available": True,
            "vector_search": rag_service.vector_search_available,
            "circuit_breaker_open": metrics.get("circuit_breaker_open", False),
            "error_count": metrics.get("error_count_current", 0)
        }
    except Exception as e:
        service_health["rag_service"] = {
            "available": False,
            "error": str(e)
        }
    
    try:
        from app.services.llm_tcc_service import llm_tcc_service
        service_health["llm_service"] = {
            "available": llm_tcc_service.client is not None,
            "circuit_breaker_open": llm_tcc_service._is_circuit_breaker_open() if hasattr(llm_tcc_service, '_is_circuit_breaker_open') else False
        }
    except Exception as e:
        service_health["llm_service"] = {
            "available": False,
            "error": str(e)
        }
    
    overall_status = "healthy" if all(
        service.get("available", False) and not service.get("circuit_breaker_open", False)
        for service in service_health.values()
    ) else "degraded"
    
    return {
        "status": overall_status,
        "version": "1.2.1",
        "enhanced_services": {
            "has_enhanced_context": has_enhanced_context,
            "has_summarizer": has_summarizer,
            "has_rag_service": has_rag_service
        },
        "service_health": service_health,
        "optimizations_enabled": True
    }

@app.get("/debug/services")
async def debug_services():
    """Debug endpoint for detailed service status."""
    try:
        from app.services.enhanced_context_service import context_service
        from app.services.rag_service import rag_service
        from app.services.llm_tcc_service import llm_tcc_service
        
        # Check summarizer
        has_summarizer = hasattr(context_service, 'summarization_available')
        summarization_available = context_service.summarization_available if has_summarizer else False
        
        # Get metrics
        rag_metrics = rag_service.get_metrics()
        
        return {
            "context_service_type": type(context_service).__name__,
            "has_summarizer": has_summarizer,
            "summarization_available": summarization_available,
            "rag_service_type": type(rag_service).__name__,
            "rag_metrics": rag_metrics,
            "llm_service_type": type(llm_tcc_service).__name__,
            "services_count": len(services_to_shutdown),
            "shutdown_handlers_registered": True
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "traceback": traceback.format_exc()
        }

@app.get("/metrics")
async def get_metrics():
    """Get metrics from all services."""
    metrics = {}
    
    try:
        from app.services.rag_service import rag_service
        metrics["rag"] = rag_service.get_metrics()
    except Exception as e:
        metrics["rag"] = {"error": str(e)}
    
    try:
        from app.services.enhanced_context_service import context_service
        if hasattr(context_service, 'summarization_stats'):
            metrics["context"] = context_service.summarization_stats
    except Exception as e:
        metrics["context"] = {"error": str(e)}
    
    return metrics

if __name__ == "__main__":
    import uvicorn
    
    # Configure uvicorn for graceful shutdown
    config = uvicorn.Config(
        "app.main:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=True,
        log_level="info",
        access_log=True
    )
    
    server = uvicorn.Server(config)
    
    try:
        server.run()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Server error: {str(e)}")
    finally:
        logger.info("Server shutdown complete")