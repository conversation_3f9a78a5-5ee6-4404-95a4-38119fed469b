"""
CosmosDB Container Initialization Tool
This tool verifies and initializes the required CosmosDB containers for the application.
"""

import asyncio
import logging
import os
import json
from azure.cosmos import CosmosClient, PartitionKey, exceptions
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Required containers with their configurations
REQUIRED_CONTAINERS = [
    {
        'id': 'ConversationContexts',
        'partitionKey': '/userId',
        'defaultTtl': -1  # No auto-expiration
    },
    {
        'id': 'RecentMessages',
        'partitionKey': '/userId',
        'defaultTtl': 2592000  # 30 days in seconds
    },
    {
        'id': 'SummarizedMessages',
        'partitionKey': '/userId',
        'defaultTtl': 7776000  # 90 days in seconds
    },
    {
        'id': 'PermanentMetadata',
        'partitionKey': '/userId',
        'defaultTtl': -1  # No auto-expiration
    },
    {
        'id': 'Users',
        'partitionKey': '/id',
        'defaultTtl': -1  # No auto-expiration
    },
    {
        'id': 'SessionIndex',
        'partitionKey': '/userId',
        'defaultTtl': -1  # No auto-expiration
    }
]

def get_cosmos_client(cosmos_endpoint: str, cosmos_key: str):
    """Initialize and return a CosmosDB client."""
    try:
        client = CosmosClient(url=cosmos_endpoint, credential=cosmos_key)
        logger.info(f"Successfully connected to CosmosDB at {cosmos_endpoint}")
        return client
    except Exception as e:
        logger.error(f"Failed to initialize CosmosDB client: {str(e)}")
        raise

def get_or_create_database(client: CosmosClient, database_name: str):
    """Get or create a database."""
    try:
        # Try to get the database
        database = client.get_database_client(database_name)
        # Test if it exists by reading its properties
        database.read()
        logger.info(f"Connected to existing database: {database_name}")
        return database
    except exceptions.CosmosResourceNotFoundError:
        # Create the database if it doesn't exist
        logger.info(f"Database {database_name} not found, creating...")
        database = client.create_database(id=database_name)
        logger.info(f"Created database: {database_name}")
        return database
    except Exception as e:
        logger.error(f"Error getting or creating database: {str(e)}")
        raise

def verify_container(database, container_config: Dict[str, Any]):
    """Verify if a container exists and has the correct configuration."""
    try:
        container_id = container_config['id']
        # Get the container
        container = database.get_container_client(container_id)
        # Test if it exists by reading its properties
        container_props = container.read()
        
        # Check partition key
        current_pk_path = container_props.get('partitionKey', {}).get('paths', [])[0]
        expected_pk_path = container_config['partitionKey']
        
        # Check TTL
        current_ttl = container_props.get('defaultTtl', None)
        expected_ttl = container_config.get('defaultTtl', None)
        
        if current_pk_path != expected_pk_path:
            logger.warning(f"Container {container_id} has incorrect partition key: {current_pk_path}, expected: {expected_pk_path}")
            return False
        
        if current_ttl != expected_ttl:
            logger.warning(f"Container {container_id} has incorrect TTL: {current_ttl}, expected: {expected_ttl}")
            # This can be updated
            try:
                container_props['defaultTtl'] = expected_ttl
                container.replace(container_props)
                logger.info(f"Updated TTL for container {container_id}")
            except Exception as e:
                logger.error(f"Failed to update TTL for container {container_id}: {str(e)}")
        
        logger.info(f"Container {container_id} exists and has correct configuration")
        return True
    except exceptions.CosmosResourceNotFoundError:
        logger.info(f"Container {container_id} does not exist")
        return False
    except Exception as e:
        logger.error(f"Error verifying container {container_config['id']}: {str(e)}")
        return False

def create_container(database, container_config: Dict[str, Any]):
    """Create a container with the specified configuration."""
    try:
        container_id = container_config['id']
        partition_key = container_config['partitionKey']
        ttl = container_config.get('defaultTtl', None)
        
        # Create container
        container_props = {
            'id': container_id,
            'partitionKey': {'paths': [partition_key], 'kind': 'Hash'}
        }
        
        if ttl is not None:
            container_props['defaultTtl'] = ttl
        
        logger.info(f"Creating container {container_id} with properties: {container_props}")
        container = database.create_container(**container_props)
        logger.info(f"Created container: {container_id}")
        return container
    except exceptions.CosmosResourceExistsError:
        logger.info(f"Container {container_id} already exists")
        return database.get_container_client(container_id)
    except Exception as e:
        logger.error(f"Error creating container {container_config['id']}: {str(e)}")
        raise

def verify_and_create_containers(database, required_containers: List[Dict[str, Any]]):
    """Verify all required containers and create missing ones."""
    for container_config in required_containers:
        container_id = container_config['id']
        try:
            if not verify_container(database, container_config):
                logger.info(f"Creating missing or incorrect container: {container_id}")
                create_container(database, container_config)
        except Exception as e:
            logger.error(f"Failed to verify or create container {container_id}: {str(e)}")

def list_containers(database):
    """List all containers in the database."""
    try:
        containers = list(database.list_containers())
        logger.info(f"Found {len(containers)} containers:")
        for container in containers:
            container_id = container['id']
            container_client = database.get_container_client(container_id)
            props = container_client.read()
            
            # Get item count with a query
            try:
                items = list(container_client.query_items(
                    query="SELECT VALUE COUNT(1) FROM c",
                    enable_cross_partition_query=True
                ))
                item_count = items[0] if items else 0
            except Exception as e:
                logger.error(f"Error counting items in {container_id}: {str(e)}")
                item_count = "Error counting"
            
            logger.info(f" - {container_id}: {item_count} items, partition key: {props.get('partitionKey', {}).get('paths', [])}, TTL: {props.get('defaultTtl', 'None')}")
    except Exception as e:
        logger.error(f"Error listing containers: {str(e)}")

def create_test_user(database, user_id="test_user_1", name="Test User", email="<EMAIL>"):
    """Create a test user to verify the Users container."""
    try:
        users_container = database.get_container_client("Users")
        
        # Create user document
        user_doc = {
            "id": user_id,
            "type": "user",
            "name": name,
            "email": email,
            "created": "2023-11-01T12:00:00Z",
            "preferences": {"theme": "light", "language": "fr"}
        }
        
        # Check if user already exists
        try:
            existing_user = users_container.read_item(item=user_id, partition_key=user_id)
            logger.info(f"Test user {user_id} already exists")
            return existing_user
        except exceptions.CosmosResourceNotFoundError:
            # Create the user
            result = users_container.create_item(body=user_doc)
            logger.info(f"Created test user: {user_id}")
            return result
    except Exception as e:
        logger.error(f"Error creating test user: {str(e)}")
        raise

async def main():
    """Main function to verify and initialize CosmosDB containers."""
    # Load environment variables (or from .env file)
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        logger.info("python-dotenv not installed, using environment variables directly")
    
    cosmos_endpoint = os.getenv("COSMOS_ENDPOINT")
    cosmos_key = os.getenv("COSMOS_KEY")
    database_name = os.getenv("COSMOS_DATABASE", "ContextDatabase")
    
    if not cosmos_endpoint or not cosmos_key:
        logger.error("COSMOS_ENDPOINT and COSMOS_KEY environment variables must be set")
        return
    
    try:
        # Initialize client
        client = get_cosmos_client(cosmos_endpoint, cosmos_key)
        
        # Get or create database
        database = get_or_create_database(client, database_name)
        
        # Verify and create containers
        verify_and_create_containers(database, REQUIRED_CONTAINERS)
        
        # List all containers
        list_containers(database)
        
        # Create a test user
        create_test_user(database)
        
        logger.info("CosmosDB initialization complete")
    except Exception as e:
        logger.error(f"Error during CosmosDB initialization: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())