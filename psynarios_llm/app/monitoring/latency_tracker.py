# ==========================================
# FILE: app/monitoring/simple_tracker.py
# ==========================================
"""
Ultra-simple latency tracker that appends to one file after every request.
"""

import time
import json
import threading
from datetime import datetime
from typing import Dict, Any
from contextlib import contextmanager, asynccontextmanager
from pathlib import Path

class LatencyTracker:
    """Append-only latency tracker - one file, append after every execution."""
    
    def __init__(self, output_file: str = "reports/latency_log_optimized.jsonl"):
        """
        Initialize tracker.
        
        Args:
            output_file: Single file to append all results (.jsonl format)
        """
        self.output_file = Path(output_file)
        self.output_file.parent.mkdir(exist_ok=True)
        
        self.lock = threading.Lock()
        
        # Create file with header if it doesn't exist
        if not self.output_file.exists():
            with open(self.output_file, 'w') as f:
                f.write(f'# Latency Log Started: {datetime.now().isoformat()}\n')
    
    def record(self, component: str, operation: str, duration_ms: float, success: bool = True, **metadata):
        """Record and immediately append to file."""
        record = {
            "timestamp": datetime.now().isoformat(),
            "component": component,
            "operation": operation,
            "duration_ms": round(duration_ms, 2),
            "success": success,
            **metadata
        }
        
        # Immediately append to file
        with self.lock:
            with open(self.output_file, 'a') as f:
                f.write(json.dumps(record) + '\n')
    
    @contextmanager
    def track(self, component: str, operation: str, **metadata):
        """Context manager for tracking sync operations."""
        start = time.time()
        success = True
        try:
            yield
        except Exception:
            success = False
            raise
        finally:
            duration = (time.time() - start) * 1000
            self.record(component, operation, duration, success, **metadata)
    
    @asynccontextmanager
    async def track_async(self, component: str, operation: str, **metadata):
        """Context manager for tracking async operations."""
        start = time.time()
        success = True
        try:
            yield
        except Exception:
            success = False
            raise
        finally:
            duration = (time.time() - start) * 1000
            self.record(component, operation, duration, success, **metadata)
    
    def get_file_path(self) -> str:
        """Get the current log file path."""
        return str(self.output_file)

# Global instance
tracker = LatencyTracker()

# Convenience functions
def track_sync(component: str, operation: str, **metadata):
    return tracker.track(component, operation, **metadata)

def track_async(component: str, operation: str, **metadata):
    return tracker.track_async(component, operation, **metadata)

def get_log_file():
    return tracker.get_file_path()