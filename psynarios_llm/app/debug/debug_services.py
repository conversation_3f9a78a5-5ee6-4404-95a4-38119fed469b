# debug_services.py
import asyncio
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def check_enhanced_services():
    try:
        # Try to import and get the enhanced context service
        from app.services.enhanced_services import get_context_service, get_rag_service
        
        # Get the services
        context_service = get_context_service()
        rag_service = get_rag_service()
        
        # Check if LLM summarizer is available
        has_summarizer = hasattr(context_service, 'llm_summarizer')
        
        logger.info(f"Enhanced context service type: {type(context_service).__name__}")
        logger.info(f"Has LLM summarizer: {has_summarizer}")
        
        if has_summarizer:
            logger.info(f"LLM summarizer type: {type(context_service.llm_summarizer).__name__}")
        
        # Print key attributes
        logger.info(f"Context service attributes: {[attr for attr in dir(context_service) if not attr.startswith('_')]}")
        
        return {
            "context_service": type(context_service).__name__,
            "has_summarizer": has_summarizer,
            "result": "success"
        }
    except Exception as e:
        logger.error(f"Debug error: {str(e)}")
        return {
            "error": str(e),
            "result": "error"
        }



if __name__ == "__main__":
    result = asyncio.run(check_enhanced_services())
    print("\nRESULT:", result)