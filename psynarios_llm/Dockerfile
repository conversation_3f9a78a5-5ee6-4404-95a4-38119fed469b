FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    nginx \
    certbot \
    python3-certbot-nginx \
    supervisor \
    curl \
    cron \
    vim \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Create necessary directories
RUN mkdir -p /var/www/html/.well-known/acme-challenge \
    && mkdir -p /var/log/supervisor \
    && mkdir -p /etc/supervisor/conf.d

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Copy configuration files
COPY docker/nginx-initial.conf /etc/nginx/sites-available/default
COPY docker/nginx-ssl.conf /etc/nginx/nginx-ssl.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/ssl-setup.sh /usr/local/bin/ssl-setup.sh
COPY docker/ssl-renew.sh /usr/local/bin/ssl-renew.sh
COPY docker/entrypoint.sh /usr/local/bin/entrypoint.sh

# Make scripts executable
RUN chmod +x /usr/local/bin/ssl-setup.sh \
    && chmod +x /usr/local/bin/ssl-renew.sh \
    && chmod +x /usr/local/bin/entrypoint.sh

# Remove default nginx config
RUN rm -f /etc/nginx/sites-enabled/default \
    && ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Expose ports
EXPOSE 80 443

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Start entrypoint script
CMD ["/usr/local/bin/entrypoint.sh"]