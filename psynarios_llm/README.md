# DeepSeek TCC Analysis Service

A FastAPI-based service for TCC (Thérapie Cognitivo-Comportementale) analysis using DeepSeek LLM on Azure with RAG (Retrieval-Augmented Generation) capabilities.

## Features

- TCC analysis of user reactions in professional scenarios
- RAG integration with Azure AI Search
- Multiple analysis steps (behavior, impacts, sources, strategies)
- Simple web UI for testing

## Setup

1. Copy `.env.example` to `.env` and fill in your Azure credentials
2. Install dependencies: `pip install -r requirements.txt`
3. Run the application: `uvicorn app.main:app --reload`

## API Usage

Send POST requests to `/api/tcc/analyze` with scenario and reaction data.

## Docker Deployment

```bash
docker-compose up -d