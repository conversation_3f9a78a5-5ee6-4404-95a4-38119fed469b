#!/bin/bash
set -e

DOMAIN="${DOMAIN:-llm.gridmindstudiolines.com}"
EMAIL="${EMAIL:-<EMAIL>}"

echo "🚀 Starting SSL setup for domain: $DOMAIN"
echo "📧 Using email: $EMAIL"

# Validate environment variables
if [ "$DOMAIN" = "api.yourdomain.com" ] || [ "$EMAIL" = "<EMAIL>" ]; then
    echo "❌ Please set DOMAIN and EMAIL environment variables!"
    echo "   Example: docker run -e DOMAIN=api.mydomain.com -e EMAIL=<EMAIL> ..."
    exit 1
fi

# Create necessary directories
mkdir -p /var/www/html/.well-known/acme-challenge
mkdir -p /var/log/supervisor

# Test nginx configuration
echo "🔧 Testing nginx configuration..."
nginx -t

# Start services with supervisor in background
echo "🚀 Starting services..."
supervisord -c /etc/supervisor/conf.d/supervisord.conf &

# Wait for FastAPI to start
echo "⏳ Waiting for FastAPI to start..."
for i in {1..120}; do
    if curl -s http://localhost:8000/health > /dev/null 2>&1; then
        echo "✅ FastAPI is running"
        break
    fi
    echo "⏳ Waiting for FastAPI... ($i/30)"
    sleep 2
done

# Wait for nginx to start
echo "⏳ Waiting for nginx to start..."
for i in {1..30}; do
    if curl -s http://localhost/health > /dev/null 2>&1; then
        echo "✅ Nginx is running"
        break
    fi
    echo "⏳ Waiting for nginx... ($i/30)"
    sleep 2
done

# Check if certificate already exists
if [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
    echo "✅ SSL certificate already exists for $DOMAIN"
    # Update nginx configuration with SSL
    sed "s/DOMAIN_PLACEHOLDER/$DOMAIN/g" /etc/nginx/nginx-ssl.conf > /etc/nginx/sites-available/default
    nginx -s reload
    echo "🔄 Nginx reloaded with SSL configuration"
else
    echo "🔒 Obtaining SSL certificate for $DOMAIN..."
    
    # Obtain certificate using certbot
    certbot certonly \
        --webroot \
        --webroot-path=/var/www/html \
        --email $EMAIL \
        --agree-tos \
        --no-eff-email \
        --non-interactive \
        --staging \
        -d $DOMAIN || {
        
        echo "🔄 Staging failed, trying production..."
        certbot certonly \
            --webroot \
            --webroot-path=/var/www/html \
            --email $EMAIL \
            --agree-tos \
            --no-eff-email \
            --non-interactive \
            -d $DOMAIN
    }
    
    if [ $? -eq 0 ] && [ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]; then
        echo "✅ SSL certificate obtained successfully!"
        
        # Update nginx configuration with SSL
        echo "🔧 Updating nginx configuration with SSL..."
        sed "s/DOMAIN_PLACEHOLDER/$DOMAIN/g" /etc/nginx/nginx-ssl.conf > /etc/nginx/sites-available/default
        
        # Test new configuration
        nginx -t
        
        # Reload nginx with SSL configuration
        echo "🔄 Reloading nginx with SSL..."
        nginx -s reload
        
        echo "🎉 SSL setup complete!"
    else
        echo "❌ Failed to obtain SSL certificate"
        echo "🔍 Check the logs for more details:"
        echo "   - Certbot logs: /var/log/letsencrypt/"
        echo "   - Nginx logs: /var/log/nginx/"
        exit 1
    fi
fi

# Setup certificate auto-renewal
echo "⏰ Setting up certificate auto-renewal..."
echo "0 12 * * * /usr/local/bin/ssl-renew.sh >> /var/log/cron.log 2>&1" | crontab -

echo "✅ SSL setup completed successfully!"
echo "🔒 Your API is available at: https://$DOMAIN"
echo "📊 Health check: https://$DOMAIN/health"

# Keep the container running
wait