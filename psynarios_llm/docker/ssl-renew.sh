#!/bin/bash
set -e

DOMAIN="${DOMAIN:-llm.gridmindstudiolines.com}"

echo "🔄 Starting SSL certificate renewal for $DOMAIN..."
echo "📅 $(date)"

# Renew certificates
certbot renew --quiet --no-self-upgrade

# Check if certificates were renewed
if [ $? -eq 0 ]; then
    echo "✅ Certificate renewal completed"
    
    # Reload nginx to use new certificates
    echo "🔄 Reloading nginx..."
    nginx -s reload
    
    echo "🎉 SSL renewal successful for $DOMAIN"
else
    echo "❌ SSL renewal failed for $DOMAIN"
    exit 1
fi

# Log renewal attempt
echo "$(date): SSL renewal completed for $DOMAIN" >> /var/log/ssl-renewal.log