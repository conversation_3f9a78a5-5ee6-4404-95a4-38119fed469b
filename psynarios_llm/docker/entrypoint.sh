#!/bin/bash
set -e

echo "🚀 Starting Psynarios API with SSL..."

# Create log directories
mkdir -p /var/log/supervisor
mkdir -p /var/log/nginx
mkdir -p /var/log/letsencrypt

# Set proper permissions
chown -R www-data:www-data /var/www/html
chmod -R 755 /var/www/html

# Test nginx configuration
echo "🔧 Testing initial nginx configuration..."
nginx -t

# Start SSL setup
echo "🔒 Starting SSL setup..."
exec /usr/local/bin/ssl-setup.sh