CREATE TABLE interaction_behaviours (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interaction_id UUID NOT NULL REFERENCES interactions(id) ON DELETE CASCADE,
    behaviour_id UUID NOT NULL REFERENCES behaviours(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(interaction_id, behaviour_id)
);

CREATE INDEX idx_interaction_behaviours_interaction_id ON interaction_behaviours(interaction_id);
CREATE INDEX idx_interaction_behaviours_behaviour_id ON interaction_behaviours(behaviour_id);