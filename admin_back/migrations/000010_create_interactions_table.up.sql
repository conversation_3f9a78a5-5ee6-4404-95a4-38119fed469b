CREATE TABLE interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) NOT NULL UNIQUE,
    character_id UUID NOT NULL REFERENCES characters(id),
    scenario_id UUID NOT NULL REFERENCES scenarios(id),
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_interactions_code ON interactions(code);
CREATE INDEX idx_interactions_scenario_id ON interactions(scenario_id);
CREATE INDEX idx_interactions_character_id ON interactions(character_id);
CREATE INDEX idx_interactions_is_active ON interactions(is_active);
CREATE INDEX idx_interactions_created_by ON interactions(created_by);