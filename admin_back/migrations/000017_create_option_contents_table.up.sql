CREATE TABLE option_contents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    option_id UUID NOT NULL REFERENCES options(id) ON DELETE CASCADE,
    language_id INTEGER NOT NULL REFERENCES languages(id),
    text TEXT NOT NULL,
    hint TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(option_id, language_id)
);

CREATE INDEX idx_option_contents_option_id ON option_contents(option_id);
CREATE INDEX idx_option_contents_language_id ON option_contents(language_id);