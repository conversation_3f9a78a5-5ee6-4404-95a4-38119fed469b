CREATE TABLE behaviour_contents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    behaviour_id UUID NOT NULL REFERENCES behaviours(id) ON DELETE CASCADE,
    language_id INTEGER NOT NULL REFERENCES languages(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(behaviour_id, language_id)
);

CREATE INDEX idx_behaviour_contents_behaviour_id ON behaviour_contents(behaviour_id);
CREATE INDEX idx_behaviour_contents_language_id ON behaviour_contents(language_id);