CREATE TABLE interaction_contents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interaction_id UUID NOT NULL REFERENCES interactions(id) ON DELETE CASCADE,
    language_id INTEGER NOT NULL REFERENCES languages(id),
    instruction TEXT,
    correction TEXT,
    question TEXT,
    instruction_video_url TEXT,
    correction_video_url TEXT,
    additional_media_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(interaction_id, language_id)
);

CREATE INDEX idx_interaction_contents_interaction_id ON interaction_contents(interaction_id);
CREATE INDEX idx_interaction_contents_language_id ON interaction_contents(language_id);
CREATE INDEX idx_interaction_contents_instruction_video_url ON interaction_contents(instruction_video_url) WHERE instruction_video_url IS NOT NULL;
CREATE INDEX idx_interaction_contents_correction_video_url ON interaction_contents(correction_video_url) WHERE correction_video_url IS NOT NULL;
CREATE INDEX idx_interaction_contents_additional_media_url ON interaction_contents(additional_media_url) WHERE additional_media_url IS NOT NULL;
