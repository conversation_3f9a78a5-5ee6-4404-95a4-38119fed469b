-- 1. Update scenarios table to remove localized fields
ALTER TABLE scenarios DROP COLUMN IF EXISTS title;
ALTER TABLE scenarios DROP COLUMN IF EXISTS description;
ALTER TABLE scenarios DROP COLUMN IF EXISTS pt;
ALTER TABLE scenarios DROP COLUMN IF EXISTS image;
ALTER TABLE scenarios DROP COLUMN IF EXISTS intro;
ALTER TABLE scenarios DROP COLUMN IF EXISTS closing;
ALTER TABLE scenarios DROP COLUMN IF EXISTS bt;
ALTER TABLE scenarios DROP COLUMN IF EXISTS pm;
ALTER TABLE scenarios DROP COLUMN IF EXISTS demo;
ALTER TABLE scenarios DROP COLUMN IF EXISTS di;

-- Add proper structure to scenarios
ALTER TABLE scenarios ADD COLUMN IF NOT EXISTS code VARCHAR(50) NOT NULL DEFAULT '';
ALTER TABLE scenarios ADD COLUMN IF NOT EXISTS difficulty_level VARCHAR(20) CHECK (difficulty_level IN ('Débutant', 'Confirmé', 'Expérimenté'));
ALTER TABLE scenarios ADD COLUMN IF NOT EXISTS scenario_type VARCHAR(50) CHECK (scenario_type IN ('Situations Interpersonnelles', 'Dialogue Intérieur', 'Nuances Perceptives'));
ALTER TABLE scenarios ADD COLUMN IF NOT EXISTS character_type VARCHAR(20) CHECK (character_type IN ('Character', 'Narrator'));
ALTER TABLE scenarios ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE scenarios ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES users(id);

-- Add unique constraint on code (PostgreSQL compatible)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'scenarios_code_unique'
    ) THEN
        ALTER TABLE scenarios ADD CONSTRAINT scenarios_code_unique UNIQUE (code);
    END IF;
END
$$;

-- 2. Update characters table to remove localized fields
ALTER TABLE characters DROP COLUMN IF EXISTS name;
ALTER TABLE characters DROP COLUMN IF EXISTS role;
ALTER TABLE characters DROP COLUMN IF EXISTS photo;
ALTER TABLE characters DROP COLUMN IF EXISTS scenarios;
ALTER TABLE characters DROP COLUMN IF EXISTS interactions;
ALTER TABLE characters DROP COLUMN IF EXISTS stats;

-- Add proper structure to characters
ALTER TABLE characters ADD COLUMN IF NOT EXISTS code VARCHAR(50) NOT NULL DEFAULT '';
ALTER TABLE characters ADD COLUMN IF NOT EXISTS character_type VARCHAR(20) CHECK (character_type IN ('Character', 'Narrator'));
ALTER TABLE characters ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE characters ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES users(id);

-- Add unique constraint on code
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'characters_code_unique'
    ) THEN
        ALTER TABLE characters ADD CONSTRAINT characters_code_unique UNIQUE (code);
    END IF;
END
$$;

-- 3. Update interactions table to remove localized fields
ALTER TABLE interactions DROP COLUMN IF EXISTS instruction;
ALTER TABLE interactions DROP COLUMN IF EXISTS correction;
ALTER TABLE interactions DROP COLUMN IF EXISTS question;
ALTER TABLE interactions DROP COLUMN IF EXISTS additional_media;
ALTER TABLE interactions DROP COLUMN IF EXISTS responses;
ALTER TABLE interactions DROP COLUMN IF EXISTS behaviour_id;

-- Add proper structure to interactions
ALTER TABLE interactions ADD COLUMN IF NOT EXISTS code VARCHAR(50) NOT NULL DEFAULT '';
ALTER TABLE interactions ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE interactions ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES users(id);

-- Add unique constraint on code
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'interactions_code_unique'
    ) THEN
        ALTER TABLE interactions ADD CONSTRAINT interactions_code_unique UNIQUE (code);
    END IF;
END
$$;

-- 4. Update behaviours table to remove localized fields
ALTER TABLE behaviours DROP COLUMN IF EXISTS title;
ALTER TABLE behaviours DROP COLUMN IF EXISTS interactions;
ALTER TABLE behaviours DROP COLUMN IF EXISTS scenarios;

-- Add proper structure to behaviours
ALTER TABLE behaviours ADD COLUMN IF NOT EXISTS code VARCHAR(50) NOT NULL DEFAULT '';
ALTER TABLE behaviours ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE behaviours ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES users(id);

-- Add unique constraint on code
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'behaviours_code_unique'
    ) THEN
        ALTER TABLE behaviours ADD CONSTRAINT behaviours_code_unique UNIQUE (code);
    END IF;
END
$$;

-- 5. Update options table to remove localized fields
ALTER TABLE options DROP COLUMN IF EXISTS text;
ALTER TABLE options DROP COLUMN IF EXISTS hint;

-- Add proper structure to options
ALTER TABLE options ADD COLUMN IF NOT EXISTS code VARCHAR(50) NOT NULL DEFAULT '';
ALTER TABLE options ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
ALTER TABLE options ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES users(id);

-- Add unique constraint on code
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'options_code_unique'
    ) THEN
        ALTER TABLE options ADD CONSTRAINT options_code_unique UNIQUE (code);
    END IF;
END
$$;