CREATE TABLE character_contents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    character_id UUID NOT NULL REFERENCES characters(id) ON DELETE CASCADE,
    language_id INTEGER NOT NULL REFERENCES languages(id),
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    role VARCHAR(255),
    bio TEXT,
    stats JSONB,
    photo_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(character_id, language_id)
);

CREATE INDEX idx_character_contents_character_id ON character_contents(character_id);
CREATE INDEX idx_character_contents_language_id ON character_contents(language_id);
