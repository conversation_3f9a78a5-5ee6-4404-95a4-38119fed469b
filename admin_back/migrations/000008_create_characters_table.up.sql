CREATE TABLE characters (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) NOT NULL UNIQUE,
    character_type VARCHAR(20) NOT NULL CHECK (character_type IN ('Character', 'Narrator')),
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_characters_code ON characters(code);
CREATE INDEX idx_characters_character_type ON characters(character_type);
CREATE INDEX idx_characters_is_active ON characters(is_active);
CREATE INDEX idx_characters_created_by ON characters(created_by);