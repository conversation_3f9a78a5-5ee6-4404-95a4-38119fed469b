CREATE TABLE IF NOT EXISTS companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    connection_string VARCHAR(1024) NOT NULL,
    database_type VARCHAR(50) NOT NULL,
    use_ssl BOOLEAN NOT NULL DEFAULT FALSE,
    url TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_created_at ON companies(created_at);