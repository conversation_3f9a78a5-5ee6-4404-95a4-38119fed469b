CREATE TABLE scenario_contents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    scenario_id UUID NOT NULL REFERENCES scenarios(id) ON DELETE CASCADE,
    language_id INTEGER NOT NULL REFERENCES languages(id),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT,
    intro_video_url TEXT,
    closing_video_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(scenario_id, language_id)
);

CREATE INDEX idx_scenario_contents_scenario_id ON scenario_contents(scenario_id);
CREATE INDEX idx_scenario_contents_language_id ON scenario_contents(language_id);