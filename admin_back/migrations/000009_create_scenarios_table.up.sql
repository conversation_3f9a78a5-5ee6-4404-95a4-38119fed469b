CREATE TABLE scenarios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) NOT NULL UNIQUE,
    difficulty_level VARCHAR(20) NOT NULL CHECK (difficulty_level IN ('Débutant', 'Confirmé', 'Expérimenté')),
    scenario_type VARCHAR(50) NOT NULL CHECK (scenario_type IN ('Situations Interpersonnelles', 'Dialogue Intérieur', 'Nuances Perceptives')),
    character_type VARCHAR(20) NOT NULL CHECK (character_type IN ('Character', 'Narrator')),
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_scenarios_code ON scenarios(code);
CREATE INDEX idx_scenarios_difficulty_level ON scenarios(difficulty_level);
CREATE INDEX idx_scenarios_scenario_type ON scenarios(scenario_type);
CREATE INDEX idx_scenarios_character_type ON scenarios(character_type);
CREATE INDEX idx_scenarios_is_active ON scenarios(is_active);
CREATE INDEX idx_scenarios_created_by ON scenarios(created_by);