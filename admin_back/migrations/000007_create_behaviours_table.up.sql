CREATE TABLE behaviours (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_behaviours_code ON behaviours(code);
CREATE INDEX idx_behaviours_is_active ON behaviours(is_active);
CREATE INDEX idx_behaviours_created_by ON behaviours(created_by);