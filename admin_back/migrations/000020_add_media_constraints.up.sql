ALTER TABLE media_files ADD CONSTRAINT check_file_size 
CHECK (file_size > 0 AND file_size <= 354857600);

ALTER TABLE media_files ADD CONSTRAINT check_dimensions_format 
CHECK (dimensions IS NULL OR (dimensions ? 'width' AND dimensions ? 'height'));

-- Create indexes for media URL fields (use IF NOT EXISTS to avoid conflicts)
CREATE INDEX IF NOT EXISTS idx_scenario_contents_image_url 
ON scenario_contents(image_url) WHERE image_url IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_scenario_contents_intro_video_url 
ON scenario_contents(intro_video_url) WHERE intro_video_url IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_scenario_contents_closing_video_url 
ON scenario_contents(closing_video_url) WHERE closing_video_url IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_character_contents_photo_url 
ON character_contents(photo_url) WHERE photo_url IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_interaction_contents_instruction_video_url 
ON interaction_contents(instruction_video_url) WHERE instruction_video_url IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_interaction_contents_correction_video_url 
ON interaction_contents(correction_video_url) WHERE correction_video_url IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_interaction_contents_additional_media_url 
ON interaction_contents(additional_media_url) WHERE additional_media_url IS NOT NULL;