CREATE TABLE options (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(50) NOT NULL UNIQUE,
    is_correct BOOLEAN NOT NULL DEFAULT false,
    interaction_id UUID NOT NULL REFERENCES interactions(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_options_code ON options(code);
CREATE INDEX idx_options_interaction_id ON options(interaction_id);
CREATE INDEX idx_options_is_correct ON options(is_correct);
CREATE INDEX idx_options_is_active ON options(is_active);
CREATE INDEX idx_options_created_by ON options(created_by);