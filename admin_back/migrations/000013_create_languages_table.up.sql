CREATE TABLE languages (
    id SERIAL PRIMARY KEY,
    code VA<PERSON>HAR(5) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    native_name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default languages
INSERT INTO languages (code, name, native_name, is_default) VALUES
('en', 'English', 'English', true),
('fr', 'French', 'Français', false),
('ar', 'Arabic', 'العربية', false);


CREATE UNIQUE INDEX idx_languages_default ON languages(is_default) WHERE is_default = true;