version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: admin-dashboard-api
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      # Application Environment
      - APP_ENV=${APP_ENV:-development}
      
      # Server Configuration
      - SERVER_HOST=${SERVER_HOST:-0.0.0.0}
      - SERVER_PORT=${SERVER_PORT:-8080}
      - SERVER_DOMAIN=${SERVER_DOMAIN:-localhost}
      - SERVER_READ_TIMEOUT=${SERVER_READ_TIMEOUT:-15s}
      - SERVER_WRITE_TIMEOUT=${SERVER_WRITE_TIMEOUT:-15s}
      - SERVER_IDLE_TIMEOUT=${SERVER_IDLE_TIMEOUT:-60s}
      
      # Database Configuration (Docker specific)
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=postgrespassword
      - DB_NAME=admin_dashboard
      - DB_SSLMODE=disable
      
      # Authentication Configuration
      - AUTH_ACCESS_SECRET=${AUTH_ACCESS_SECRET:-your-super-secret-access-key-minimum-32-characters-long-change-in-production}
      - AUTH_REFRESH_SECRET=${AUTH_REFRESH_SECRET:-your-super-secret-refresh-key-minimum-32-characters-long-change-in-production}
      - AUTH_ACCESS_LIFETIME=${AUTH_ACCESS_LIFETIME:-15m}
      - AUTH_REFRESH_LIFETIME=${AUTH_REFRESH_LIFETIME:-168h}
      - AUTH_REFRESH_THRESHOLD=${AUTH_REFRESH_THRESHOLD:-5m}
      
      # Cookie Configuration (Development settings)
      - AUTH_COOKIE_DOMAIN=${AUTH_COOKIE_DOMAIN:-}
      - AUTH_COOKIE_SECURE=${AUTH_COOKIE_SECURE:-false}
      - AUTH_COOKIE_HTTP_ONLY=${AUTH_COOKIE_HTTP_ONLY:-true}
      - AUTH_COOKIE_SAME_SITE=${AUTH_COOKIE_SAME_SITE:-Lax}
      
      # CSRF Configuration
      - AUTH_CSRF_ENABLED=${AUTH_CSRF_ENABLED:-true}
      - AUTH_CSRF_SECRET=${AUTH_CSRF_SECRET:-your-csrf-secret-minimum-16-characters}
      
      # Rate Limiting
      - AUTH_LOGIN_RATE_LIMIT=${AUTH_LOGIN_RATE_LIMIT:-5}
      - AUTH_REFRESH_RATE_LIMIT=${AUTH_REFRESH_RATE_LIMIT:-10}
      
      # Email Configuration (Using MailHog for development)
      - EMAIL_DEFAULT_SENDER=${EMAIL_DEFAULT_SENDER:-noreply@localhost}
      - EMAIL_DEFAULT_NAME=${EMAIL_DEFAULT_NAME:-Admin Dashboard}
      - EMAIL_SMTP_HOST=mailhog
      - EMAIL_SMTP_PORT=1025
      - EMAIL_SMTP_USERNAME=${EMAIL_SMTP_USERNAME:-}
      - EMAIL_SMTP_PASSWORD=${EMAIL_SMTP_PASSWORD:-}
      - EMAIL_USE_SSL=${EMAIL_USE_SSL:-false}
      - EMAIL_CONNECT_TIMEOUT=${EMAIL_CONNECT_TIMEOUT:-10s}
      
      # Logging Configuration
      - LOG_LEVEL=${LOG_LEVEL:-debug}
      - LOG_TIME_FORMAT=${LOG_TIME_FORMAT:-2006-01-02T15:04:05Z07:00}
      - LOG_CONSOLE=${LOG_CONSOLE:-true}
      - LOG_FILE=${LOG_FILE:-}
      
      # Storage Configuration - Fixed paths
      - STORAGE_BASE_PATH=/app/uploads
      - STORAGE_BASE_URL=${STORAGE_BASE_URL:-/api/media/files}
      - STORAGE_MAX_FILE_SIZE=${STORAGE_MAX_FILE_SIZE:-104857600}
      
      # Encryption (if you're using it)
      - ENCRYPTION_KEY=${ENCRYPTION_KEY:-default-encryption-key-32-bytes-lon}
      
    volumes:
      - ./certs:/app/certs:ro  # Read-only for certificates
      - uploads_data:/app/uploads  # Use named volume for uploads
      - logs_data:/app/logs        # Use named volume for logs
    restart: unless-stopped
    networks:
      - admin-network
    # Add healthcheck for better container management
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:16-alpine
    container_name: admin-dashboard-postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgrespassword
      - POSTGRES_DB=admin_dashboard
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d:ro  # Read-only for init scripts
    restart: unless-stopped
    networks:
      - admin-network
    # Add healthcheck for PostgreSQL
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d admin_dashboard"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  migrations:
    image: migrate/migrate
    container_name: admin-dashboard-migrations
    volumes:
      - ./migrations:/migrations:ro
    command: 
      - "-path"
      - "/migrations"
      - "-database"
      - "**************************************************/admin_dashboard?sslmode=disable"
      - "up"
    depends_on:
      postgres:
        condition: service_healthy
    restart: on-failure
    networks:
      - admin-network

  mailhog:
    image: mailhog/mailhog
    container_name: admin-dashboard-mailhog
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - admin-network
    restart: unless-stopped

  # Optional: Add Redis for caching/sessions (if needed later)
  # redis:
  #   image: redis:7-alpine
  #   container_name: admin-dashboard-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped
  #   networks:
  #     - admin-network
  #   command: redis-server --appendonly yes

networks:
  admin-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  uploads_data:
    driver: local
  logs_data:
    driver: local
  # redis_data:
  #   driver: local