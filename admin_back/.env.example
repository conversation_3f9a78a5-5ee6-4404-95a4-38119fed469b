# .env - Development Configuration for Docker
# Copy this file and customize for your environment

# ===========================================
# APPLICATION ENVIRONMENT
# ===========================================
APP_ENV=development

# ===========================================
# SERVER CONFIGURATION
# ===========================================
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_DOMAIN=localhost
SERVER_READ_TIMEOUT=15s
SERVER_WRITE_TIMEOUT=15s
SERVER_IDLE_TIMEOUT=60s

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
# Note: When running locally (not in Docker), use these settings:
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=admin_back
DB_SSLMODE=disable

# When running in Docker, the API container will override DB_HOST to 'postgres'

# ===========================================
# AUTHENTICATION CONFIGURATION
# ===========================================
# IMPORTANT: Change these secrets in production!
# Generate strong secrets: openssl rand -base64 48
AUTH_ACCESS_SECRET=your-super-secret-access-key-minimum-32-characters-long-change-in-production
AUTH_REFRESH_SECRET=your-super-secret-refresh-key-minimum-32-characters-long-change-in-production

# Token Lifetimes
AUTH_ACCESS_LIFETIME=15m
AUTH_REFRESH_LIFETIME=168h  # 7 days
AUTH_REFRESH_THRESHOLD=5m   # Refresh proactively when token expires in 5 minutes

# Cookie Configuration (Development settings)
AUTH_COOKIE_DOMAIN=          # Leave empty for localhost in development
AUTH_COOKIE_SECURE=false    # Set to true in production with HTTPS
AUTH_COOKIE_HTTP_ONLY=true
AUTH_COOKIE_SAME_SITE=Lax   # Options: Strict, Lax, None

# CSRF Configuration
AUTH_CSRF_ENABLED=true
AUTH_CSRF_SECRET=your-csrf-secret-minimum-16-characters

# Rate Limiting
AUTH_LOGIN_RATE_LIMIT=5     # Max login attempts per minute per IP
AUTH_REFRESH_RATE_LIMIT=10  # Max refresh attempts per minute per IP

# ===========================================
# EMAIL CONFIGURATION
# ===========================================
# Development - using MailHog (running in Docker)
EMAIL_DEFAULT_SENDER=noreply@localhost
EMAIL_DEFAULT_NAME=Admin Dashboard
EMAIL_SMTP_HOST=localhost   # When running locally, set to localhost
EMAIL_SMTP_PORT=1025       # MailHog SMTP port
EMAIL_SMTP_USERNAME=
EMAIL_SMTP_PASSWORD=
EMAIL_USE_SSL=false
EMAIL_CONNECT_TIMEOUT=10s

# Production email settings (commented out for development)
# EMAIL_SMTP_HOST=smtp.gmail.com
# EMAIL_SMTP_PORT=587
# EMAIL_SMTP_USERNAME=<EMAIL>
# EMAIL_SMTP_PASSWORD=your-app-password
# EMAIL_USE_SSL=true

# ===========================================
# LOGGING CONFIGURATION
# ===========================================
LOG_LEVEL=debug             # Use 'debug' for development, 'info' for production
LOG_TIME_FORMAT=2006-01-02T15:04:05Z07:00
LOG_CONSOLE=true
LOG_FILE=                   # Leave empty to disable file logging

# ===========================================
# STORAGE CONFIGURATION
# ===========================================
STORAGE_BASE_PATH=./uploads
STORAGE_BASE_URL=/api/media/files
STORAGE_MAX_FILE_SIZE=104857600  # 100MB in bytes

# ===========================================
# SECURITY CONFIGURATION
# ===========================================
# Generate with: openssl rand -base64 32
ENCRYPTION_KEY=default-encryption-key-32-bytes-lon

# ===========================================
# DEVELOPMENT OVERRIDES
# ===========================================
# These are used for local development outside Docker

# Local PostgreSQL (when not using Docker)
# DB_HOST=localhost
# DB_PASSWORD=your-local-postgres-password

# Local email testing (when not using Docker MailHog)
# EMAIL_SMTP_HOST=localhost

# ===========================================
# PRODUCTION TEMPLATE (commented out)
# ===========================================
# Uncomment and modify these for production deployment

# APP_ENV=production
# SERVER_DOMAIN=yourdomain.com
# AUTH_COOKIE_SECURE=true
# AUTH_COOKIE_DOMAIN=.yourdomain.com
# AUTH_COOKIE_SAME_SITE=Strict
# LOG_LEVEL=info
# LOG_FILE=/var/log/admin-dashboard/app.log

# Production secrets (generate strong ones!)
# AUTH_ACCESS_SECRET=randomly-generated-64-character-secret-for-access-tokens
# AUTH_REFRESH_SECRET=randomly-generated-64-character-secret-for-refresh-tokens
# AUTH_CSRF_SECRET=randomly-generated-32-character-csrf-secret
# ENCRYPTION_KEY=randomly-generated-32-character-encryption-key

# Production database
# DB_HOST=your-production-db-host
# DB_PASSWORD=your-secure-db-password
# DB_SSLMODE=require

# Production email
# EMAIL_SMTP_HOST=your-smtp-provider.com
# EMAIL_SMTP_USERNAME=your-smtp-username
# EMAIL_SMTP_PASSWORD=your-smtp-password