#!/bin/bash
# scripts/dev.sh - Development helper script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_warning ".env file not found. Creating from template..."
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success ".env file created from .env.example"
        else
            print_error ".env.example not found. Please create .env file manually."
            exit 1
        fi
    fi
}

# Generate secure secrets
generate_secrets() {
    print_status "Generating secure secrets for development..."
    
    ACCESS_SECRET=$(openssl rand -base64 48 | tr -d '\n')
    REFRESH_SECRET=$(openssl rand -base64 48 | tr -d '\n')
    CSRF_SECRET=$(openssl rand -base64 24 | tr -d '\n')
    ENCRYPTION_KEY=$(openssl rand -base64 32 | tr -d '\n')
    
    # Update .env file with generated secrets
    sed -i.bak "s|AUTH_ACCESS_SECRET=.*|AUTH_ACCESS_SECRET=${ACCESS_SECRET}|" .env
    sed -i.bak "s|AUTH_REFRESH_SECRET=.*|AUTH_REFRESH_SECRET=${REFRESH_SECRET}|" .env
    sed -i.bak "s|AUTH_CSRF_SECRET=.*|AUTH_CSRF_SECRET=${CSRF_SECRET}|" .env
    sed -i.bak "s|ENCRYPTION_KEY=.*|ENCRYPTION_KEY=${ENCRYPTION_KEY}|" .env
    
    # Remove backup file
    rm .env.bak
    
    print_success "Secrets generated and updated in .env file"
}

# Start development environment
start_dev() {
    print_status "Starting development environment..."
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Build and start containers
    docker-compose up --build -d
    
    print_success "Development environment started!"
    print_status "Services available at:"
    echo "  🚀 API Server: http://localhost:8080"
    echo "  📧 MailHog UI: http://localhost:8025"
    echo "  🗄️  PostgreSQL: localhost:5432"
    echo ""
    print_status "Useful commands:"
    echo "  📊 View logs: ./scripts/dev.sh logs"
    echo "  🛑 Stop services: ./scripts/dev.sh stop"
    echo "  🔄 Restart API: ./scripts/dev.sh restart api"
    echo "  🏥 Check health: ./scripts/dev.sh health"
}

# Stop development environment
stop_dev() {
    print_status "Stopping development environment..."
    docker-compose down
    print_success "Development environment stopped!"
}

# View logs
view_logs() {
    local service=${1:-api}
    print_status "Viewing logs for service: $service"
    docker-compose logs -f $service
}

# Restart specific service
restart_service() {
    local service=${1:-api}
    print_status "Restarting service: $service"
    docker-compose restart $service
    print_success "Service $service restarted!"
}

# Check health of services
check_health() {
    print_status "Checking service health..."
    
    # Check API health
    echo -n "API Server: "
    if curl -s http://localhost:8080/api/health > /dev/null; then
        print_success "✅ Healthy"
    else
        print_error "❌ Not responding"
    fi
    
    # Check PostgreSQL
    echo -n "PostgreSQL: "
    if docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
        print_success "✅ Healthy"
    else
        print_error "❌ Not responding"
    fi
    
    # Check MailHog
    echo -n "MailHog: "
    if curl -s http://localhost:8025 > /dev/null; then
        print_success "✅ Healthy"
    else
        print_error "❌ Not responding"
    fi
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    docker-compose run --rm migrations
    print_success "Migrations completed!"
}

# Access database shell
db_shell() {
    print_status "Opening PostgreSQL shell..."
    docker-compose exec postgres psql -U postgres -d admin_dashboard
}

# Clean up development environment
cleanup() {
    print_status "Cleaning up development environment..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    print_success "Cleanup completed!"
}

# Show usage
show_usage() {
    echo "Development Helper Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  setup          Setup development environment (check env, generate secrets)"
    echo "  start          Start development environment"
    echo "  stop           Stop development environment"
    echo "  restart [svc]  Restart service (default: api)"
    echo "  logs [svc]     View logs for service (default: api)"
    echo "  health         Check health of all services"
    echo "  migrate        Run database migrations"
    echo "  db             Access database shell"
    echo "  cleanup        Clean up containers and volumes"
    echo "  secrets        Generate new development secrets"
    echo ""
    echo "Examples:"
    echo "  $0 setup          # Initial setup"
    echo "  $0 start          # Start all services"
    echo "  $0 logs api       # View API logs"
    echo "  $0 restart api    # Restart API service"
    echo "  $0 health         # Check service health"
}

# Main script logic
main() {
    case "${1:-}" in
        "setup")
            check_env_file
            generate_secrets
            print_success "Development environment setup completed!"
            print_status "Run './scripts/dev.sh start' to start the services."
            ;;
        "start")
            check_env_file
            start_dev
            ;;
        "stop")
            stop_dev
            ;;
        "restart")
            restart_service "${2:-api}"
            ;;
        "logs")
            view_logs "${2:-api}"
            ;;
        "health")
            check_health
            ;;
        "migrate")
            run_migrations
            ;;
        "db")
            db_shell
            ;;
        "cleanup")
            cleanup
            ;;
        "secrets")
            generate_secrets
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown command: ${1:-}"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"