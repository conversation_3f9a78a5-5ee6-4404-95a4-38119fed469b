package main

import (
	"context"
	"os"
	"strconv"
	"time"

	"github.com/psynarios/admin_back/internal/api"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
	"github.com/psynarios/admin_back/internal/api/routes"
	"github.com/psynarios/admin_back/internal/auth/repository"
	"github.com/psynarios/admin_back/internal/auth/service"
	"github.com/psynarios/admin_back/internal/common/config"
	"github.com/psynarios/admin_back/internal/common/database"
	"github.com/psynarios/admin_back/internal/common/logger"
	"github.com/rs/zerolog"

	// Core services
	companyRepo "github.com/psynarios/admin_back/internal/company/repository/postgres"
	companyService "github.com/psynarios/admin_back/internal/company/service"
	i18nRepo "github.com/psynarios/admin_back/internal/i18n/repository/postgres"
	i18nService "github.com/psynarios/admin_back/internal/i18n/service"
	mailerRepo "github.com/psynarios/admin_back/internal/mailer/repository/postgres"
	mailerService "github.com/psynarios/admin_back/internal/mailer/service"
	mediaRepo "github.com/psynarios/admin_back/internal/media/repository/postgres"
	mediaService "github.com/psynarios/admin_back/internal/media/service"
	mediaStorage "github.com/psynarios/admin_back/internal/media/storage"
	scraperService "github.com/psynarios/admin_back/internal/scraper/service"
	templatingRepo "github.com/psynarios/admin_back/internal/templating/repository/postgres"
	templatingService "github.com/psynarios/admin_back/internal/templating/service"

	// Library services
	behaviourRepo "github.com/psynarios/admin_back/internal/library/behaviour/repository/postgres"
	behaviourService "github.com/psynarios/admin_back/internal/library/behaviour/service"
	characterRepo "github.com/psynarios/admin_back/internal/library/character/repository/postgres"
	characterService "github.com/psynarios/admin_back/internal/library/character/service"
	interactionRepo "github.com/psynarios/admin_back/internal/library/interaction/repository/postgres"
	interactionService "github.com/psynarios/admin_back/internal/library/interaction/service"
	optionRepo "github.com/psynarios/admin_back/internal/library/option/repository/postgres"
	optionService "github.com/psynarios/admin_back/internal/library/option/service"
	scenarioRepo "github.com/psynarios/admin_back/internal/library/scenario/repository/postgres"
	scenarioService "github.com/psynarios/admin_back/internal/library/scenario/service"

	"github.com/psynarios/admin_back/pkg/utils"
	"github.com/psynarios/admin_back/pkg/validator"
)

func main() {
	// Initialize logger
	log := logger.Initialize(config.LoggerConfig{
		Level:      getEnvWithDefault("LOG_LEVEL", "info"),
		TimeFormat: time.RFC3339,
		Console:    true,
		File:       "logs/api.log",
	})

	// Load configuration with enhanced validation
	cfg, err := config.LoadConfig(log)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}

	// Initialize database
	db, err := database.NewPostgresDB(database.Config{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		DBName:   cfg.Database.DBName,
		SSLMode:  cfg.Database.SSLMode,
	})

	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}

	// Initialize encryption for secure password storage
	if err := utils.InitEncryption(); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize encryption")
	}

	defer db.Close()

	// Initialize repositories
	userRepo := repository.NewPostgresUserRepository(db)
	tokenRepo := repository.NewTokenRepository(db, log)
	companyRepository := companyRepo.NewCompanyRepository(db.GetPool(), log)
	mailRepository := mailerRepo.NewMailRepository(db.GetPool(), log)
	mediaRepository := mediaRepo.NewMediaRepository(db.GetPool(), log)
	templateGroupRepo := templatingRepo.NewTemplateGroupRepository(db.GetPool(), log)
	templateRepository := templatingRepo.NewTemplateRepository(db.GetPool(), log)

	// Initialize library repositories
	languageRepository := i18nRepo.NewLanguageRepository(db.GetPool(), log)
	scenarioRepository := scenarioRepo.NewScenarioRepository(db.GetPool(), log)
	characterRepository := characterRepo.NewCharacterRepository(db.GetPool(), log)
	interactionRepository := interactionRepo.NewInteractionRepository(db.GetPool(), log)
	optionRepository := optionRepo.NewOptionRepository(db.GetPool(), log)
	behaviourRepository := behaviourRepo.NewBehaviourRepository(db.GetPool(), log)

	// Initialize enhanced auth service (updated to use the new service)
	authService := service.NewAuthService(
		userRepo,
		tokenRepo,
		cfg, // Pass the entire config instead of individual fields
		log,
	)

	userService := service.NewUserService(userRepo, log)
	companyService := companyService.NewCompanyService(companyRepository, log)
	mailService := mailerService.NewMailService(mailRepository, log)
	templateService := templatingService.NewTemplateService(templateRepository, templateGroupRepo, log)

	// Initialize i18n service (needed by library services)
	i18nSvc := i18nService.NewI18nService(languageRepository)

	// Initialize storage provider for media service with proper error handling
	storageBasePath := getEnvWithDefault("STORAGE_BASE_PATH", "./uploads")
	storageBaseURL := getEnvWithDefault("STORAGE_BASE_URL", "/api/media/files")

	// Parse max file size from environment with default
	maxFileSize := int64(100 * 1024 * 1024) // 100MB default
	if maxFileSizeStr := os.Getenv("STORAGE_MAX_FILE_SIZE"); maxFileSizeStr != "" {
		if parsedSize, err := strconv.ParseInt(maxFileSizeStr, 10, 64); err == nil {
			maxFileSize = parsedSize
		} else {
			log.Warn().
				Str("provided_value", maxFileSizeStr).
				Int64("default_value", maxFileSize).
				Msg("Invalid STORAGE_MAX_FILE_SIZE, using default")
		}
	}
	if cfg.Storage.MaxFileSize > 0 {
		maxFileSize = cfg.Storage.MaxFileSize
	}

	// Initialize storage provider with logger and error handling
	storageProvider, err := mediaStorage.NewLocalStorage(storageBasePath, storageBaseURL, log)
	if err != nil {
		log.Fatal().
			Err(err).
			Str("base_path", storageBasePath).
			Str("base_url", storageBaseURL).
			Msg("Failed to initialize storage provider")
	}

	log.Info().
		Str("storage_path", storageBasePath).
		Str("storage_url", storageBaseURL).
		Int64("max_file_size", maxFileSize).
		Msg("Storage provider initialized successfully")

	// Initialize media service with all required dependencies
	mediaService := mediaService.NewMediaService(
		mediaRepository,
		storageProvider,
		i18nSvc,
		log,
		maxFileSize,
	)

	// Initialize scraper service
	scraperService := scraperService.NewScraperService(
		companyRepository,
		mailService,
		templateService,
		log,
	)

	// Initialize library services
	scenarioService := scenarioService.NewScenarioService(scenarioRepository, i18nSvc, log)
	characterService := characterService.NewCharacterService(characterRepository)
	interactionService := interactionService.NewInteractionService(interactionRepository)
	optionService := optionService.NewOptionService(optionRepository)
	behaviourService := behaviourService.NewBehaviourService(behaviourRepository)

	// Start background jobs
	startBackgroundJobs(tokenRepo, log)

	// Initialize validator
	customValidator := validator.NewCustomValidator()

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, log)
	userHandler := handlers.NewUserHandler(userService, log)
	companyHandler := handlers.NewCompanyHandler(companyService, log)
	mailHandler := handlers.NewMailHandler(mailService, log)
	mediaHandler := handlers.NewMediaHandler(mediaService, customValidator)
	templateHandler := handlers.NewTemplateHandler(templateService, log)
	scraperHandler := handlers.NewScraperHandler(scraperService, log)
	healthHandler := handlers.NewHealthHandler(db.GetPool(), storageProvider, log)

	// Initialize library handlers
	scenarioHandler := handlers.NewScenarioHandler(scenarioService, log)
	characterHandler := handlers.NewCharacterHandler(characterService, log)
	interactionHandler := handlers.NewInteractionHandler(interactionService, log)
	optionHandler := handlers.NewOptionHandler(optionService, log)
	behaviourHandler := handlers.NewBehaviourHandler(behaviourService, log)

	// Create middleware instances (enhanced versions)
	authMiddleware := middleware.NewAuthMiddleware(authService, log)
	var csrfMiddleware *middleware.CsrfMiddleware
	if cfg.Auth.CSRFEnabled {
		csrfMiddleware = middleware.NewCsrfMiddleware(authService, cfg, log)
		log.Info().Msg("CSRF middleware initialized")
	} else {
		log.Info().Msg("CSRF protection disabled")
	}

	// Setup router with all handlers and CSRF configuration
	router := routes.SetupRouter(
		log,
		authHandler,
		userHandler,
		companyHandler,
		mailHandler,
		templateHandler,
		scraperHandler,
		mediaHandler,
		// Library handlers
		scenarioHandler,
		characterHandler,
		interactionHandler,
		optionHandler,
		behaviourHandler,
		authMiddleware,
		csrfMiddleware, // Pass CSRF middleware to router
		cfg,            // Pass config for CSRF settings
		healthHandler,
	)

	// Log startup information
	log.Info().
		Str("environment", cfg.Environment).
		Bool("csrf_enabled", cfg.Auth.CSRFEnabled).
		Dur("access_token_lifetime", cfg.Auth.AccessLifetime).
		Dur("refresh_token_lifetime", cfg.Auth.RefreshLifetime).
		Str("cookie_same_site", cfg.Auth.CookieSameSite).
		Bool("cookie_secure", cfg.Auth.CookieSecure).
		Str("storage_path", storageBasePath).
		Int64("max_file_size_mb", maxFileSize/(1024*1024)).
		Msg("Admin dashboard API server initialized")

	// Create and start server
	server := api.NewServer(router, cfg.Server, log)
	if err := server.Start(); err != nil {
		log.Fatal().Err(err).Msg("Server failed")
	}
}

// startBackgroundJobs initializes and starts background tasks
func startBackgroundJobs(tokenRepo repository.TokenRepository, log zerolog.Logger) {
	// Start token cleanup job
	go func() {
		ticker := time.NewTicker(24 * time.Hour) // Run once per day
		defer ticker.Stop()

		// Run immediately on startup
		cleanupTokens(tokenRepo, log)

		for range ticker.C {
			cleanupTokens(tokenRepo, log)
		}
	}()

	log.Info().Msg("Background jobs started successfully")
}

// cleanupTokens removes expired tokens from the database
func cleanupTokens(tokenRepo repository.TokenRepository, log zerolog.Logger) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	start := time.Now()
	err := tokenRepo.CleanExpiredTokens(ctx)
	duration := time.Since(start)

	if err != nil {
		log.Error().
			Err(err).
			Dur("duration", duration).
			Msg("Failed to clean expired tokens")
	} else {
		log.Info().
			Dur("duration", duration).
			Msg("Successfully cleaned expired tokens")
	}
}

// getEnvWithDefault returns environment variable value or default if not set
func getEnvWithDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
