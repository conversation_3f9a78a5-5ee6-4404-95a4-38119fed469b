# Makefile for Admin Dashboard Development

.PHONY: help setup start stop restart logs health migrate db cleanup secrets build test lint clean

# Default target
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

setup: ## Setup development environment (check env, generate secrets)
	@echo "Setting up development environment..."
	@if [ ! -f .env ]; then \
		echo "Creating .env from template..."; \
		cp .env.example .env 2>/dev/null || echo "Please create .env file manually"; \
	fi
	@echo "Development environment setup completed!"

secrets: ## Generate secure development secrets
	@echo "Generating secure secrets..."
	@ACCESS_SECRET=$$(openssl rand -base64 48 | tr -d '\n'); \
	REFRESH_SECRET=$$(openssl rand -base64 48 | tr -d '\n'); \
	CSRF_SECRET=$$(openssl rand -base64 24 | tr -d '\n'); \
	ENCRYPTION_KEY=$$(openssl rand -base64 32 | tr -d '\n'); \
	sed -i.bak "s|AUTH_ACCESS_SECRET=.*|AUTH_ACCESS_SECRET=$$ACCESS_SECRET|" .env; \
	sed -i.bak "s|AUTH_REFRESH_SECRET=.*|AUTH_REFRESH_SECRET=$$REFRESH_SECRET|" .env; \
	sed -i.bak "s|AUTH_CSRF_SECRET=.*|AUTH_CSRF_SECRET=$$CSRF_SECRET|" .env; \
	sed -i.bak "s|ENCRYPTION_KEY=.*|ENCRYPTION_KEY=$$ENCRYPTION_KEY|" .env; \
	rm -f .env.bak
	@echo "✅ Secrets generated and updated in .env file"

start: ## Start development environment
	@echo "🚀 Starting development environment..."
	@docker-compose up --build -d
	@echo "✅ Development environment started!"
	@echo ""
	@echo "Services available at:"
	@echo "  🚀 API Server: http://localhost:8080"
	@echo "  📧 MailHog UI: http://localhost:8025"
	@echo "  🗄️  PostgreSQL: localhost:5432"

stop: ## Stop development environment
	@echo "🛑 Stopping development environment..."
	@docker-compose down
	@echo "✅ Development environment stopped!"

restart: ## Restart API service
	@echo "🔄 Restarting API service..."
	@docker-compose restart api
	@echo "✅ API service restarted!"

restart-db: ## Restart PostgreSQL service
	@echo "🔄 Restarting PostgreSQL service..."
	@docker-compose restart postgres
	@echo "✅ PostgreSQL service restarted!"

logs: ## View API logs
	@docker-compose logs -f api

logs-db: ## View PostgreSQL logs
	@docker-compose logs -f postgres

logs-all: ## View all service logs
	@docker-compose logs -f

health: ## Check health of all services
	@echo "🏥 Checking service health..."
	@echo -n "API Server: "
	@if curl -s http://localhost:8080/api/health > /dev/null 2>&1; then \
		echo "✅ Healthy"; \
	else \
		echo "❌ Not responding"; \
	fi
	@echo -n "PostgreSQL: "
	@if docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then \
		echo "✅ Healthy"; \
	else \
		echo "❌ Not responding"; \
	fi
	@echo -n "MailHog: "
	@if curl -s http://localhost:8025 > /dev/null 2>&1; then \
		echo "✅ Healthy"; \
	else \
		echo "❌ Not responding"; \
	fi

migrate: ## Run database migrations
	@echo "🔄 Running database migrations..."
	@docker-compose run --rm migrations
	@echo "✅ Migrations completed!"

migrate-down: ## Rollback database migrations
	@echo "⬇️  Rolling back database migrations..."
	@docker-compose run --rm migrations -database "**************************************************/admin_dashboard?sslmode=disable" down
	@echo "✅ Migrations rolled back!"

db: ## Access database shell
	@echo "🗄️  Opening PostgreSQL shell..."
	@docker-compose exec postgres psql -U postgres -d admin_dashboard

db-reset: ## Reset database (WARNING: This will delete all data!)
	@echo "⚠️  WARNING: This will delete all data in the database!"
	@echo "Are you sure? (y/N): " && read ans && [ $${ans:-N} = y ]
	@docker-compose down -v
	@docker-compose up -d postgres
	@sleep 5
	@make migrate
	@echo "✅ Database reset completed!"

build: ## Build the application
	@echo "🔨 Building application..."
	@docker-compose build api
	@echo "✅ Application built!"

test: ## Run tests
	@echo "🧪 Running tests..."
	@go test ./...
	@echo "✅ Tests completed!"

test-coverage: ## Run tests with coverage
	@echo "🧪 Running tests with coverage..."
	@go test -coverprofile=coverage.out ./...
	@go tool cover -html=coverage.out -o coverage.html
	@echo "✅ Coverage report generated: coverage.html"

lint: ## Run linter
	@echo "🔍 Running linter..."
	@golangci-lint run
	@echo "✅ Linting completed!"

format: ## Format code
	@echo "🎨 Formatting code..."
	@go fmt ./...
	@echo "✅ Code formatted!"

clean: ## Clean up development environment
	@echo "🧹 Cleaning up development environment..."
	@docker-compose down -v --remove-orphans
	@docker system prune -f
	@echo "✅ Cleanup completed!"

# Development workflow targets
dev-setup: setup secrets ## Complete development setup
	@echo "🎉 Development environment ready!"
	@echo "Run 'make start' to begin development."

dev-start: start migrate ## Start development with migrations
	@echo "🎉 Development environment started with database migrations!"

dev-reset: stop clean dev-start ## Complete development reset

# Production helpers
prod-build: ## Build for production
	@echo "🏭 Building for production..."
	@docker build -t admin-dashboard:latest .
	@echo "✅ Production build completed!"

# Utility targets
env-check: ## Check environment variables
	@echo "🔍 Checking environment variables..."
	@echo "APP_ENV: $${APP_ENV:-not set}"
	@echo "AUTH_ACCESS_SECRET: $${AUTH_ACCESS_SECRET:+set}"
	@echo "AUTH_REFRESH_SECRET: $${AUTH_REFRESH_SECRET:+set}"
	@echo "AUTH_CSRF_SECRET: $${AUTH_CSRF_SECRET:+set}"
	@echo "DB_HOST: $${DB_HOST:-not set}"
	@echo "DB_NAME: $${DB_NAME:-not set}"

show-urls: ## Show service URLs
	@echo "🌐 Service URLs:"
	@echo "  API Server: http://localhost:8080"
	@echo "  API Health: http://localhost:8080/api/health"
	@echo "  MailHog UI: http://localhost:8025"
	@echo "  PostgreSQL: localhost:5432"

# Quick shortcuts
up: start ## Alias for start
down: stop ## Alias for stop
ps: ## Show running containers
	@docker-compose ps