# Build stage
FROM golang:1.24-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache ca-certificates git

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o admin-api ./cmd/api

# Final stage
FROM alpine:latest

# Install runtime dependencies
RUN apk --no-cache add ca-certificates wget curl

# Create non-root user and group
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Create application directory
WORKDIR /app

# Create necessary directories with proper permissions
RUN mkdir -p /app/uploads /app/logs /app/certs && \
    chown -R appuser:appgroup /app && \
    chmod -R 755 /app

# Copy binary from builder
COPY --from=builder /app/admin-api .

# Copy migrations directory
COPY --from=builder /app/migrations ./migrations

# Copy config files if they exist
COPY --from=builder /app/configs ./configs

# Ensure all files are owned by appuser
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/api/health || exit 1

# Command to run the application
CMD ["./admin-api"]