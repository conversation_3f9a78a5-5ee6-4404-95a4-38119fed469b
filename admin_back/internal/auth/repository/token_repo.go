// internal/auth/repository/token_repo.go
package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/psynarios/admin_back/internal/common/database"
	"github.com/rs/zerolog"
)

// RefreshToken model represents a refresh token in the database
type RefreshToken struct {
	ID        int64     `json:"id" db:"id"`
	TokenID   string    `json:"token_id" db:"token_id"`
	UserID    int64     `json:"user_id" db:"user_id"`
	ExpiresAt time.Time `json:"expires_at" db:"expires_at"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
	Revoked   bool      `json:"revoked" db:"revoked"`
	UserAgent string    `json:"user_agent,omitempty" db:"user_agent"`
	IPAddress string    `json:"ip_address,omitempty" db:"ip_address"`
}

// TokenRepository defines operations for managing refresh tokens
type TokenRepository interface {
	StoreToken(ctx context.Context, userID int64, tokenID string, expiresAt time.Time) error
	ValidateToken(ctx context.Context, tokenID string) (int64, error)
	RevokeToken(ctx context.Context, tokenID string) error
	RevokeAllUserTokens(ctx context.Context, userID int64) error
	CleanExpiredTokens(ctx context.Context) error
	GetActiveTokensByUserID(ctx context.Context, userID int64) ([]*RefreshToken, error)
	RevokeTokenByUUIDAndUserID(ctx context.Context, uuid string, userID int64) error
}

// PostgresTokenRepository implements TokenRepository using PostgreSQL
type PostgresTokenRepository struct {
	db     *database.PostgresDB
	logger zerolog.Logger
}

// NewTokenRepository creates a new token repository
func NewTokenRepository(db *database.PostgresDB, logger zerolog.Logger) TokenRepository {
	return &PostgresTokenRepository{
		db:     db,
		logger: logger.With().Str("component", "token_repository").Logger(),
	}
}

// StoreToken stores a refresh token in the database
func (r *PostgresTokenRepository) StoreToken(ctx context.Context, userID int64, tokenID string, expiresAt time.Time) error {
	query := `
		INSERT INTO refresh_tokens (user_id, token_id, expires_at, created_at, updated_at, revoked)
		VALUES ($1, $2, $3, NOW(), NOW(), false)
	`
	_, err := r.db.Exec(ctx, query, userID, tokenID, expiresAt)
	if err != nil {
		r.logger.Error().Err(err).
			Int64("user_id", userID).
			Str("token_id", tokenID).
			Time("expires_at", expiresAt).
			Msg("Failed to store refresh token")
		return fmt.Errorf("failed to store refresh token: %w", err)
	}

	r.logger.Debug().
		Int64("user_id", userID).
		Str("token_id", tokenID).
		Time("expires_at", expiresAt).
		Msg("Refresh token stored successfully")

	return nil
}

// ValidateToken validates a refresh token and returns the associated user ID
func (r *PostgresTokenRepository) ValidateToken(ctx context.Context, tokenID string) (int64, error) {
	query := `
		SELECT user_id FROM refresh_tokens
		WHERE token_id = $1 AND expires_at > NOW() AND revoked = false
	`
	var userID int64
	err := r.db.QueryRow(ctx, query, tokenID).Scan(&userID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			r.logger.Warn().
				Str("token_id", tokenID).
				Msg("Refresh token not found or revoked")
			return 0, fmt.Errorf("refresh token not found or revoked")
		}
		r.logger.Error().Err(err).
			Str("token_id", tokenID).
			Msg("Failed to validate refresh token")
		return 0, fmt.Errorf("failed to validate refresh token: %w", err)
	}

	r.logger.Debug().
		Int64("user_id", userID).
		Str("token_id", tokenID).
		Msg("Refresh token validated successfully")

	return userID, nil
}

// RevokeToken revokes a specific refresh token
func (r *PostgresTokenRepository) RevokeToken(ctx context.Context, tokenID string) error {
	query := `
		UPDATE refresh_tokens
		SET revoked = true, updated_at = NOW()
		WHERE token_id = $1
	`
	ct, err := r.db.Exec(ctx, query, tokenID)
	if err != nil {
		r.logger.Error().Err(err).
			Str("token_id", tokenID).
			Msg("Failed to revoke refresh token")
		return fmt.Errorf("failed to revoke refresh token: %w", err)
	}

	if ct.RowsAffected() == 0 {
		r.logger.Warn().
			Str("token_id", tokenID).
			Msg("Refresh token not found for revocation")
		return fmt.Errorf("refresh token not found")
	}

	r.logger.Debug().
		Str("token_id", tokenID).
		Msg("Refresh token revoked successfully")

	return nil
}

// RevokeAllUserTokens revokes all refresh tokens for a user
func (r *PostgresTokenRepository) RevokeAllUserTokens(ctx context.Context, userID int64) error {
	query := `
		UPDATE refresh_tokens
		SET revoked = true, updated_at = NOW()
		WHERE user_id = $1 AND revoked = false
	`
	ct, err := r.db.Exec(ctx, query, userID)
	if err != nil {
		r.logger.Error().Err(err).
			Int64("user_id", userID).
			Msg("Failed to revoke all user tokens")
		return fmt.Errorf("failed to revoke all user tokens: %w", err)
	}

	r.logger.Debug().
		Int64("user_id", userID).
		Int64("tokens_revoked", ct.RowsAffected()).
		Msg("All user tokens revoked successfully")

	return nil
}

// CleanExpiredTokens removes expired tokens from the database
func (r *PostgresTokenRepository) CleanExpiredTokens(ctx context.Context) error {
	query := `
		DELETE FROM refresh_tokens
		WHERE expires_at < NOW()
	`
	ct, err := r.db.Exec(ctx, query)
	if err != nil {
		r.logger.Error().Err(err).
			Msg("Failed to clean expired tokens")
		return fmt.Errorf("failed to clean expired tokens: %w", err)
	}

	r.logger.Debug().
		Int64("tokens_cleaned", ct.RowsAffected()).
		Msg("Expired tokens cleaned successfully")

	return nil
}

// GetActiveTokensByUserID retrieves all active refresh tokens for a user
func (r *PostgresTokenRepository) GetActiveTokensByUserID(ctx context.Context, userID int64) ([]*RefreshToken, error) {
	query := `
		SELECT id, token_id, user_id, expires_at, created_at, updated_at, revoked,
		       COALESCE(user_agent, '') as user_agent,
		       COALESCE(ip_address, '') as ip_address
		FROM refresh_tokens 
		WHERE user_id = $1 AND expires_at > NOW() AND revoked = false
		ORDER BY created_at DESC
	`

	rows, err := r.db.Query(ctx, query, userID)
	if err != nil {
		r.logger.Error().Err(err).
			Int64("user_id", userID).
			Msg("Failed to query active tokens")
		return nil, fmt.Errorf("failed to query active tokens: %w", err)
	}
	defer rows.Close()

	var tokens []*RefreshToken
	for rows.Next() {
		token := &RefreshToken{}
		err := rows.Scan(
			&token.ID,
			&token.TokenID,
			&token.UserID,
			&token.ExpiresAt,
			&token.CreatedAt,
			&token.UpdatedAt,
			&token.Revoked,
			&token.UserAgent,
			&token.IPAddress,
		)
		if err != nil {
			r.logger.Error().Err(err).
				Int64("user_id", userID).
				Msg("Failed to scan token")
			return nil, fmt.Errorf("failed to scan token: %w", err)
		}
		tokens = append(tokens, token)
	}

	if err = rows.Err(); err != nil {
		r.logger.Error().Err(err).
			Int64("user_id", userID).
			Msg("Failed to iterate tokens")
		return nil, fmt.Errorf("failed to iterate tokens: %w", err)
	}

	r.logger.Debug().
		Int64("user_id", userID).
		Int("token_count", len(tokens)).
		Msg("Retrieved active tokens successfully")

	return tokens, nil
}

// RevokeTokenByUUIDAndUserID revokes a specific token by UUID for a specific user
func (r *PostgresTokenRepository) RevokeTokenByUUIDAndUserID(ctx context.Context, uuid string, userID int64) error {
	query := `
		UPDATE refresh_tokens 
		SET revoked = true, updated_at = NOW() 
		WHERE token_id = $1 AND user_id = $2 AND revoked = false
	`

	ct, err := r.db.Exec(ctx, query, uuid, userID)
	if err != nil {
		r.logger.Error().Err(err).
			Str("uuid", uuid).
			Int64("user_id", userID).
			Msg("Failed to revoke token")
		return fmt.Errorf("failed to revoke token: %w", err)
	}

	if ct.RowsAffected() == 0 {
		r.logger.Warn().
			Str("uuid", uuid).
			Int64("user_id", userID).
			Msg("Token not found or doesn't belong to user")
		return fmt.Errorf("token not found or doesn't belong to user")
	}

	r.logger.Debug().
		Str("uuid", uuid).
		Int64("user_id", userID).
		Msg("Token revoked successfully")

	return nil
}
