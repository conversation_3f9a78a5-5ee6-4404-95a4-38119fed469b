// internal/auth/service/auth_service.go
package service

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"golang.org/x/crypto/bcrypt"

	"github.com/psynarios/admin_back/internal/auth/models"
	"github.com/psynarios/admin_back/internal/auth/repository"
	"github.com/psynarios/admin_back/internal/common/config"
)

// AuthService defines the interface for authentication operations
type AuthService interface {
	Login(ctx context.Context, email, password string) (*TokenDetails, *models.UserResponse, error)
	SetAuthCookies(w http.ResponseWriter, tokens *TokenDetails)
	SetCsrfCookie(w http.ResponseWriter, csrfToken string)
	ClearAuthCookies(w http.ResponseWriter)
	ClearCsrfCookie(w http.ResponseWriter)
	GetTokenFromCookies(r *http.Request) (accessToken, refreshToken string)
	GetCsrfToken(r *http.Request) string
	Register(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error)
	Logout(ctx context.Context, w http.ResponseWriter, refreshToken string) error
	LogoutAllSessions(ctx context.Context, w http.ResponseWriter, userID int64) error
	RefreshToken(ctx context.Context, refreshToken string) (*TokenDetails, error)
	ValidateToken(accessToken string) (*AccessTokenClaims, error)
	ChangePassword(ctx context.Context, userID int64, currentPassword, newPassword string) error
	GetUserByID(ctx context.Context, id int64) (*models.UserResponse, error)
	VerifyCsrfToken(r *http.Request, providedToken string) bool
	ListUsers(ctx context.Context, page, pageSize int) ([]*models.UserResponse, int64, error)
	UpdateUser(ctx context.Context, userID int64, req *models.UpdateUserRequest) (*models.UserResponse, error)
	DeleteUser(ctx context.Context, userID int64) error
	RefreshTokenForUser(ctx context.Context, userID int64) (*TokenDetails, error)
	IsTokenNearExpiry(claims *AccessTokenClaims) bool
	GetActiveSessions(ctx context.Context, userID int64) ([]models.SessionInfo, error)
	RevokeSession(ctx context.Context, userID int64, sessionID string) error
}

// TokenDetails contains auth token details with enhanced expiry information
type TokenDetails struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	AccessUuid   string `json:"access_uuid"`
	RefreshUuid  string `json:"refresh_uuid"`
	AtExpires    int64  `json:"at_expires"`
	RtExpires    int64  `json:"rt_expires"`
	CsrfToken    string `json:"csrf_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int64  `json:"expires_in"`
}

// AccessTokenClaims defines JWT claims for access tokens
type AccessTokenClaims struct {
	UserID    int64       `json:"user_id"`
	Username  string      `json:"username"`
	Role      models.Role `json:"role"`
	UUID      string      `json:"uuid"`
	CsrfToken string      `json:"csrf_token"`
	jwt.RegisteredClaims
}

// RefreshTokenClaims defines JWT claims for refresh tokens
type RefreshTokenClaims struct {
	UserID int64  `json:"user_id"`
	UUID   string `json:"uuid"`
	jwt.RegisteredClaims
}

// authServiceImpl implements the AuthService interface
type authServiceImpl struct {
	userRepo  repository.UserRepository
	tokenRepo repository.TokenRepository
	config    *config.Config
	logger    zerolog.Logger
}

// NewAuthService creates a new auth service with your existing config structure
func NewAuthService(
	userRepo repository.UserRepository,
	tokenRepo repository.TokenRepository,
	cfg *config.Config,
	logger zerolog.Logger,
) AuthService {
	return &authServiceImpl{
		userRepo:  userRepo,
		tokenRepo: tokenRepo,
		config:    cfg,
		logger:    logger.With().Str("component", "auth_service").Logger(),
	}
}

// Login authenticates a user and generates JWT tokens
func (s *authServiceImpl) Login(ctx context.Context, email, password string) (*TokenDetails, *models.UserResponse, error) {
	// Find user by email
	user, err := s.userRepo.FindByEmail(ctx, email)
	if err != nil {
		s.logger.Debug().
			Str("email", email).
			Err(err).
			Msg("Invalid login credentials")
		return nil, nil, fmt.Errorf("invalid credentials")
	}

	// Check if user is active
	if !user.Active {
		s.logger.Debug().
			Str("email", email).
			Int64("user_id", user.ID).
			Msg("Account is disabled")
		return nil, nil, fmt.Errorf("account is disabled")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		s.logger.Debug().
			Str("email", email).
			Int64("user_id", user.ID).
			Err(err).
			Msg("Invalid password")
		return nil, nil, fmt.Errorf("invalid credentials")
	}

	// Generate tokens
	tokens, err := s.createTokens(ctx, user)
	if err != nil {
		s.logger.Error().
			Str("email", email).
			Int64("user_id", user.ID).
			Err(err).
			Msg("Failed to generate tokens")
		return nil, nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Update last login time
	if err := s.userRepo.UpdateLastLogin(ctx, user.ID); err != nil {
		// Non-critical error, just log it
		s.logger.Warn().
			Int64("user_id", user.ID).
			Err(err).
			Msg("Failed to update last login")
	}

	// Convert to response object
	userResponse := user.ToResponse()

	s.logger.Info().
		Int64("user_id", user.ID).
		Str("email", user.Email).
		Int64("expires_in", tokens.ExpiresIn).
		Msg("User logged in successfully")

	return tokens, &userResponse, nil
}

// Enhanced SetAuthCookies with your config structure
func (s *authServiceImpl) SetAuthCookies(w http.ResponseWriter, tokens *TokenDetails) {
	now := time.Now()

	// Calculate actual max ages
	accessMaxAge := int(time.Unix(tokens.AtExpires, 0).Sub(now).Seconds())
	refreshMaxAge := int(time.Unix(tokens.RtExpires, 0).Sub(now).Seconds())

	// Ensure positive max ages
	if accessMaxAge < 0 {
		accessMaxAge = 0
	}
	if refreshMaxAge < 0 {
		refreshMaxAge = 0
	}

	// Set access token cookie
	accessCookie := &http.Cookie{
		Name:     "access_token",
		Value:    tokens.AccessToken,
		Domain:   s.config.GetEffectiveCookieDomain(),
		Path:     "/",
		MaxAge:   accessMaxAge,
		Expires:  time.Unix(tokens.AtExpires, 0),
		Secure:   s.config.Auth.CookieSecure,
		HttpOnly: s.config.Auth.CookieHTTPOnly,
		SameSite: s.config.GetCookieSameSite(),
	}

	// Set refresh token cookie
	refreshCookie := &http.Cookie{
		Name:     "refresh_token",
		Value:    tokens.RefreshToken,
		Domain:   s.config.GetEffectiveCookieDomain(),
		Path:     "/",
		MaxAge:   refreshMaxAge,
		Expires:  time.Unix(tokens.RtExpires, 0),
		Secure:   s.config.Auth.CookieSecure,
		HttpOnly: s.config.Auth.CookieHTTPOnly,
		SameSite: s.config.GetCookieSameSite(),
	}

	http.SetCookie(w, accessCookie)
	http.SetCookie(w, refreshCookie)

	s.logger.Debug().
		Int("access_max_age", accessMaxAge).
		Int("refresh_max_age", refreshMaxAge).
		Time("access_expires", time.Unix(tokens.AtExpires, 0)).
		Time("refresh_expires", time.Unix(tokens.RtExpires, 0)).
		Bool("secure", s.config.Auth.CookieSecure).
		Str("same_site", s.config.Auth.CookieSameSite).
		Msg("Auth cookies set successfully")
}

// SetCsrfCookie sets the CSRF token cookie (non HTTP-only so JS can read it)
func (s *authServiceImpl) SetCsrfCookie(w http.ResponseWriter, csrfToken string) {
	if !s.config.Auth.CSRFEnabled {
		return
	}

	now := time.Now()
	accessMaxAge := int(s.config.Auth.AccessLifetime.Seconds())
	expires := now.Add(s.config.Auth.AccessLifetime)

	csrfCookie := &http.Cookie{
		Name:     "csrf_token",
		Value:    csrfToken,
		Domain:   s.config.GetEffectiveCookieDomain(),
		Path:     "/",
		MaxAge:   accessMaxAge,
		Expires:  expires,
		Secure:   s.config.Auth.CookieSecure,
		HttpOnly: false, // Important: NOT HttpOnly so JS can read it
		SameSite: s.config.GetCookieSameSite(),
	}

	http.SetCookie(w, csrfCookie)

	s.logger.Debug().
		Int("max_age", accessMaxAge).
		Time("expires", expires).
		Msg("CSRF cookie set successfully")
}

// Enhanced ClearAuthCookies
func (s *authServiceImpl) ClearAuthCookies(w http.ResponseWriter) {
	cookieNames := []string{"access_token", "refresh_token"}
	if s.config.Auth.CSRFEnabled {
		cookieNames = append(cookieNames, "csrf_token")
	}

	for _, name := range cookieNames {
		cookie := &http.Cookie{
			Name:     name,
			Value:    "",
			Domain:   s.config.GetEffectiveCookieDomain(),
			Path:     "/",
			MaxAge:   -1,
			Expires:  time.Unix(0, 0),
			Secure:   s.config.Auth.CookieSecure,
			HttpOnly: name != "csrf_token", // CSRF cookie is not HttpOnly
			SameSite: s.config.GetCookieSameSite(),
		}
		http.SetCookie(w, cookie)
	}

	s.logger.Debug().Msg("Auth cookies cleared successfully")
}

// GetTokenFromCookies retrieves tokens from cookies
func (s *authServiceImpl) GetTokenFromCookies(r *http.Request) (accessToken, refreshToken string) {
	if accessCookie, err := r.Cookie("access_token"); err == nil {
		accessToken = accessCookie.Value
	}

	if refreshCookie, err := r.Cookie("refresh_token"); err == nil {
		refreshToken = refreshCookie.Value
	}

	return
}

// GetCsrfToken retrieves CSRF token from cookie
func (s *authServiceImpl) GetCsrfToken(r *http.Request) string {
	if !s.config.Auth.CSRFEnabled {
		return ""
	}

	if csrfCookie, err := r.Cookie("csrf_token"); err == nil {
		return csrfCookie.Value
	}
	return ""
}

// VerifyCsrfToken verifies that the provided CSRF token matches the one in the cookie
func (s *authServiceImpl) VerifyCsrfToken(r *http.Request, providedToken string) bool {
	if !s.config.Auth.CSRFEnabled {
		return true // CSRF is disabled, always allow
	}

	cookieToken := s.GetCsrfToken(r)
	isValid := cookieToken != "" && providedToken != "" && cookieToken == providedToken

	s.logger.Debug().
		Bool("csrf_enabled", s.config.Auth.CSRFEnabled).
		Bool("has_cookie_token", cookieToken != "").
		Bool("has_provided_token", providedToken != "").
		Bool("tokens_match", cookieToken == providedToken).
		Bool("verification_result", isValid).
		Msg("CSRF token verification")

	return isValid
}

// Enhanced RefreshToken with better error handling and token rotation
func (s *authServiceImpl) RefreshToken(ctx context.Context, refreshToken string) (*TokenDetails, error) {
	// Parse refresh token
	claims := &RefreshTokenClaims{}
	token, err := jwt.ParseWithClaims(refreshToken, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.Auth.RefreshSecret), nil
	})

	if err != nil {
		if strings.Contains(err.Error(), "expired") || strings.Contains(err.Error(), "token is expired") {
			s.logger.Debug().Err(err).Msg("Refresh token expired")
			return nil, fmt.Errorf("refresh token expired")
		}
		s.logger.Warn().Err(err).Msg("Invalid refresh token format")
		return nil, fmt.Errorf("invalid refresh token")
	}

	if !token.Valid {
		s.logger.Warn().Msg("Invalid refresh token")
		return nil, fmt.Errorf("invalid refresh token")
	}

	// Validate token in database
	userID, err := s.tokenRepo.ValidateToken(ctx, claims.UUID)
	if err != nil {
		s.logger.Warn().
			Err(err).
			Str("uuid", claims.UUID).
			Msg("Refresh token not found or revoked")
		return nil, fmt.Errorf("refresh token not found or revoked")
	}

	// Ensure userID matches claims
	if userID != claims.UserID {
		s.logger.Warn().
			Int64("db_user_id", userID).
			Int64("token_user_id", claims.UserID).
			Str("uuid", claims.UUID).
			Msg("User ID mismatch in refresh token")
		return nil, fmt.Errorf("invalid refresh token")
	}

	// Get user from database
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		s.logger.Error().
			Err(err).
			Int64("user_id", userID).
			Msg("User not found during token refresh")
		return nil, fmt.Errorf("user not found")
	}

	// Check if user is still active
	if !user.Active {
		s.logger.Warn().
			Int64("user_id", userID).
			Msg("User account is disabled")
		return nil, fmt.Errorf("account is disabled")
	}

	// Revoke the current refresh token (token rotation for security)
	if err := s.tokenRepo.RevokeToken(ctx, claims.UUID); err != nil {
		s.logger.Warn().
			Err(err).
			Str("uuid", claims.UUID).
			Msg("Failed to revoke old refresh token")
		// Continue anyway, the new token will still be valid
	}

	// Generate new tokens
	newTokens, err := s.createTokens(ctx, user)
	if err != nil {
		s.logger.Error().
			Err(err).
			Int64("user_id", user.ID).
			Msg("Failed to generate new tokens during refresh")
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	s.logger.Info().
		Int64("user_id", user.ID).
		Str("old_uuid", claims.UUID).
		Str("new_uuid", newTokens.RefreshUuid).
		Int64("expires_in", newTokens.ExpiresIn).
		Msg("Tokens refreshed successfully")

	return newTokens, nil
}

// Enhanced ValidateToken with CSRF token validation
func (s *authServiceImpl) ValidateToken(accessToken string) (*AccessTokenClaims, error) {
	claims := &AccessTokenClaims{}
	token, err := jwt.ParseWithClaims(accessToken, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.config.Auth.AccessSecret), nil
	})

	if err != nil {
		if strings.Contains(err.Error(), "expired") || strings.Contains(err.Error(), "token is expired") {
			return nil, fmt.Errorf("token is expired")
		}
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	return claims, nil
}

// IsTokenNearExpiry checks if token should be refreshed proactively
func (s *authServiceImpl) IsTokenNearExpiry(claims *AccessTokenClaims) bool {
	if claims.ExpiresAt == nil {
		return true
	}

	expiryTime := claims.ExpiresAt.Time
	refreshThreshold := time.Now().Add(s.config.Auth.RefreshThreshold)

	return expiryTime.Before(refreshThreshold)
}

// Enhanced createTokens with better CSRF integration and expiry calculation
func (s *authServiceImpl) createTokens(ctx context.Context, user *models.User) (*TokenDetails, error) {
	now := time.Now()
	atExpires := now.Add(s.config.Auth.AccessLifetime)
	rtExpires := now.Add(s.config.Auth.RefreshLifetime)

	td := &TokenDetails{
		AtExpires:   atExpires.Unix(),
		RtExpires:   rtExpires.Unix(),
		AccessUuid:  uuid.NewString(),
		RefreshUuid: uuid.NewString(),
		TokenType:   "Bearer",
		ExpiresIn:   int64(s.config.Auth.AccessLifetime.Seconds()),
	}

	// Generate CSRF token
	var csrfToken string
	if s.config.Auth.CSRFEnabled {
		csrfBytes := make([]byte, 32)
		if _, err := rand.Read(csrfBytes); err != nil {
			return nil, fmt.Errorf("failed to generate CSRF token: %w", err)
		}
		csrfToken = base64.URLEncoding.EncodeToString(csrfBytes)
	}
	td.CsrfToken = csrfToken

	// Create access token
	atClaims := AccessTokenClaims{
		UserID:    user.ID,
		Username:  user.Username,
		Role:      user.Role,
		UUID:      td.AccessUuid,
		CsrfToken: csrfToken,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(atExpires),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Subject:   fmt.Sprintf("%d", user.ID),
		},
	}

	at := jwt.NewWithClaims(jwt.SigningMethodHS256, atClaims)
	accessToken, err := at.SignedString([]byte(s.config.Auth.AccessSecret))
	if err != nil {
		return nil, fmt.Errorf("failed to sign access token: %w", err)
	}
	td.AccessToken = accessToken

	// Create refresh token
	rtClaims := RefreshTokenClaims{
		UserID: user.ID,
		UUID:   td.RefreshUuid,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(rtExpires),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Subject:   fmt.Sprintf("%d", user.ID),
			ID:        td.RefreshUuid, // Add jti claim
		},
	}

	rt := jwt.NewWithClaims(jwt.SigningMethodHS256, rtClaims)
	refreshToken, err := rt.SignedString([]byte(s.config.Auth.RefreshSecret))
	if err != nil {
		return nil, fmt.Errorf("failed to sign refresh token: %w", err)
	}
	td.RefreshToken = refreshToken

	// Store refresh token in database
	err = s.tokenRepo.StoreToken(ctx, user.ID, td.RefreshUuid, rtExpires)
	if err != nil {
		return nil, fmt.Errorf("failed to store refresh token: %w", err)
	}

	s.logger.Debug().
		Int64("user_id", user.ID).
		Str("access_uuid", td.AccessUuid).
		Str("refresh_uuid", td.RefreshUuid).
		Time("at_expires", atExpires).
		Time("rt_expires", rtExpires).
		Int64("expires_in", td.ExpiresIn).
		Bool("csrf_enabled", s.config.Auth.CSRFEnabled).
		Msg("Tokens created successfully")

	return td, nil
}

// Register creates a new user account
func (s *authServiceImpl) Register(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error) {
	// Check if email already exists
	existingByEmail, err := s.userRepo.FindByEmail(ctx, req.Email)
	if err == nil && existingByEmail != nil {
		return nil, fmt.Errorf("email already in use")
	}

	// Check if username already exists
	existingByUsername, err := s.userRepo.FindByUsername(ctx, req.Username)
	if err == nil && existingByUsername != nil {
		return nil, fmt.Errorf("username already in use")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create new user
	user := &models.User{
		Email:     req.Email,
		Username:  req.Username,
		Password:  string(hashedPassword),
		Role:      req.Role,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Active:    true, // New users are active by default
	}

	// Save user to database
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Convert to response object
	userResponse := user.ToResponse()

	return &userResponse, nil
}

// Enhanced Logout with better cleanup
func (s *authServiceImpl) Logout(ctx context.Context, w http.ResponseWriter, refreshToken string) error {
	// If refresh token is provided, parse it to get the UUID
	if refreshToken != "" {
		claims := &RefreshTokenClaims{}
		token, err := jwt.ParseWithClaims(refreshToken, claims, func(token *jwt.Token) (interface{}, error) {
			return []byte(s.config.Auth.RefreshSecret), nil
		})

		// Only proceed if token is valid
		if err == nil && token.Valid {
			// Revoke the refresh token
			if err := s.tokenRepo.RevokeToken(ctx, claims.UUID); err != nil {
				s.logger.Warn().
					Err(err).
					Str("uuid", claims.UUID).
					Msg("Failed to revoke refresh token during logout")
			} else {
				s.logger.Debug().
					Str("uuid", claims.UUID).
					Int64("user_id", claims.UserID).
					Msg("Refresh token revoked during logout")
			}
		}
	}

	// Clear cookies
	s.ClearAuthCookies(w)

	s.logger.Info().Msg("User logged out successfully")
	return nil
}

// ChangePassword changes a user's password
func (s *authServiceImpl) ChangePassword(ctx context.Context, userID int64, currentPassword, newPassword string) error {
	// Get user from database
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("user not found")
	}

	// Verify current password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(currentPassword)); err != nil {
		return fmt.Errorf("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update password in database
	if err := s.userRepo.UpdatePassword(ctx, userID, string(hashedPassword)); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}

func (s *authServiceImpl) GetUserByID(ctx context.Context, id int64) (*models.UserResponse, error) {
	user, err := s.userRepo.FindByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	userResponse := user.ToResponse()
	return &userResponse, nil
}

func (s *authServiceImpl) ListUsers(ctx context.Context, page, pageSize int) ([]*models.UserResponse, int64, error) {
	users, total, err := s.userRepo.FindAll(ctx, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", err)
	}

	// Convert to response objects
	userResponses := make([]*models.UserResponse, len(users))
	for i, user := range users {
		resp := user.ToResponse()
		userResponses[i] = &resp
	}

	return userResponses, total, nil
}

func (s *authServiceImpl) UpdateUser(ctx context.Context, userID int64, req *models.UpdateUserRequest) (*models.UserResponse, error) {
	// Check if user exists
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("user not found")
	}

	// Update fields
	if req.Email != "" && req.Email != user.Email {
		// Check if email is already used by another user
		existingUser, err := s.userRepo.FindByEmail(ctx, req.Email)
		if err == nil && existingUser != nil && existingUser.ID != userID {
			return nil, fmt.Errorf("email already in use")
		}
		user.Email = req.Email
	}

	if req.Username != "" && req.Username != user.Username {
		// Check if username is already used by another user
		existingUser, err := s.userRepo.FindByUsername(ctx, req.Username)
		if err == nil && existingUser != nil && existingUser.ID != userID {
			return nil, fmt.Errorf("username already in use")
		}
		user.Username = req.Username
	}

	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}

	if req.LastName != "" {
		user.LastName = req.LastName
	}

	if req.Role != "" {
		user.Role = models.Role(req.Role)
	}

	if req.Active != nil {
		user.Active = *req.Active
	}

	// Update user in database
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Convert to response object
	userResponse := user.ToResponse()
	return &userResponse, nil
}

func (s *authServiceImpl) DeleteUser(ctx context.Context, userID int64) error {
	// Check if user exists
	_, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("user not found")
	}

	// Revoke all user tokens
	if err := s.tokenRepo.RevokeAllUserTokens(ctx, userID); err != nil {
		s.logger.Warn().
			Err(err).
			Int64("user_id", userID).
			Msg("Failed to revoke all user tokens during user deletion")
	}

	// Delete user from database
	if err := s.userRepo.Delete(ctx, userID); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

func (s *authServiceImpl) RefreshTokenForUser(ctx context.Context, userID int64) (*TokenDetails, error) {
	// Get the user from database
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// Generate new tokens
	tokens, err := s.createTokens(ctx, user)
	if err != nil {
		s.logger.Error().
			Err(err).
			Int64("user_id", user.ID).
			Msg("Failed to generate new tokens for CSRF fix")
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	s.logger.Info().
		Int64("user_id", user.ID).
		Msg("Tokens generated for CSRF fix")

	return tokens, nil
}

// LogoutAllSessions logs out user from all devices/sessions
func (s *authServiceImpl) LogoutAllSessions(ctx context.Context, w http.ResponseWriter, userID int64) error {
	// Revoke all refresh tokens for the user
	err := s.tokenRepo.RevokeAllUserTokens(ctx, userID)
	if err != nil {
		s.logger.Error().
			Err(err).
			Int64("user_id", userID).
			Msg("Failed to revoke all user tokens")
		return fmt.Errorf("failed to revoke all sessions: %w", err)
	}

	// Clear cookies for the current session
	s.ClearAuthCookies(w)

	s.logger.Info().
		Int64("user_id", userID).
		Msg("User logged out from all sessions")

	return nil
}

// GetActiveSessions returns all active sessions for a user
func (s *authServiceImpl) GetActiveSessions(ctx context.Context, userID int64) ([]models.SessionInfo, error) {
	// Get all active refresh tokens for the user
	tokens, err := s.tokenRepo.GetActiveTokensByUserID(ctx, userID)
	if err != nil {
		s.logger.Error().
			Err(err).
			Int64("user_id", userID).
			Msg("Failed to get active sessions")
		return nil, fmt.Errorf("failed to get active sessions: %w", err)
	}

	// Convert tokens to session info
	sessions := make([]models.SessionInfo, len(tokens))
	for i, token := range tokens {
		sessions[i] = models.SessionInfo{
			ID:        token.TokenID, // Use TokenID instead of UUID
			CreatedAt: token.CreatedAt,
			ExpiresAt: token.ExpiresAt,
			LastUsed:  token.UpdatedAt,
			UserAgent: token.UserAgent,
			IPAddress: token.IPAddress,
			IsCurrent: false, // We'll determine this if needed
		}
	}

	s.logger.Debug().
		Int64("user_id", userID).
		Int("session_count", len(sessions)).
		Msg("Retrieved active sessions")

	return sessions, nil
}

// RevokeSession revokes a specific session by token ID
func (s *authServiceImpl) RevokeSession(ctx context.Context, userID int64, sessionID string) error {
	// Verify the session belongs to the user and revoke it
	err := s.tokenRepo.RevokeTokenByUUIDAndUserID(ctx, sessionID, userID)
	if err != nil {
		s.logger.Error().
			Err(err).
			Int64("user_id", userID).
			Str("session_id", sessionID).
			Msg("Failed to revoke session")
		return fmt.Errorf("failed to revoke session: %w", err)
	}

	s.logger.Info().
		Int64("user_id", userID).
		Str("session_id", sessionID).
		Msg("Session revoked successfully")

	return nil
}

// ClearCsrfCookie clears only the CSRF cookie
func (s *authServiceImpl) ClearCsrfCookie(w http.ResponseWriter) {
	if !s.config.Auth.CSRFEnabled {
		return
	}

	cookie := &http.Cookie{
		Name:     "csrf_token",
		Value:    "",
		Domain:   s.config.GetEffectiveCookieDomain(),
		Path:     "/",
		MaxAge:   -1,
		Expires:  time.Unix(0, 0),
		Secure:   s.config.Auth.CookieSecure,
		HttpOnly: false, // CSRF cookie is not HttpOnly
		SameSite: s.config.GetCookieSameSite(),
	}
	http.SetCookie(w, cookie)

	s.logger.Debug().Msg("CSRF cookie cleared")
}
