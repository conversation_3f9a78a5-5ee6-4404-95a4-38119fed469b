package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

func RegisterInteractionRoutes(router *gin.RouterGroup, handler *handlers.InteractionHandler, authMiddleware *middleware.AuthMiddleware) {
	interactions := router.Group("/interactions")
	interactions.Use(authMiddleware.Authenticate())
	{
		// CRUD operations
		interactions.POST("", handler.CreateInteraction)
		interactions.GET("", handler.GetInteractions)
		interactions.GET("/:id", handler.GetInteraction)
		interactions.PUT("/:id", handler.UpdateInteraction)
		interactions.DELETE("/:id", handler.DeleteInteraction)

		// Relationship operations
		interactions.GET("/scenario/:scenario_id", handler.GetInteractionsByScenario)
		interactions.GET("/character/:character_id", handler.GetInteractionsByCharacter)

		// Behaviour management
		interactions.POST("/:id/behaviours/:behaviour_id", handler.AddBehaviourToInteraction)
		interactions.DELETE("/:id/behaviours/:behaviour_id", handler.RemoveBehaviourFromInteraction)
	}
}
