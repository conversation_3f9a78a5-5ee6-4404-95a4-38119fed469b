package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

func RegisterOptionRoutes(router *gin.RouterGroup, handler *handlers.OptionHandler, authMiddleware *middleware.AuthMiddleware) {
	options := router.Group("/options")
	options.Use(authMiddleware.Authenticate())
	{
		// CRUD operations
		options.POST("", handler.CreateOption)
		options.GET("", handler.GetOptions)
		options.GET("/:id", handler.GetOption)
		options.PUT("/:id", handler.UpdateOption)
		options.DELETE("/:id", handler.DeleteOption)

		// Interaction-specific operations
		options.GET("/interaction/:interaction_id", handler.GetOptionsByInteraction)
		options.GET("/interaction/:interaction_id/correct", handler.GetCorrectOptionsByInteraction)
		options.POST("/interaction/:interaction_id/validate", handler.ValidateInteractionOptions)
	}
}
