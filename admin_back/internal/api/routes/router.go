// internal/api/routes/router.go - Fixed CSRF integration

package routes

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
	"github.com/psynarios/admin_back/internal/common/config"
	"github.com/rs/zerolog"
)

// SetupRouter configures the API router with enhanced authentication and optional CSRF
func SetupRouter(
	logger zerolog.Logger,
	authHandler *handlers.AuthHandler,
	userHandler *handlers.UserHandler,
	companyHandler *handlers.CompanyHandler,
	mailHandler *handlers.MailHandler,
	templateHandler *handlers.TemplateHandler,
	scraperHandler *handlers.ScraperHandler,
	mediaHandler *handlers.MediaHandler,
	// Library handlers
	scenarioHandler *handlers.ScenarioHandler,
	characterHandler *handlers.CharacterHandler,
	interactionHandler *handlers.<PERSON>action<PERSON><PERSON><PERSON>,
	optionHandler *handlers.<PERSON><PERSON><PERSON><PERSON><PERSON>,
	behaviourHandler *handlers.BehaviourHandler,
	authMiddleware *middleware.AuthMiddleware,
	csrfMiddleware *middleware.CsrfMiddleware, // Can be nil when CSRF is disabled
	cfg *config.Config,
	healthHandler *handlers.HealthHandler,
) *gin.Engine {
	// Create router with default middleware
	router := gin.New()

	// Add custom middleware
	router.Use(middleware.CORS())
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.Logger(logger))

	// Setup route groups
	api := router.Group("/api")

	// Health check endpoint (supports both GET and HEAD methods for better Docker health checks)
	api.GET("/health", healthHandler.CheckHealth)
	api.HEAD("/health", healthHandler.CheckHealth)

	// Simple health check fallback (lightweight version)
	api.GET("/ping", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":       "ok",
			"timestamp":    time.Now(),
			"csrf_enabled": cfg.Auth.CSRFEnabled,
			"environment":  cfg.Environment,
		})
	})

	// Setup auth routes (no CSRF for login/register/refresh)
	SetupAuthRoutes(api, authHandler, authMiddleware, cfg)

	// Setup other routes with conditional CSRF protection
	if cfg.Auth.CSRFEnabled && csrfMiddleware != nil {
		logger.Info().Msg("CSRF protection enabled for API routes")

		// Create protected API group with CSRF protection
		// Exempt auth endpoints except for protected ones
		protectedAPI := api.Group("")
		protectedAPI.Use(csrfMiddleware.ExemptPrefix(
			"/api/auth/login",
			"/api/auth/register",
			"/api/auth/refresh",
			"/api/auth/logout",
			"/api/auth/debug",
			"/api/auth/csrf", // Allow CSRF endpoint without CSRF validation
			"/api/health",
			"/api/ping",
		))

		// Setup protected routes with CSRF
		SetupUserRoutes(protectedAPI, userHandler, authMiddleware)
		SetupCompanyRoutes(protectedAPI, companyHandler, authMiddleware)
		SetupMailRoutes(protectedAPI, mailHandler, authMiddleware)
		SetupTemplateRoutes(protectedAPI, templateHandler, authMiddleware)
		SetupScraperRoutes(protectedAPI, scraperHandler, authMiddleware)
		SetupMediaRoutes(protectedAPI, mediaHandler, authMiddleware)

		// Setup library routes with CSRF protection
		RegisterLibraryRoutes(protectedAPI, scenarioHandler, characterHandler, interactionHandler, optionHandler, behaviourHandler, authMiddleware)
	} else {
		// Setup routes without CSRF protection
		if !cfg.Auth.CSRFEnabled {
			logger.Info().Msg("CSRF protection disabled")
		} else {
			logger.Warn().Msg("CSRF is enabled but middleware is nil")
		}

		SetupUserRoutes(api, userHandler, authMiddleware)
		SetupCompanyRoutes(api, companyHandler, authMiddleware)
		SetupMailRoutes(api, mailHandler, authMiddleware)
		SetupTemplateRoutes(api, templateHandler, authMiddleware)
		SetupScraperRoutes(api, scraperHandler, authMiddleware)
		SetupMediaRoutes(api, mediaHandler, authMiddleware)

		// Setup library routes without CSRF protection
		RegisterLibraryRoutes(api, scenarioHandler, characterHandler, interactionHandler, optionHandler, behaviourHandler, authMiddleware)
	}

	// Add debug endpoints for development
	if cfg.IsDevelopment() {
		api.GET("/debug/auth", authMiddleware.OptionalAuth(), func(c *gin.Context) {
			authenticated, _ := c.Get("authenticated")
			userID, _ := c.Get("user_id")
			username, _ := c.Get("username")
			role, _ := c.Get("role")

			c.JSON(200, gin.H{
				"authenticated": authenticated,
				"user_id":       userID,
				"username":      username,
				"role":          role,
				"csrf_enabled":  cfg.Auth.CSRFEnabled,
				"environment":   cfg.Environment,
			})
		})

		// Add CSRF debug endpoint if enabled
		if cfg.Auth.CSRFEnabled && csrfMiddleware != nil {
			api.GET("/debug/csrf-status", csrfMiddleware.GetCSRFToken())
		}
	}

	// Add media file serving route
	if cfg.Storage.BasePath != "" {
		router.Static("/api/media/files", cfg.Storage.BasePath)
		logger.Info().
			Str("path", "/api/media/files").
			Str("directory", cfg.Storage.BasePath).
			Msg("Static file serving enabled for media files")
	}

	return router
}
