// internal/api/routes/auth_routes.go
package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
	"github.com/psynarios/admin_back/internal/common/config"
)

// SetupAuthRoutes configures authentication-related routes
func SetupAuthRoutes(
	router *gin.RouterGroup,
	authHandler *handlers.AuthHandler,
	authMiddleware *middleware.AuthMiddleware,
	cfg *config.Config,
) {
	// Create auth group
	auth := router.Group("/auth")

	// Public authentication endpoints (no CSRF required)
	auth.POST("/register", authHandler.Register)
	auth.POST("/login", authHandler.Login)
	auth.POST("/refresh", authHandler.RefreshToken)
	auth.POST("/logout", authHandler.Logout)

	// CSRF token endpoint (requires authentication but no CSRF validation)
	auth.GET("/csrf", authMiddleware.Authenticate(), func(c *gin.Context) {
		// This endpoint helps frontend get CSRF token when needed
		authHandler.GetCSRFToken(c)
	})

	// Protected endpoints (require authentication and CSRF if enabled)
	protected := auth.Group("")
	protected.Use(authMiddleware.Authenticate())

	// User profile endpoints
	protected.GET("/profile", authHandler.GetProfile)
	protected.PUT("/profile", authHandler.UpdateProfile)
	protected.POST("/change-password", authHandler.ChangePassword)

	// Session management
	protected.GET("/validate", authHandler.ValidateToken)
	protected.GET("/status", authHandler.CheckAuthStatus)
	protected.POST("/logout-all", authHandler.LogoutAllSessions)

	// Session management endpoints
	protected.GET("/sessions", authHandler.GetActiveSessions)
	protected.DELETE("/sessions/:sessionId", authHandler.RevokeSession)

	// Alternative endpoint for current user (backward compatibility)
	protected.GET("/me", authHandler.GetCurrentUser)

	// Development/debug endpoints (only in development)
	if cfg.IsDevelopment() {
		// Debug endpoint for CSRF status
		auth.GET("/debug/csrf", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"csrf_enabled": cfg.Auth.CSRFEnabled,
				"environment":  cfg.Environment,
				"message":      "CSRF debug endpoint",
			})
		})

		// Debug endpoint for token validation
		auth.GET("/debug/token", authMiddleware.OptionalAuth(), func(c *gin.Context) {
			authenticated, _ := c.Get("authenticated")
			userID, _ := c.Get("user_id")
			username, _ := c.Get("username")
			role, _ := c.Get("role")

			c.JSON(200, gin.H{
				"authenticated": authenticated,
				"user_id":       userID,
				"username":      username,
				"role":          role,
				"csrf_enabled":  cfg.Auth.CSRFEnabled,
				"environment":   cfg.Environment,
			})
		})
	}
}
