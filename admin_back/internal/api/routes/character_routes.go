package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

func RegisterCharacterRoutes(router *gin.RouterGroup, handler *handlers.CharacterHandler, authMiddleware *middleware.AuthMiddleware) {
	characters := router.Group("/characters")
	characters.Use(authMiddleware.Authenticate())
	{
		// CRUD operations
		characters.POST("", handler.CreateCharacter)
		characters.GET("", handler.GetCharacters)
		characters.GET("/:id", handler.GetCharacter)
		characters.PUT("/:id", handler.UpdateCharacter)
		characters.DELETE("/:id", handler.DeleteCharacter)

		// Search and filter operations
		characters.GET("/search", handler.SearchCharacters)
		characters.GET("/scenario/:scenario_id", handler.GetCharactersByScenario)
	}
}
