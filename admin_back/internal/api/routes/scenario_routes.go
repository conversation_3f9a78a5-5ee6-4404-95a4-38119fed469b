package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

func RegisterScenarioRoutes(router *gin.RouterGroup, handler *handlers.ScenarioHandler, authMiddleware *middleware.AuthMiddleware) {
	scenarios := router.Group("/scenarios")
	scenarios.Use(authMiddleware.Authenticate())
	{
		// CRUD operations
		scenarios.POST("", handler.CreateScenario)
		scenarios.GET("", handler.GetScenarios)
		scenarios.GET("/:id", handler.GetScenario)
		scenarios.PUT("/:id", handler.UpdateScenario)
		scenarios.DELETE("/:id", handler.DeleteScenario)

		// Get by code
		scenarios.GET("/code/:code", handler.GetScenarioByCode)

		// Search and filter operations
		scenarios.GET("/search", handler.SearchScenarios)
		scenarios.GET("/difficulty/:difficulty", handler.GetScenariosByDifficulty)
		scenarios.GET("/type/:type", handler.GetScenariosByType)

		// Statistics
		scenarios.GET("/statistics", handler.GetScenarioStatistics)
	}
}
