package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

func RegisterLibraryRoutes(
	router *gin.RouterGroup,
	scenarioHandler *handlers.<PERSON>enarioHandler,
	characterHandler *handlers.CharacterHandler,
	interactionHandler *handlers.InteractionHandler,
	optionHandler *handlers.OptionHandler,
	behaviourHandler *handlers.BehaviourHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	// Library group
	library := router.Group("/library")
	{
		// Register all library component routes
		RegisterScenarioRoutes(library, scenarioHandler, authMiddleware)
		RegisterCharacterRoutes(library, characterHandler, authMiddleware)
		RegisterInteractionRoutes(library, interactionHandler, authMiddleware)
		RegisterOptionRoutes(library, optionHandler, authMiddleware)
		RegisterBehaviourRoutes(library, behaviourHandler, authMiddleware)

		// Library-wide endpoints
		library.GET("/stats", authMiddleware.Authenticate(), func(c *gin.Context) {
			c.J<PERSON>(200, gin.H{
				"message": "Library statistics endpoint",
				"endpoints": gin.H{
					"scenarios":    "/library/scenarios/statistics",
					"characters":   "/library/characters/statistics",
					"interactions": "/library/interactions/statistics",
					"options":      "/library/options/statistics",
					"behaviours":   "/library/behaviours/statistics",
				},
			})
		})

		// Health check
		library.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status": "healthy",
				"components": gin.H{
					"scenarios":    "operational",
					"characters":   "operational",
					"interactions": "operational",
					"options":      "operational",
					"behaviours":   "operational",
				},
			})
		})
	}
}
