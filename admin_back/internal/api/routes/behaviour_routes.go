package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

func RegisterBehaviourRoutes(router *gin.RouterGroup, handler *handlers.BehaviourHandler, authMiddleware *middleware.AuthMiddleware) {
	behaviours := router.Group("/behaviours")
	behaviours.Use(authMiddleware.Authenticate())
	{
		// CRUD operations
		behaviours.POST("", handler.CreateBehaviour)
		behaviours.GET("", handler.GetBehaviours)
		behaviours.GET("/:id", handler.GetBehaviour)
		behaviours.PUT("/:id", handler.UpdateBehaviour)
		behaviours.DELETE("/:id", handler.DeleteBehaviour)

		// Search operations
		behaviours.GET("/search", handler.SearchBehaviours)
	}
}
