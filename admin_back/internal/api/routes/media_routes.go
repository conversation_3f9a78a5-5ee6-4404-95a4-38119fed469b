package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

// SetupMediaRoutes configures the media-related routes
func SetupMediaRoutes(
	router *gin.RouterGroup,
	handler *handlers.MediaHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	// Main media group for all media-related endpoints
	mediaGroup := router.Group("/media")

	// Public endpoints (no authentication required)
	// These allow public access to media files and basic info
	mediaGroup.GET("/:id", handler.GetMediaByID)
	mediaGroup.GET("/:id/info", handler.GetMediaInfo) // Basic info without sensitive data

	// Public search and listing (with limited data)
	mediaGroup.GET("/public", handler.ListPublicMedia) // Only public/published media

	// Protected endpoints (require authentication)
	protectedGroup := mediaGroup.Group("")
	protectedGroup.Use(authMiddleware.Authenticate())

	// Media operations available to all authenticated users
	protectedGroup.POST("/upload", handler.UploadMedia)
	protectedGroup.GET("", handler.ListMedia)           // List user's media or filtered
	protectedGroup.GET("/search", handler.SearchMedia)  // Search functionality
	protectedGroup.GET("/my", handler.GetMyMedia)       // User's own media
	protectedGroup.POST("/:id/copy", handler.CopyMedia) // Copy media to different language
	protectedGroup.PUT("/:id", handler.UpdateMedia)     // Update own media
	protectedGroup.DELETE("/:id", handler.DeleteMedia)  // Delete own media (with ownership check)

	// Statistics endpoints (authenticated users can see their own stats)
	protectedGroup.GET("/stats", handler.GetMediaStats)
	protectedGroup.GET("/stats/my", handler.GetMyMediaStats)

	// Admin-only endpoints
	adminGroup := mediaGroup.Group("/admin")
	adminGroup.Use(authMiddleware.Authenticate())
	adminGroup.Use(authMiddleware.RequireRole("admin"))

	// Admin operations for managing all media
	adminGroup.GET("", handler.ListAllMedia)                     // List all media in system
	adminGroup.GET("/stats/global", handler.GetGlobalMediaStats) // Global statistics
	adminGroup.GET("/orphaned", handler.ListOrphanedMedia)       // Media not referenced anywhere
	adminGroup.POST("/cleanup", handler.CleanupOrphanedMedia)    // Remove orphaned media
	adminGroup.DELETE("/:id/force", handler.ForceDeleteMedia)    // Force delete any media
	adminGroup.PUT("/:id/admin", handler.AdminUpdateMedia)       // Admin update (can change owner, etc.)

	// System maintenance endpoints
	adminGroup.POST("/reprocess/:id", handler.ReprocessMedia) // Reprocess media (extract metadata, etc.)
	adminGroup.POST("/migrate", handler.MigrateMediaStorage)  // Migrate storage locations
	adminGroup.GET("/health", handler.CheckMediaHealth)       // Check storage health

	// Bulk operations (admin only)
	bulkGroup := adminGroup.Group("/bulk")
	bulkGroup.POST("/delete", handler.BulkDeleteMedia) // Bulk delete
	bulkGroup.POST("/update", handler.BulkUpdateMedia) // Bulk update
	bulkGroup.POST("/move", handler.BulkMoveMedia)     // Bulk move between languages
}
