// internal/api/middleware/csrf.go - Fixed version to match your config structure

package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/auth/service"
	"github.com/psynarios/admin_back/internal/common/config"
)

// CsrfMiddleware handles CSRF protection
type CsrfMiddleware struct {
	authService service.AuthService
	config      *config.Config
	logger      zerolog.Logger
}

// NewCsrfMiddleware creates a new CSRF middleware - FIXED to accept config
func NewCsrfMiddleware(authService service.AuthService, cfg *config.Config, logger zerolog.Logger) *CsrfMiddleware {
	return &CsrfMiddleware{
		authService: authService,
		config:      cfg,
		logger:      logger.With().Str("component", "csrf_middleware").Logger(),
	}
}

// VerifyCsrf verifies CSRF token for state-changing requests
func (m *CsrfMiddleware) VerifyCsrf() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip if CSRF is disabled
		if !m.config.Auth.CSRFEnabled {
			c.Next()
			return
		}

		// Early return for preflight OPTIONS request
		if c.Request.Method == http.MethodOptions {
			c.Next()
			return
		}

		// Only check CSRF for state-changing methods
		if c.Request.Method == http.MethodGet || c.Request.Method == http.MethodHead {
			c.Next()
			return
		}

		// Get CSRF token from header
		csrfToken := c.GetHeader("X-CSRF-Token")

		// Also try alternative header names
		if csrfToken == "" {
			csrfToken = c.GetHeader("X-CSRFToken")
		}
		if csrfToken == "" {
			csrfToken = c.GetHeader("csrf-token")
		}

		// Log the token for debugging
		m.logger.Debug().
			Str("path", c.Request.URL.Path).
			Str("method", c.Request.Method).
			Str("csrf_token_header", csrfToken).
			Bool("has_csrf_cookie", m.authService.GetCsrfToken(c.Request) != "").
			Msg("CSRF token check")

		// Check if token exists
		if csrfToken == "" {
			cookieToken := m.authService.GetCsrfToken(c.Request)

			m.logger.Warn().
				Str("path", c.Request.URL.Path).
				Str("method", c.Request.Method).
				Str("ip", c.ClientIP()).
				Bool("has_cookie_token", cookieToken != "").
				Msg("CSRF token missing from header")

			// Provide helpful error message
			var message string
			if cookieToken != "" {
				message = "CSRF token must be provided in X-CSRF-Token header"
			} else {
				message = "CSRF token is required. Please refresh the page and try again."
			}

			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":   "csrf_token_missing",
				"message": message,
			})
			return
		}

		// Verify CSRF token
		if !m.authService.VerifyCsrfToken(c.Request, csrfToken) {
			cookieToken := m.authService.GetCsrfToken(c.Request)

			m.logger.Warn().
				Str("path", c.Request.URL.Path).
				Str("method", c.Request.Method).
				Str("ip", c.ClientIP()).
				Str("token_from_header", csrfToken).
				Str("token_from_cookie", cookieToken).
				Bool("tokens_match", cookieToken == csrfToken).
				Msg("CSRF token validation failed")

			// Provide specific error message
			var message string
			if cookieToken == "" {
				message = "CSRF session expired. Please refresh the page."
			} else if csrfToken != cookieToken {
				message = "CSRF token mismatch. Please refresh the page."
			} else {
				message = "Invalid CSRF token"
			}

			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":   "csrf_validation_failed",
				"message": message,
			})
			return
		}

		m.logger.Debug().
			Str("path", c.Request.URL.Path).
			Str("method", c.Request.Method).
			Msg("CSRF token validated successfully")

		c.Next()
	}
}

// ExemptPaths specifies paths that are exempt from CSRF protection
func (m *CsrfMiddleware) ExemptPaths(paths ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if current path is in exempt list
		currentPath := c.Request.URL.Path
		for _, path := range paths {
			if path == currentPath {
				m.logger.Debug().
					Str("path", currentPath).
					Str("exempt_path", path).
					Msg("Path exempt from CSRF protection")
				c.Next()
				return
			}
		}

		// Apply CSRF verification for non-exempt paths
		m.VerifyCsrf()(c)
	}
}

// ExemptPrefix specifies path prefixes that are exempt from CSRF protection
func (m *CsrfMiddleware) ExemptPrefix(prefixes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if current path starts with any exempt prefix
		currentPath := c.Request.URL.Path
		for _, prefix := range prefixes {
			if strings.HasPrefix(currentPath, prefix) {
				m.logger.Debug().
					Str("path", currentPath).
					Str("exempt_prefix", prefix).
					Msg("Path prefix exempt from CSRF protection")
				c.Next()
				return
			}
		}

		// Apply CSRF verification for non-exempt paths
		m.VerifyCsrf()(c)
	}
}

// GetCSRFToken endpoint returns the current CSRF token
func (m *CsrfMiddleware) GetCSRFToken() gin.HandlerFunc {
	return func(c *gin.Context) {
		if !m.config.Auth.CSRFEnabled {
			c.JSON(http.StatusOK, gin.H{
				"csrf_enabled": false,
				"message":      "CSRF protection is disabled",
			})
			return
		}

		// Try to get existing token from cookie
		existingToken := m.authService.GetCsrfToken(c.Request)

		if existingToken == "" {
			m.logger.Warn().
				Str("path", c.Request.URL.Path).
				Str("ip", c.ClientIP()).
				Msg("No CSRF token found for request")

			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "no_csrf_token",
				"message": "No CSRF token available. Please log in first.",
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"csrf_token":   existingToken,
			"csrf_enabled": true,
			"message":      "CSRF token retrieved successfully",
		})
	}
}
