// internal/api/middleware/auth.go
package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/auth/models"
	"github.com/psynarios/admin_back/internal/auth/service"
)

// AuthMiddleware handles authentication and authorization with enhanced features
type AuthMiddleware struct {
	authService service.AuthService
	logger      zerolog.Logger
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(authService service.AuthService, logger zerolog.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		authService: authService,
		logger:      logger.With().Str("component", "auth_middleware").Logger(),
	}
}

// Authenticate checks if the user is authenticated with auto-refresh capability
func (m *AuthMiddleware) Authenticate() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get tokens from cookies
		accessToken, refreshToken := m.authService.GetTokenFromCookies(c.Request)

		// If no access token in cookie, check Authorization header as fallback
		if accessToken == "" {
			authHeader := c.GetHeader("Authorization")
			if len(authHeader) > 7 && strings.HasPrefix(authHeader, "Bearer ") {
				accessToken = authHeader[7:]
			}
		}

		// If still no access token, try to refresh using refresh token
		if accessToken == "" {
			if refreshToken != "" {
				if m.attemptTokenRefresh(c, refreshToken) {
					return // Success, request will continue with new tokens
				}
			}

			m.logger.Debug().
				Str("path", c.Request.URL.Path).
				Str("ip", c.ClientIP()).
				Bool("has_refresh_token", refreshToken != "").
				Msg("Authentication required but no valid token provided")

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Please log in to access this resource",
			})
			return
		}

		// Validate the access token
		claims, err := m.authService.ValidateToken(accessToken)
		if err != nil {
			// Token validation failed
			if strings.Contains(err.Error(), "token is expired") {
				// Try to refresh if we have a refresh token
				if refreshToken != "" {
					if m.attemptTokenRefresh(c, refreshToken) {
						return // Success, request will continue with new tokens
					}
				}

				m.logger.Debug().
					Err(err).
					Str("path", c.Request.URL.Path).
					Bool("has_refresh_token", refreshToken != "").
					Msg("Access token expired and refresh failed or unavailable")

				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
					"error":   "token_expired",
					"message": "Access token expired, please refresh or log in again",
				})
				return
			}

			// Other token validation errors
			m.logger.Warn().
				Err(err).
				Str("path", c.Request.URL.Path).
				Msg("Invalid access token")

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error":   "invalid_token",
				"message": "Invalid access token",
			})
			return
		}

		// Check if token is close to expiry (proactive refresh)
		if m.authService.IsTokenNearExpiry(claims) && refreshToken != "" {
			m.logger.Debug().
				Int64("user_id", claims.UserID).
				Str("path", c.Request.URL.Path).
				Msg("Proactively refreshing token")

			// Attempt proactive refresh (don't fail request if this fails)
			if m.attemptTokenRefresh(c, refreshToken) {
				// Token was refreshed, get new claims
				newAccessToken, _ := m.authService.GetTokenFromCookies(c.Request)
				if newAccessToken != "" {
					if newClaims, err := m.authService.ValidateToken(newAccessToken); err == nil {
						claims = newClaims
					}
				}
			}
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("csrf_token", claims.CsrfToken)

		m.logger.Debug().
			Int64("user_id", claims.UserID).
			Str("username", claims.Username).
			Str("role", string(claims.Role)).
			Str("path", c.Request.URL.Path).
			Msg("User authenticated successfully")

		c.Next()
	}
}

// attemptTokenRefresh tries to refresh the token and set new cookies
func (m *AuthMiddleware) attemptTokenRefresh(c *gin.Context, refreshToken string) bool {
	ctx := c.Request.Context()

	// Attempt to refresh the token
	newTokens, err := m.authService.RefreshToken(ctx, refreshToken)
	if err != nil {
		m.logger.Debug().
			Err(err).
			Str("path", c.Request.URL.Path).
			Msg("Failed to refresh token")
		return false
	}

	// Set new cookies
	m.authService.SetAuthCookies(c.Writer, newTokens)

	// Set CSRF cookie if enabled
	if newTokens.CsrfToken != "" {
		m.authService.SetCsrfCookie(c.Writer, newTokens.CsrfToken)
	}

	// Validate the new access token and set context
	claims, err := m.authService.ValidateToken(newTokens.AccessToken)
	if err != nil {
		m.logger.Error().
			Err(err).
			Msg("Failed to validate newly refreshed token")
		return false
	}

	// Set user information in context
	c.Set("user_id", claims.UserID)
	c.Set("username", claims.Username)
	c.Set("role", claims.Role)
	c.Set("csrf_token", claims.CsrfToken)

	// Add headers to inform the client that tokens were refreshed
	c.Header("X-Token-Refreshed", "true")
	c.Header("X-New-Expires-In", string(rune(newTokens.ExpiresIn)))
	if newTokens.CsrfToken != "" {
		c.Header("X-CSRF-Token", newTokens.CsrfToken)
	}

	m.logger.Info().
		Int64("user_id", claims.UserID).
		Str("path", c.Request.URL.Path).
		Int64("expires_in", newTokens.ExpiresIn).
		Msg("Token refreshed automatically")

	return true
}

// RequireRole ensures the user has the required role
func (m *AuthMiddleware) RequireRole(role string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("role")
		if !exists {
			m.logger.Warn().
				Str("path", c.Request.URL.Path).
				Str("ip", c.ClientIP()).
				Msg("Authentication required for role check")

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Authentication required",
			})
			return
		}

		// Convert to string for comparison
		userRoleStr := string(userRole.(models.Role))
		if userRoleStr != role {
			userID, _ := c.Get("user_id")
			m.logger.Warn().
				Interface("user_id", userID).
				Str("required_role", role).
				Str("user_role", userRoleStr).
				Str("path", c.Request.URL.Path).
				Msg("Insufficient permissions")

			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":   "insufficient_permissions",
				"message": "You don't have permission to access this resource",
			})
			return
		}

		c.Next()
	}
}

// RequireAnyRole ensures the user has one of the specified roles
func (m *AuthMiddleware) RequireAnyRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("role")
		if !exists {
			m.logger.Warn().
				Str("path", c.Request.URL.Path).
				Str("ip", c.ClientIP()).
				Msg("Authentication required for role check")

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"error":   "authentication_required",
				"message": "Authentication required",
			})
			return
		}

		// Convert to string for comparison
		userRoleStr := string(userRole.(models.Role))

		// Check if user has any of the required roles
		hasRole := false
		for _, role := range roles {
			if userRoleStr == role {
				hasRole = true
				break
			}
		}

		if !hasRole {
			userID, _ := c.Get("user_id")
			m.logger.Warn().
				Interface("user_id", userID).
				Strs("required_roles", roles).
				Str("user_role", userRoleStr).
				Str("path", c.Request.URL.Path).
				Msg("Insufficient permissions")

			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"error":   "insufficient_permissions",
				"message": "You don't have permission to access this resource",
			})
			return
		}

		c.Next()
	}
}

// OptionalAuth middleware that doesn't fail if user is not authenticated
// Useful for endpoints that work with or without authentication
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		accessToken, refreshToken := m.authService.GetTokenFromCookies(c.Request)

		if accessToken == "" {
			authHeader := c.GetHeader("Authorization")
			if len(authHeader) > 7 && strings.HasPrefix(authHeader, "Bearer ") {
				accessToken = authHeader[7:]
			}
		}

		if accessToken != "" {
			claims, err := m.authService.ValidateToken(accessToken)
			if err == nil {
				// Set user information in context
				c.Set("user_id", claims.UserID)
				c.Set("username", claims.Username)
				c.Set("role", claims.Role)
				c.Set("csrf_token", claims.CsrfToken)
				c.Set("authenticated", true)

				m.logger.Debug().
					Int64("user_id", claims.UserID).
					Str("username", claims.Username).
					Str("path", c.Request.URL.Path).
					Msg("Optional auth: User authenticated")
			} else if strings.Contains(err.Error(), "token is expired") && refreshToken != "" {
				// Try to refresh expired token
				if m.attemptTokenRefresh(c, refreshToken) {
					c.Set("authenticated", true)
					m.logger.Debug().
						Str("path", c.Request.URL.Path).
						Msg("Optional auth: Token refreshed")
				} else {
					c.Set("authenticated", false)
				}
			} else {
				c.Set("authenticated", false)
				m.logger.Debug().
					Err(err).
					Str("path", c.Request.URL.Path).
					Msg("Optional auth: Invalid token")
			}
		} else {
			c.Set("authenticated", false)
			m.logger.Debug().
				Str("path", c.Request.URL.Path).
				Msg("Optional auth: No token provided")
		}

		// Continue regardless of authentication status
		c.Next()
	}
}

// AdminOnly is a convenience method for admin-only routes
func (m *AuthMiddleware) AdminOnly() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		m.Authenticate()(c)
		if c.IsAborted() {
			return
		}
		m.RequireRole("admin")(c)
	})
}

// UserOrAdmin allows both users and admins
func (m *AuthMiddleware) UserOrAdmin() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		m.Authenticate()(c)
		if c.IsAborted() {
			return
		}
		m.RequireAnyRole("user", "admin")(c)
	})
}
