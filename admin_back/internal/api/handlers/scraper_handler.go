package handlers

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/scraper/models"
	"github.com/psynarios/admin_back/internal/scraper/service"
)

// ScraperHandler handles scraper-related HTTP requests
type Scraper<PERSON>andler struct {
	service service.ScraperService
	logger  zerolog.Logger
}

// NewScraperHandler creates a new scraper handler
func NewScraperHandler(service service.ScraperService, logger zerolog.Logger) *ScraperHandler {
	return &ScraperHandler{
		service: service,
		logger:  logger.With().Str("component", "scraper_handler").Logger(),
	}
}

// ProcessUsers handles the request to process a user file (keeping for backward compatibility)
func (h *ScraperHandler) ProcessUsers(c *gin.Context) {
	// Get the file from the request
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No file uploaded",
		})
		return
	}

	// Check file extension
	ext := filepath.Ext(file.Filename)
	if ext != ".xlsx" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Only XLSX files are supported",
		})
		return
	}

	// Create a temporary directory to store the file
	tempDir := os.TempDir()
	tempFilePath := filepath.Join(tempDir, fmt.Sprintf("users_%d%s", time.Now().UnixNano(), ext))

	// Save the file to the temporary directory
	if err := c.SaveUploadedFile(file, tempFilePath); err != nil {
		h.logger.Error().Err(err).Msg("Failed to save uploaded file")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to save uploaded file",
		})
		return
	}

	// Process the file
	result, err := h.service.ProcessUserFile(c.Request.Context(), tempFilePath)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to process user file")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to process file: %v", err),
		})
		return
	}

	// Clean up the temporary file
	os.Remove(tempFilePath)

	// Return the result
	c.JSON(http.StatusOK, result)
}

// ProcessUsersJSON handles the request to process users from JSON data
func (h *ScraperHandler) ProcessUsersJSON(c *gin.Context) {
	// Parse the request body
	var request models.ProcessUsersRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	// Validate the request
	if len(request.Users) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No users provided",
		})
		return
	}

	// Check if detailed results are requested
	detailed := c.Query("detailed") == "true"

	var result interface{}
	var err error

	if detailed {
		// Process the users with detailed results
		result, err = h.service.ProcessUsersWithDetails(c.Request.Context(), request.Users)
	} else {
		// Process the users with summary results
		result, err = h.service.ProcessUsers(c.Request.Context(), request.Users)
	}

	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to process users")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to process users: %v", err),
		})
		return
	}

	// Return the result
	c.JSON(http.StatusOK, result)
}

// SelectTemplatesForUsers handles the request to select templates for users
func (h *ScraperHandler) SelectTemplatesForUsers(c *gin.Context) {
	var request struct {
		Users []models.UserData `json:"users" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	h.logger.Info().Int("userCount", len(request.Users)).Msg("Received template selection request")

	// Process users and select templates
	result, err := h.service.SelectTemplatesForUsers(c.Request.Context(), request.Users)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to select templates for users")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process users"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// SendTemplateEmails handles sending emails with selected templates
func (h *ScraperHandler) SendTemplateEmails(c *gin.Context) {
	// Set a longer timeout for the context
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Minute)
	c.Request = c.Request.WithContext(ctx)
	defer cancel()

	var request struct {
		Users []struct {
			Email      string                 `json:"email" binding:"required,email"`
			TemplateID string                 `json:"template_id" binding:"required"`
			Data       map[string]interface{} `json:"data" binding:"required"`
			CompanyID  string                 `json:"company_id" binding:"required"`
		} `json:"users" binding:"required"`
		SendToAll bool `json:"send_to_all"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	h.logger.Info().
		Int("userCount", len(request.Users)).
		Bool("sendToAll", request.SendToAll).
		Msg("Received email sending request")

	// Validate company IDs and check for mail configs before sending
	for _, user := range request.Users {
		if user.CompanyID == "" {
			h.logger.Error().Str("email", user.Email).Msg("Missing company ID")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Company ID is required for all users"})
			return
		}
	}

	// Increase worker count for better performance
	workerCount := 10
	if len(request.Users) < workerCount {
		workerCount = len(request.Users)
	}

	// Process each user in parallel with a worker pool
	type result struct {
		Email string
		Error error
	}

	jobs := make(chan int, len(request.Users))
	results := make(chan result, len(request.Users))

	// Start workers
	for w := 1; w <= workerCount; w++ {
		go func(id int) {
			for j := range jobs {
				user := request.Users[j]
				h.logger.Debug().
					Int("worker", id).
					Str("email", user.Email).
					Str("companyID", user.CompanyID).
					Msg("Sending email")

				err := h.service.SendTemplateEmail(
					c.Request.Context(),
					[]string{user.Email},
					[]string{},
					[]string{},
					user.TemplateID,
					user.Data,
					user.CompanyID,
				)

				results <- result{
					Email: user.Email,
					Error: err,
				}
			}
		}(w)
	}

	// Send jobs to workers
	for i := range request.Users {
		jobs <- i
	}
	close(jobs)

	// Collect results
	successCount := 0
	errorCount := 0
	errors := make(map[string]string)

	for i := 0; i < len(request.Users); i++ {
		r := <-results
		if r.Error != nil {
			errorCount++
			errors[r.Email] = r.Error.Error()
			h.logger.Error().
				Err(r.Error).
				Str("email", r.Email).
				Msg("Failed to send email")
		} else {
			successCount++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success_count": successCount,
		"error_count":   errorCount,
		"errors":        errors,
	})
}
