package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/library/behaviour/models"
	"github.com/psynarios/admin_back/internal/library/behaviour/service"
)

type BehaviourHandler struct {
	service service.BehaviourService
	logger  zerolog.Logger
}

func NewBehaviourHandler(service service.BehaviourService, logger zerolog.Logger) *BehaviourHandler {
	return &BehaviourHandler{
		service: service,
		logger:  logger.With().Str("component", "behaviour_handler").Logger(),
	}
}

// CreateBehaviour creates a new behaviour
func (h *BehaviourHandler) CreateBehaviour(c *gin.Context) {
	var req models.BehaviourCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	behaviour, err := h.service.CreateBehaviour(c.Request.Context(), &req, &userUUID)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to create behaviour")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create behaviour", "details": err.Error()})
		return
	}

	h.logger.Info().Str("behaviour_id", behaviour.ID.String()).Msg("Behaviour created successfully")
	c.JSON(http.StatusCreated, behaviour)
}

// GetBehaviour gets a behaviour by ID
func (h *BehaviourHandler) GetBehaviour(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid behaviour ID"})
		return
	}

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	behaviour, err := h.service.GetBehaviour(c.Request.Context(), id, languageID)
	if err != nil {
		h.logger.Error().Err(err).Str("behaviour_id", id.String()).Msg("Failed to get behaviour")
		c.JSON(http.StatusNotFound, gin.H{"error": "Behaviour not found"})
		return
	}

	c.JSON(http.StatusOK, behaviour)
}

// GetBehaviours gets all behaviours with pagination
func (h *BehaviourHandler) GetBehaviours(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))
	activeOnly := c.DefaultQuery("active", "true") == "true"

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	behaviours, err := h.service.GetBehaviours(c.Request.Context(), limit, offset, languageID, activeOnly)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get behaviours")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get behaviours"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"behaviours": behaviours,
		"pagination": gin.H{
			"limit":  limit,
			"offset": offset,
			"count":  len(behaviours),
		},
	})
}

// UpdateBehaviour updates a behaviour
func (h *BehaviourHandler) UpdateBehaviour(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid behaviour ID"})
		return
	}

	var req models.BehaviourUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	behaviour, err := h.service.UpdateBehaviour(c.Request.Context(), id, &req)
	if err != nil {
		h.logger.Error().Err(err).Str("behaviour_id", id.String()).Msg("Failed to update behaviour")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update behaviour", "details": err.Error()})
		return
	}

	h.logger.Info().Str("behaviour_id", id.String()).Msg("Behaviour updated successfully")
	c.JSON(http.StatusOK, behaviour)
}

// DeleteBehaviour deletes a behaviour
func (h *BehaviourHandler) DeleteBehaviour(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid behaviour ID"})
		return
	}

	err = h.service.DeleteBehaviour(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("behaviour_id", id.String()).Msg("Failed to delete behaviour")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete behaviour"})
		return
	}

	h.logger.Info().Str("behaviour_id", id.String()).Msg("Behaviour deleted successfully")
	c.JSON(http.StatusNoContent, nil)
}

// SearchBehaviours searches behaviours
func (h *BehaviourHandler) SearchBehaviours(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	behaviours, err := h.service.SearchBehaviours(c.Request.Context(), query, languageID, limit, offset)
	if err != nil {
		h.logger.Error().Err(err).Str("query", query).Msg("Failed to search behaviours")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search behaviours"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"behaviours": behaviours,
		"query":      query,
		"pagination": gin.H{
			"limit":  limit,
			"offset": offset,
			"count":  len(behaviours),
		},
	})
}
