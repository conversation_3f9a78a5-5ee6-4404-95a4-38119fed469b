package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/library/enum"
	"github.com/psynarios/admin_back/internal/library/scenario/models"
	"github.com/psynarios/admin_back/internal/library/scenario/service"
)

type ScenarioHandler struct {
	service *service.ScenarioService
	logger  zerolog.Logger
}

func NewScenarioHandler(service *service.ScenarioService, logger zerolog.Logger) *ScenarioHandler {
	return &ScenarioHandler{
		service: service,
		logger:  logger.With().Str("component", "scenario_handler").Logger(),
	}
}

// CreateScenario creates a new scenario
func (h *ScenarioHandler) CreateScenario(c *gin.Context) {
	var req models.CreateScenarioRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Get user ID from context (from auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	// Set created_by from authenticated user
	req.CreatedBy = userUUID

	scenario, err := h.service.CreateScenario(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to create scenario")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create scenario", "details": err.Error()})
		return
	}

	h.logger.Info().Str("scenario_id", scenario.ID.String()).Msg("Scenario created successfully")
	c.JSON(http.StatusCreated, models.ScenarioResponse{Scenario: scenario})
}

// GetScenario gets a scenario by ID
func (h *ScenarioHandler) GetScenario(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scenario ID"})
		return
	}

	// Check if language filter is provided
	languageIDStr := c.Query("language")
	if languageIDStr != "" {
		languageID, err := strconv.Atoi(languageIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}

		scenario, err := h.service.GetScenarioByLanguage(c.Request.Context(), id, languageID)
		if err != nil {
			h.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Failed to get scenario by language")
			c.JSON(http.StatusNotFound, gin.H{"error": "Scenario not found"})
			return
		}

		c.JSON(http.StatusOK, models.ScenarioResponse{Scenario: scenario})
		return
	}

	// Get scenario with all contents
	scenario, err := h.service.GetScenarioByID(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Failed to get scenario")
		c.JSON(http.StatusNotFound, gin.H{"error": "Scenario not found"})
		return
	}

	c.JSON(http.StatusOK, models.ScenarioResponse{Scenario: scenario})
}

// GetScenarioByCode gets a scenario by code
func (h *ScenarioHandler) GetScenarioByCode(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Scenario code is required"})
		return
	}

	scenario, err := h.service.GetScenarioByCode(c.Request.Context(), code)
	if err != nil {
		h.logger.Error().Err(err).Str("code", code).Msg("Failed to get scenario by code")
		c.JSON(http.StatusNotFound, gin.H{"error": "Scenario not found"})
		return
	}

	c.JSON(http.StatusOK, models.ScenarioResponse{Scenario: scenario})
}

// GetScenarios gets all scenarios with pagination
func (h *ScenarioHandler) GetScenarios(c *gin.Context) {
	// Parse pagination parameters
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	// Validate pagination limits
	if limit < 1 || limit > 100 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}

	// Parse language filter
	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	// Create list request
	req := &models.ScenarioListRequest{
		LanguageID: languageID,
		Limit:      limit,
		Offset:     offset,
	}

	response, err := h.service.ListScenarios(c.Request.Context(), req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get scenarios")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get scenarios"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// UpdateScenario updates a scenario
func (h *ScenarioHandler) UpdateScenario(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scenario ID"})
		return
	}

	var req models.UpdateScenarioRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	scenario, err := h.service.UpdateScenario(c.Request.Context(), id, &req)
	if err != nil {
		h.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Failed to update scenario")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update scenario", "details": err.Error()})
		return
	}

	h.logger.Info().Str("scenario_id", id.String()).Msg("Scenario updated successfully")
	c.JSON(http.StatusOK, models.ScenarioResponse{Scenario: scenario})
}

// DeleteScenario deletes a scenario
func (h *ScenarioHandler) DeleteScenario(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scenario ID"})
		return
	}

	err = h.service.DeleteScenario(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Failed to delete scenario")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete scenario"})
		return
	}

	h.logger.Info().Str("scenario_id", id.String()).Msg("Scenario deleted successfully")
	c.JSON(http.StatusNoContent, nil)
}

// GetScenariosByDifficulty gets scenarios by difficulty level
func (h *ScenarioHandler) GetScenariosByDifficulty(c *gin.Context) {
	difficultyStr := c.Param("difficulty")
	if difficultyStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Difficulty level is required"})
		return
	}

	// Validate and convert difficulty
	var difficulty enum.DifficultyLevel
	switch difficultyStr {
	case "Débutant":
		difficulty = enum.DifficultyDebutant
	case "Confirmé":
		difficulty = enum.DifficultyConfirme
	case "Expérimenté":
		difficulty = enum.DifficultyExperimente
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error":              "Invalid difficulty level",
			"valid_difficulties": []string{"Débutant", "Confirmé", "Expérimenté"},
		})
		return
	}

	// Parse pagination
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	// Validate pagination
	if limit < 1 || limit > 100 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}

	// Parse language filter
	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	response, err := h.service.GetScenariosByDifficulty(c.Request.Context(), difficulty, languageID, limit, offset)
	if err != nil {
		h.logger.Error().Err(err).Str("difficulty", difficultyStr).Msg("Failed to get scenarios by difficulty")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get scenarios by difficulty"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetScenariosByType gets scenarios by type
func (h *ScenarioHandler) GetScenariosByType(c *gin.Context) {
	typeStr := c.Param("type")
	if typeStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Scenario type is required"})
		return
	}

	// Validate and convert scenario type
	var scenarioType enum.ScenarioType
	switch typeStr {
	case "Situations Interpersonnelles":
		scenarioType = enum.ScenarioTypeInterpersonal
	case "Dialogue Intérieur":
		scenarioType = enum.ScenarioTypeDialogue
	case "Nuances Perceptives":
		scenarioType = enum.ScenarioTypePerceptual
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error":       "Invalid scenario type",
			"valid_types": []string{"Situations Interpersonnelles", "Dialogue Intérieur", "Nuances Perceptives"},
		})
		return
	}

	// Parse pagination
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	// Validate pagination
	if limit < 1 || limit > 100 {
		limit = 20
	}
	if offset < 0 {
		offset = 0
	}

	// Parse language filter
	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	response, err := h.service.GetScenariosByType(c.Request.Context(), scenarioType, languageID, limit, offset)
	if err != nil {
		h.logger.Error().Err(err).Str("type", typeStr).Msg("Failed to get scenarios by type")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get scenarios by type"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetScenarioStatistics gets scenario statistics
func (h *ScenarioHandler) GetScenarioStatistics(c *gin.Context) {
	stats, err := h.service.GetScenarioStats(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get scenario statistics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get scenario statistics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"statistics": stats,
	})
}

// SearchScenarios searches scenarios (placeholder for future implementation)
func (h *ScenarioHandler) SearchScenarios(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	// For now, return empty results since search is not implemented in service
	// You would need to implement SearchScenarios in the service
	c.JSON(http.StatusOK, gin.H{
		"message": "Search functionality not yet implemented",
		"query":   query,
		"results": []models.Scenario{},
	})
}
