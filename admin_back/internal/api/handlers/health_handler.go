// internal/api/handlers/health_handler.go
package handlers

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/psynarios/admin_back/internal/media/storage"
	"github.com/rs/zerolog"
)

type HealthHandler struct {
	db              *pgxpool.Pool
	storageProvider storage.StorageProvider
	logger          zerolog.Logger
}

type HealthResponse struct {
	Status    string                 `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Services  map[string]interface{} `json:"services"`
	Version   string                 `json:"version,omitempty"`
}

func NewHealthHandler(db *pgxpool.Pool, storageProvider storage.StorageProvider, logger zerolog.Logger) *HealthHandler {
	return &HealthHandler{
		db:              db,
		storageProvider: storageProvider,
		logger:          logger.With().Str("component", "health_handler").<PERSON><PERSON>(),
	}
}

// CheckHealth handles both GET and HEAD requests for health checks
func (h *HealthHandler) CheckHealth(c *gin.Context) {
	h.logger.Debug().
		Str("method", c.Request.Method).
		Str("user_agent", c.GetHeader("User-Agent")).
		Msg("Health check requested")

	services := make(map[string]interface{})
	overallStatus := "healthy"

	// Check database
	dbStatus := h.checkDatabase()
	services["database"] = dbStatus
	if dbStatus["status"] != "healthy" {
		overallStatus = "unhealthy"
	}

	// Check storage
	storageStatus := h.checkStorage()
	services["storage"] = storageStatus
	if storageStatus["status"] != "healthy" && overallStatus == "healthy" {
		overallStatus = "degraded"
	}

	response := HealthResponse{
		Status:    overallStatus,
		Timestamp: time.Now(),
		Services:  services,
		Version:   "1.0.0",
	}

	// Set appropriate status code
	statusCode := http.StatusOK
	if overallStatus == "unhealthy" {
		statusCode = http.StatusServiceUnavailable
	}

	// For HEAD requests, just return the status code with headers
	if c.Request.Method == "HEAD" {
		c.Header("Content-Type", "application/json")
		c.Status(statusCode)
		return
	}

	// For GET requests, return the full response
	c.JSON(statusCode, response)
}

func (h *HealthHandler) checkDatabase() map[string]interface{} {
	start := time.Now()

	if h.db == nil {
		return map[string]interface{}{
			"status":   "unhealthy",
			"error":    "database not initialized",
			"duration": time.Since(start).String(),
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := h.db.Ping(ctx)
	duration := time.Since(start)

	if err != nil {
		h.logger.Error().Err(err).Msg("Database health check failed")
		return map[string]interface{}{
			"status":   "unhealthy",
			"error":    err.Error(),
			"duration": duration.String(),
		}
	}

	// Get some basic database stats
	stats := h.db.Stat()
	return map[string]interface{}{
		"status":         "healthy",
		"duration":       duration.String(),
		"total_conns":    stats.TotalConns(),
		"acquired_conns": stats.AcquiredConns(),
		"idle_conns":     stats.IdleConns(),
	}
}

func (h *HealthHandler) checkStorage() map[string]interface{} {
	start := time.Now()

	if h.storageProvider == nil {
		return map[string]interface{}{
			"status":   "unhealthy",
			"error":    "storage not initialized",
			"duration": time.Since(start).String(),
		}
	}

	// Try to check if storage is accessible
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	testPath := "health-check-test.tmp"
	_, err := h.storageProvider.Exists(ctx, testPath)
	duration := time.Since(start)

	if err != nil {
		h.logger.Warn().Err(err).Msg("Storage health check warning")
		return map[string]interface{}{
			"status":   "degraded",
			"warning":  "storage access issues",
			"error":    err.Error(),
			"duration": duration.String(),
		}
	}

	return map[string]interface{}{
		"status":   "healthy",
		"duration": duration.String(),
	}
}
