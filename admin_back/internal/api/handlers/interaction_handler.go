package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/library/interaction/models"
	"github.com/psynarios/admin_back/internal/library/interaction/service"
)

type InteractionHandler struct {
	service service.InteractionService
	logger  zerolog.Logger
}

func NewInteractionHandler(service service.InteractionService, logger zerolog.Logger) *InteractionHandler {
	return &InteractionHandler{
		service: service,
		logger:  logger.With().Str("component", "interaction_handler").<PERSON>gger(),
	}
}

// CreateInteraction creates a new interaction
func (h *InteractionHandler) CreateInteraction(c *gin.Context) {
	var req models.InteractionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	interaction, err := h.service.CreateInteraction(c.Request.Context(), &req, &userUUID)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to create interaction")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create interaction", "details": err.Error()})
		return
	}

	h.logger.Info().Str("interaction_id", interaction.ID.String()).Msg("Interaction created successfully")
	c.JSON(http.StatusCreated, interaction)
}

// GetInteraction gets an interaction by ID
func (h *InteractionHandler) GetInteraction(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interaction ID"})
		return
	}

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	interaction, err := h.service.GetInteraction(c.Request.Context(), id, languageID)
	if err != nil {
		h.logger.Error().Err(err).Str("interaction_id", id.String()).Msg("Failed to get interaction")
		c.JSON(http.StatusNotFound, gin.H{"error": "Interaction not found"})
		return
	}

	c.JSON(http.StatusOK, interaction)
}

// GetInteractions gets all interactions with pagination
func (h *InteractionHandler) GetInteractions(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))
	activeOnly := c.DefaultQuery("active", "true") == "true"

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	interactions, err := h.service.GetInteractions(c.Request.Context(), limit, offset, languageID, activeOnly)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get interactions")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get interactions"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"interactions": interactions,
		"pagination": gin.H{
			"limit":  limit,
			"offset": offset,
			"count":  len(interactions),
		},
	})
}

// UpdateInteraction updates an interaction
func (h *InteractionHandler) UpdateInteraction(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interaction ID"})
		return
	}

	var req models.InteractionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	interaction, err := h.service.UpdateInteraction(c.Request.Context(), id, &req)
	if err != nil {
		h.logger.Error().Err(err).Str("interaction_id", id.String()).Msg("Failed to update interaction")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update interaction", "details": err.Error()})
		return
	}

	h.logger.Info().Str("interaction_id", id.String()).Msg("Interaction updated successfully")
	c.JSON(http.StatusOK, interaction)
}

// DeleteInteraction deletes an interaction
func (h *InteractionHandler) DeleteInteraction(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interaction ID"})
		return
	}

	err = h.service.DeleteInteraction(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("interaction_id", id.String()).Msg("Failed to delete interaction")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete interaction"})
		return
	}

	h.logger.Info().Str("interaction_id", id.String()).Msg("Interaction deleted successfully")
	c.JSON(http.StatusNoContent, nil)
}

// GetInteractionsByScenario gets interactions by scenario ID
func (h *InteractionHandler) GetInteractionsByScenario(c *gin.Context) {
	scenarioIDStr := c.Param("scenario_id")
	scenarioID, err := uuid.Parse(scenarioIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scenario ID"})
		return
	}

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	interactions, err := h.service.GetInteractionsByScenario(c.Request.Context(), scenarioID, languageID)
	if err != nil {
		h.logger.Error().Err(err).Str("scenario_id", scenarioID.String()).Msg("Failed to get interactions by scenario")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get interactions by scenario"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"interactions": interactions})
}

// GetInteractionsByCharacter gets interactions by character ID
func (h *InteractionHandler) GetInteractionsByCharacter(c *gin.Context) {
	characterIDStr := c.Param("character_id")
	characterID, err := uuid.Parse(characterIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid character ID"})
		return
	}

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	interactions, err := h.service.GetInteractionsByCharacter(c.Request.Context(), characterID, languageID)
	if err != nil {
		h.logger.Error().Err(err).Str("character_id", characterID.String()).Msg("Failed to get interactions by character")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get interactions by character"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"interactions": interactions})
}

// AddBehaviourToInteraction adds a behaviour to an interaction
func (h *InteractionHandler) AddBehaviourToInteraction(c *gin.Context) {
	interactionIDStr := c.Param("id")
	interactionID, err := uuid.Parse(interactionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interaction ID"})
		return
	}

	behaviourIDStr := c.Param("behaviour_id")
	behaviourID, err := uuid.Parse(behaviourIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid behaviour ID"})
		return
	}

	err = h.service.AddBehaviourToInteraction(c.Request.Context(), interactionID, behaviourID)
	if err != nil {
		h.logger.Error().Err(err).
			Str("interaction_id", interactionID.String()).
			Str("behaviour_id", behaviourID.String()).
			Msg("Failed to add behaviour to interaction")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add behaviour to interaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Behaviour added successfully"})
}

// RemoveBehaviourFromInteraction removes a behaviour from an interaction
func (h *InteractionHandler) RemoveBehaviourFromInteraction(c *gin.Context) {
	interactionIDStr := c.Param("id")
	interactionID, err := uuid.Parse(interactionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interaction ID"})
		return
	}

	behaviourIDStr := c.Param("behaviour_id")
	behaviourID, err := uuid.Parse(behaviourIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid behaviour ID"})
		return
	}

	err = h.service.RemoveBehaviourFromInteraction(c.Request.Context(), interactionID, behaviourID)
	if err != nil {
		h.logger.Error().Err(err).
			Str("interaction_id", interactionID.String()).
			Str("behaviour_id", behaviourID.String()).
			Msg("Failed to remove behaviour from interaction")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove behaviour from interaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Behaviour removed successfully"})
}
