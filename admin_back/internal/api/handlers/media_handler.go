// internal/api/handlers/media_handler.go
package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/psynarios/admin_back/internal/media/models"
	"github.com/psynarios/admin_back/internal/media/service"
	"github.com/psynarios/admin_back/pkg/validator"
)

type MediaHandler struct {
	mediaService *service.MediaService
	validator    *validator.CustomValidator
}

func NewMediaHandler(mediaService *service.MediaService, validator *validator.CustomValidator) *MediaHandler {
	return &MediaHandler{
		mediaService: mediaService,
		validator:    validator,
	}
}

// UploadMedia handles media file uploads
func (h *MediaHandler) UploadMedia(c *gin.Context) {
	// Get file from form
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file provided"})
		return
	}
	defer file.Close()

	// Get form data
	mediaTypeStr := c.PostForm("media_type")
	languageIDStr := c.PostForm("language_id")

	// Validate media type using validator
	if err := h.validator.ValidateMediaType(mediaTypeStr); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	mediaType := models.MediaType(mediaTypeStr)

	// Parse language ID if provided
	var languageID *int
	if languageIDStr != "" {
		langID, err := strconv.Atoi(languageIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language_id"})
			return
		}
		languageID = &langID
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	createdBy, ok := userID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	// Parse metadata if provided
	var metadata map[string]interface{}
	if metadataStr := c.PostForm("metadata"); metadataStr != "" {
		if err := json.Unmarshal([]byte(metadataStr), &metadata); err != nil {
			metadata = map[string]interface{}{
				"uploaded_via": "api",
				"raw_metadata": metadataStr,
			}
		}
	}

	// Validate file using validator
	if err := h.validator.ValidateFileUpload(header, mediaType); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create upload request
	uploadReq := &service.UploadRequest{
		File:       file,
		Header:     header,
		LanguageID: languageID,
		MediaType:  mediaType,
		CreatedBy:  createdBy,
		Metadata:   metadata,
	}

	// Upload media
	response, err := h.mediaService.UploadMedia(c.Request.Context(), uploadReq)
	if err != nil {
		if strings.Contains(err.Error(), "invalid language ID") ||
			strings.Contains(err.Error(), "invalid file") ||
			strings.Contains(err.Error(), "file size") {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upload media"})
		}
		return
	}

	c.JSON(http.StatusCreated, response)
}

// GetMediaByID retrieves a media file by ID
func (h *MediaHandler) GetMediaByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid media ID"})
		return
	}

	media, err := h.mediaService.GetMediaByID(c.Request.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Media not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get media"})
		}
		return
	}

	// Add public URL
	publicURL := h.mediaService.GetPublicURL(media.FilePath)

	c.JSON(http.StatusOK, gin.H{
		"media": media,
		"url":   publicURL,
	})
}

// ListMedia retrieves media files with filtering and pagination
func (h *MediaHandler) ListMedia(c *gin.Context) {
	// Parse query parameters
	var req service.MediaListRequest

	// Media type filter
	if mediaTypeStr := c.Query("media_type"); mediaTypeStr != "" {
		mediaType := models.MediaType(mediaTypeStr)
		if mediaType.IsValid() {
			req.MediaType = &mediaType
		}
	}

	// Language filter
	if languageIDStr := c.Query("language_id"); languageIDStr != "" {
		if languageID, err := strconv.Atoi(languageIDStr); err == nil {
			req.LanguageID = &languageID
		}
	}

	// Creator filter - only allow if user is admin or requesting their own
	if createdByStr := c.Query("created_by"); createdByStr != "" {
		// Parse as int64 instead of UUID
		if createdBy, err := strconv.ParseInt(createdByStr, 10, 64); err == nil {
			// Check if user is admin or requesting their own media
			userID, _ := c.Get("user_id")
			userRole, _ := c.Get("user_role")

			if userRole == "admin" || userID == createdBy {
				req.CreatedBy = &createdBy
			}
		}
	}

	// Search filter
	req.Search = c.Query("search")

	// Pagination with validation
	req.Limit, _ = strconv.Atoi(c.DefaultQuery("limit", "20"))
	req.Offset, _ = strconv.Atoi(c.DefaultQuery("offset", "0"))

	if err := h.validator.ValidatePagination(req.Limit, req.Offset); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.mediaService.ListMedia(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list media"})
		return
	}

	// Add public URLs to all media files
	for i := range response.MediaFiles {
		response.MediaFiles[i].FilePath = h.mediaService.GetPublicURL(response.MediaFiles[i].FilePath)
	}

	c.JSON(http.StatusOK, response)
}

// UpdateMedia updates media file metadata
func (h *MediaHandler) UpdateMedia(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid media ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Validate updates using validator
	if err := h.validator.ValidateMediaUpdates(updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updatedMedia, err := h.mediaService.UpdateMedia(c.Request.Context(), id, updates)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Media not found"})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		}
		return
	}

	// Add public URL
	publicURL := h.mediaService.GetPublicURL(updatedMedia.FilePath)

	c.JSON(http.StatusOK, gin.H{
		"media": updatedMedia,
		"url":   publicURL,
	})
}

// DeleteMedia deletes a media file
func (h *MediaHandler) DeleteMedia(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid media ID"})
		return
	}

	err = h.mediaService.DeleteMedia(c.Request.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Media not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete media"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Media deleted successfully"})
}

// GetMediaStats returns media statistics
func (h *MediaHandler) GetMediaStats(c *gin.Context) {
	stats, err := h.mediaService.GetMediaStats(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get media stats"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}

// CopyMedia creates a copy of an existing media file
func (h *MediaHandler) CopyMedia(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid media ID"})
		return
	}

	var req struct {
		LanguageID *int `json:"language_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	createdBy, ok := userID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	copiedMedia, err := h.mediaService.CopyMedia(c.Request.Context(), id, req.LanguageID, createdBy)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Media not found"})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		}
		return
	}

	// Add public URL
	publicURL := h.mediaService.GetPublicURL(copiedMedia.FilePath)

	c.JSON(http.StatusCreated, gin.H{
		"media": copiedMedia,
		"url":   publicURL,
	})
}

// GetMediaInfo retrieves basic media info without sensitive data
func (h *MediaHandler) GetMediaInfo(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid media ID"})
		return
	}

	media, err := h.mediaService.GetMediaByID(c.Request.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Media not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get media"})
		}
		return
	}

	// Return limited info for public access
	publicInfo := gin.H{
		"id":                media.ID,
		"original_filename": media.OriginalFilename,
		"media_type":        media.MediaType,
		"file_size":         media.FileSize,
		"duration":          media.Duration,
		"dimensions":        media.Dimensions,
		"created_at":        media.CreatedAt,
	}

	c.JSON(http.StatusOK, gin.H{"media": publicInfo})
}

// ListPublicMedia lists only public/published media
func (h *MediaHandler) ListPublicMedia(c *gin.Context) {
	// Parse query parameters for public listing
	var req service.MediaListRequest

	// Only allow basic filters for public access
	if mediaTypeStr := c.Query("media_type"); mediaTypeStr != "" {
		mediaType := models.MediaType(mediaTypeStr)
		if mediaType.IsValid() {
			req.MediaType = &mediaType
		}
	}

	req.Limit, _ = strconv.Atoi(c.DefaultQuery("limit", "20"))
	req.Offset, _ = strconv.Atoi(c.DefaultQuery("offset", "0"))

	if err := h.validator.ValidatePagination(req.Limit, req.Offset); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// TODO: Add a service method for public media only
	response, err := h.mediaService.ListMedia(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list media"})
		return
	}

	// Return limited info for public access
	publicMedia := make([]gin.H, len(response.MediaFiles))
	for i, media := range response.MediaFiles {
		publicMedia[i] = gin.H{
			"id":                media.ID,
			"original_filename": media.OriginalFilename,
			"media_type":        media.MediaType,
			"file_size":         media.FileSize,
			"url":               h.mediaService.GetPublicURL(media.FilePath),
			"created_at":        media.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"media":  publicMedia,
		"total":  response.Total,
		"limit":  response.Limit,
		"offset": response.Offset,
	})
}

// ===================== MISSING PROTECTED METHODS =====================

// SearchMedia provides search functionality
func (h *MediaHandler) SearchMedia(c *gin.Context) {
	searchTerm := c.Query("q")
	if searchTerm == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search term 'q' is required"})
		return
	}

	req := service.MediaListRequest{
		Search: searchTerm,
		Limit:  20,
		Offset: 0,
	}

	// Parse pagination
	if limit, err := strconv.Atoi(c.Query("limit")); err == nil {
		req.Limit = limit
	}
	if offset, err := strconv.Atoi(c.Query("offset")); err == nil {
		req.Offset = offset
	}

	if err := h.validator.ValidatePagination(req.Limit, req.Offset); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.mediaService.ListMedia(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search media"})
		return
	}

	// Add public URLs
	for i := range response.MediaFiles {
		response.MediaFiles[i].FilePath = h.mediaService.GetPublicURL(response.MediaFiles[i].FilePath)
	}

	c.JSON(http.StatusOK, response)
}

// GetMyMedia retrieves current user's media
func (h *MediaHandler) GetMyMedia(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	createdBy, ok := userID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	req := service.MediaListRequest{
		CreatedBy: &createdBy,
		Limit:     20,
		Offset:    0,
	}

	// Parse pagination
	if limit, err := strconv.Atoi(c.Query("limit")); err == nil {
		req.Limit = limit
	}
	if offset, err := strconv.Atoi(c.Query("offset")); err == nil {
		req.Offset = offset
	}

	if err := h.validator.ValidatePagination(req.Limit, req.Offset); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.mediaService.ListMedia(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user media"})
		return
	}

	// Add public URLs
	for i := range response.MediaFiles {
		response.MediaFiles[i].FilePath = h.mediaService.GetPublicURL(response.MediaFiles[i].FilePath)
	}

	c.JSON(http.StatusOK, response)
}

// GetMyMediaStats returns current user's media statistics
func (h *MediaHandler) GetMyMediaStats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	createdBy, ok := userID.(int64)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	// TODO: Add a service method for user-specific stats
	// For now, get all user's media and calculate stats
	req := service.MediaListRequest{
		CreatedBy: &createdBy,
		Limit:     1000, // Get all user's media for stats
		Offset:    0,
	}

	response, err := h.mediaService.ListMedia(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user media stats"})
		return
	}

	// Calculate user stats
	stats := make(map[string]interface{})
	typeStats := make(map[string]int)
	var totalSize int64

	for _, media := range response.MediaFiles {
		typeStats[string(media.MediaType)]++
		totalSize += media.FileSize
	}

	stats["total_count"] = len(response.MediaFiles)
	stats["total_size"] = totalSize
	stats["by_type"] = typeStats

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}

// ===================== MISSING ADMIN METHODS =====================

// ListAllMedia lists all media in the system (admin only)
func (h *MediaHandler) ListAllMedia(c *gin.Context) {
	var req service.MediaListRequest

	// Parse all possible filters for admin
	if mediaTypeStr := c.Query("media_type"); mediaTypeStr != "" {
		mediaType := models.MediaType(mediaTypeStr)
		if mediaType.IsValid() {
			req.MediaType = &mediaType
		}
	}

	if languageIDStr := c.Query("language_id"); languageIDStr != "" {
		if languageID, err := strconv.Atoi(languageIDStr); err == nil {
			req.LanguageID = &languageID
		}
	}

	if createdByStr := c.Query("created_by"); createdByStr != "" {
		if createdBy, err := strconv.ParseInt(createdByStr, 10, 64); err == nil {
			req.CreatedBy = &createdBy
		}
	}

	req.Search = c.Query("search")
	req.Limit, _ = strconv.Atoi(c.DefaultQuery("limit", "50"))
	req.Offset, _ = strconv.Atoi(c.DefaultQuery("offset", "0"))

	if err := h.validator.ValidatePagination(req.Limit, req.Offset); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	response, err := h.mediaService.ListMedia(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list all media"})
		return
	}

	// Add public URLs
	for i := range response.MediaFiles {
		response.MediaFiles[i].FilePath = h.mediaService.GetPublicURL(response.MediaFiles[i].FilePath)
	}

	c.JSON(http.StatusOK, response)
}

// GetGlobalMediaStats returns global media statistics (admin only)
func (h *MediaHandler) GetGlobalMediaStats(c *gin.Context) {
	stats, err := h.mediaService.GetMediaStats(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get global media stats"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"stats": stats})
}

// ListOrphanedMedia lists media not referenced anywhere (admin only)
func (h *MediaHandler) ListOrphanedMedia(c *gin.Context) {
	// TODO: Implement orphaned media detection in service
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Orphaned media detection not yet implemented"})
}

// CleanupOrphanedMedia removes orphaned media (admin only)
func (h *MediaHandler) CleanupOrphanedMedia(c *gin.Context) {
	// TODO: Implement orphaned media cleanup in service
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Orphaned media cleanup not yet implemented"})
}

// ForceDeleteMedia force deletes any media (admin only)
func (h *MediaHandler) ForceDeleteMedia(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid media ID"})
		return
	}

	// Force delete without ownership checks
	err = h.mediaService.DeleteMedia(c.Request.Context(), id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Media not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to force delete media"})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Media force deleted successfully"})
}

// AdminUpdateMedia allows admin to update any media (admin only)
func (h *MediaHandler) AdminUpdateMedia(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid media ID"})
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Admin can update additional fields
	if err := h.validator.ValidateAdminMediaUpdates(updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updatedMedia, err := h.mediaService.UpdateMedia(c.Request.Context(), id, updates)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			c.JSON(http.StatusNotFound, gin.H{"error": "Media not found"})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		}
		return
	}

	publicURL := h.mediaService.GetPublicURL(updatedMedia.FilePath)

	c.JSON(http.StatusOK, gin.H{
		"media": updatedMedia,
		"url":   publicURL,
	})
}

// ReprocessMedia reprocesses media to extract metadata (admin only)
func (h *MediaHandler) ReprocessMedia(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Media reprocessing not yet implemented"})
}

// MigrateMediaStorage migrates storage locations (admin only)
func (h *MediaHandler) MigrateMediaStorage(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Media storage migration not yet implemented"})
}

// CheckMediaHealth checks storage health (admin only)
func (h *MediaHandler) CheckMediaHealth(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Media health check not yet implemented"})
}

// BulkDeleteMedia bulk deletes media (admin only)
func (h *MediaHandler) BulkDeleteMedia(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Bulk delete not yet implemented"})
}

// BulkUpdateMedia bulk updates media (admin only)
func (h *MediaHandler) BulkUpdateMedia(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Bulk update not yet implemented"})
}

// BulkMoveMedia bulk moves media between languages (admin only)
func (h *MediaHandler) BulkMoveMedia(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Bulk move not yet implemented"})
}
