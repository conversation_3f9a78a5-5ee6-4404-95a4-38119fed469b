package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/library/character/models"
	"github.com/psynarios/admin_back/internal/library/character/service"
)

type CharacterHandler struct {
	service service.CharacterService
	logger  zerolog.Logger
}

func NewCharacterHandler(service service.CharacterService, logger zerolog.Logger) *CharacterHandler {
	return &CharacterHandler{
		service: service,
		logger:  logger.With().Str("component", "character_handler").<PERSON>gger(),
	}
}

// CreateCharacter creates a new character
func (h *CharacterHandler) CreateCharacter(c *gin.Context) {
	var req models.CharacterCreateRequest
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.<PERSON>rror()})
		return
	}

	// Get user ID from context (from auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	character, err := h.service.CreateCharacter(c.Request.Context(), &req, &userUUID)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to create character")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create character", "details": err.Error()})
		return
	}

	h.logger.Info().Str("character_id", character.ID.String()).Msg("Character created successfully")
	c.JSON(http.StatusCreated, character)
}

// GetCharacter gets a character by ID
func (h *CharacterHandler) GetCharacter(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid character ID"})
		return
	}

	// Parse language filter
	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	character, err := h.service.GetCharacter(c.Request.Context(), id, languageID)
	if err != nil {
		h.logger.Error().Err(err).Str("character_id", id.String()).Msg("Failed to get character")
		c.JSON(http.StatusNotFound, gin.H{"error": "Character not found"})
		return
	}

	c.JSON(http.StatusOK, character)
}

// GetCharacters gets all characters with pagination
func (h *CharacterHandler) GetCharacters(c *gin.Context) {
	// Parse pagination
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))
	activeOnly := c.DefaultQuery("active", "true") == "true"

	// Parse language filter
	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	characters, err := h.service.GetCharacters(c.Request.Context(), limit, offset, languageID, activeOnly)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get characters")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get characters"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"characters": characters,
		"pagination": gin.H{
			"limit":  limit,
			"offset": offset,
			"count":  len(characters),
		},
	})
}

// UpdateCharacter updates a character
func (h *CharacterHandler) UpdateCharacter(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid character ID"})
		return
	}

	var req models.CharacterUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	character, err := h.service.UpdateCharacter(c.Request.Context(), id, &req)
	if err != nil {
		h.logger.Error().Err(err).Str("character_id", id.String()).Msg("Failed to update character")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update character", "details": err.Error()})
		return
	}

	h.logger.Info().Str("character_id", id.String()).Msg("Character updated successfully")
	c.JSON(http.StatusOK, character)
}

// DeleteCharacter deletes a character
func (h *CharacterHandler) DeleteCharacter(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid character ID"})
		return
	}

	err = h.service.DeleteCharacter(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("character_id", id.String()).Msg("Failed to delete character")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete character"})
		return
	}

	h.logger.Info().Str("character_id", id.String()).Msg("Character deleted successfully")
	c.JSON(http.StatusNoContent, nil)
}

// SearchCharacters searches characters
func (h *CharacterHandler) SearchCharacters(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	characters, err := h.service.SearchCharacters(c.Request.Context(), query, languageID, limit, offset)
	if err != nil {
		h.logger.Error().Err(err).Str("query", query).Msg("Failed to search characters")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to search characters"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"characters": characters,
		"query":      query,
		"pagination": gin.H{
			"limit":  limit,
			"offset": offset,
			"count":  len(characters),
		},
	})
}

// GetCharactersByScenario gets characters by scenario ID
func (h *CharacterHandler) GetCharactersByScenario(c *gin.Context) {
	scenarioIDStr := c.Param("scenario_id")
	scenarioID, err := uuid.Parse(scenarioIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scenario ID"})
		return
	}

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	characters, err := h.service.GetCharactersByScenario(c.Request.Context(), scenarioID, languageID)
	if err != nil {
		h.logger.Error().Err(err).Str("scenario_id", scenarioID.String()).Msg("Failed to get characters by scenario")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get characters by scenario"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"characters": characters})
}
