package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/library/option/models"
	"github.com/psynarios/admin_back/internal/library/option/service"
)

type OptionHandler struct {
	service service.OptionService
	logger  zerolog.Logger
}

func NewOptionHandler(service service.OptionService, logger zerolog.Logger) *OptionHandler {
	return &OptionHandler{
		service: service,
		logger:  logger.With().Str("component", "option_handler").Logger(),
	}
}

// CreateOption creates a new option
func (h *OptionHandler) CreateOption(c *gin.Context) {
	var req models.OptionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	option, err := h.service.CreateOption(c.Request.Context(), &req, &userUUID)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to create option")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create option", "details": err.Error()})
		return
	}

	h.logger.Info().Str("option_id", option.ID.String()).Msg("Option created successfully")
	c.JSON(http.StatusCreated, option)
}

// GetOption gets an option by ID
func (h *OptionHandler) GetOption(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid option ID"})
		return
	}

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	option, err := h.service.GetOption(c.Request.Context(), id, languageID)
	if err != nil {
		h.logger.Error().Err(err).Str("option_id", id.String()).Msg("Failed to get option")
		c.JSON(http.StatusNotFound, gin.H{"error": "Option not found"})
		return
	}

	c.JSON(http.StatusOK, option)
}

// GetOptions gets all options with pagination
func (h *OptionHandler) GetOptions(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	offset, _ := strconv.Atoi(c.DefaultQuery("offset", "0"))
	activeOnly := c.DefaultQuery("active", "true") == "true"

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	options, err := h.service.GetOptions(c.Request.Context(), limit, offset, languageID, activeOnly)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to get options")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get options"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"options": options,
		"pagination": gin.H{
			"limit":  limit,
			"offset": offset,
			"count":  len(options),
		},
	})
}

// UpdateOption updates an option
func (h *OptionHandler) UpdateOption(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid option ID"})
		return
	}

	var req models.OptionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	option, err := h.service.UpdateOption(c.Request.Context(), id, &req)
	if err != nil {
		h.logger.Error().Err(err).Str("option_id", id.String()).Msg("Failed to update option")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update option", "details": err.Error()})
		return
	}

	h.logger.Info().Str("option_id", id.String()).Msg("Option updated successfully")
	c.JSON(http.StatusOK, option)
}

// DeleteOption deletes an option
func (h *OptionHandler) DeleteOption(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid option ID"})
		return
	}

	err = h.service.DeleteOption(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("option_id", id.String()).Msg("Failed to delete option")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete option"})
		return
	}

	h.logger.Info().Str("option_id", id.String()).Msg("Option deleted successfully")
	c.JSON(http.StatusNoContent, nil)
}

// GetOptionsByInteraction gets options by interaction ID
func (h *OptionHandler) GetOptionsByInteraction(c *gin.Context) {
	interactionIDStr := c.Param("interaction_id")
	interactionID, err := uuid.Parse(interactionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interaction ID"})
		return
	}

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	options, err := h.service.GetOptionsByInteraction(c.Request.Context(), interactionID, languageID)
	if err != nil {
		h.logger.Error().Err(err).Str("interaction_id", interactionID.String()).Msg("Failed to get options by interaction")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get options by interaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"options": options})
}

// GetCorrectOptionsByInteraction gets only correct options by interaction ID
func (h *OptionHandler) GetCorrectOptionsByInteraction(c *gin.Context) {
	interactionIDStr := c.Param("interaction_id")
	interactionID, err := uuid.Parse(interactionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interaction ID"})
		return
	}

	var languageID *int
	if langStr := c.Query("language"); langStr != "" {
		lang, err := strconv.Atoi(langStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid language ID"})
			return
		}
		languageID = &lang
	}

	options, err := h.service.GetCorrectOptionsByInteraction(c.Request.Context(), interactionID, languageID)
	if err != nil {
		h.logger.Error().Err(err).Str("interaction_id", interactionID.String()).Msg("Failed to get correct options by interaction")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get correct options by interaction"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"options": options})
}

// ValidateInteractionOptions validates option correctness for an interaction
func (h *OptionHandler) ValidateInteractionOptions(c *gin.Context) {
	interactionIDStr := c.Param("interaction_id")
	interactionID, err := uuid.Parse(interactionIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid interaction ID"})
		return
	}

	err = h.service.ValidateOptionCorrectness(c.Request.Context(), interactionID)
	if err != nil {
		h.logger.Error().Err(err).Str("interaction_id", interactionID.String()).Msg("Option validation failed")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Validation failed", "details": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Options are valid"})
}
