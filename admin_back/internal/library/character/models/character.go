package models

import (
	"time"

	"github.com/google/uuid"
	i18nModels "github.com/psynarios/admin_back/internal/i18n/models"
)

type CharacterType string

const (
	CharacterTypeCharacter CharacterType = "Character"
	CharacterTypeNarrator  CharacterType = "Narrator"
)

// Core character entity (language-agnostic)
type Character struct {
	ID            uuid.UUID     `json:"id" db:"id"`
	Code          string        `json:"code" db:"code" validate:"required,max=50"`
	CharacterType CharacterType `json:"character_type" db:"character_type"`
	IsActive      bool          `json:"is_active" db:"is_active"`
	CreatedBy     *uuid.UUID    `json:"created_by,omitempty" db:"created_by"`
	CreatedAt     time.Time     `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time     `json:"updated_at" db:"updated_at"`

	// Relations - loaded based on context
	Contents []CharacterContent `json:"contents,omitempty"`
}

// Language-specific content for characters
type CharacterContent struct {
	ID          uuid.UUID              `json:"id" db:"id"`
	CharacterID uuid.UUID              `json:"character_id" db:"character_id"`
	LanguageID  int                    `json:"language_id" db:"language_id"`
	Name        string                 `json:"name" db:"name" validate:"required,max=255"`
	Role        *string                `json:"role,omitempty" db:"role"`
	Bio         *string                `json:"bio,omitempty" db:"bio"`
	Stats       map[string]interface{} `json:"stats,omitempty" db:"stats"`
	PhotoURL    *string                `json:"photo_url,omitempty" db:"photo_url"`
	CreatedAt   time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at" db:"updated_at"`

	// Relations
	Language *i18nModels.Language `json:"language,omitempty"`
}

// Request DTOs
type CharacterCreateRequest struct {
	Code          string                    `json:"code" binding:"required,max=50"`
	CharacterType CharacterType             `json:"character_type" binding:"required"`
	Contents      []CharacterContentRequest `json:"contents" binding:"required,min=1,dive"`
}

type CharacterUpdateRequest struct {
	Code          *string                   `json:"code,omitempty" binding:"omitempty,max=50"`
	CharacterType *CharacterType            `json:"character_type,omitempty"`
	IsActive      *bool                     `json:"is_active,omitempty"`
	Contents      []CharacterContentRequest `json:"contents,omitempty" binding:"omitempty,dive"`
}

type CharacterContentRequest struct {
	LanguageID int                    `json:"language_id" binding:"required"`
	Name       string                 `json:"name" binding:"required,max=255"`
	Role       *string                `json:"role,omitempty"`
	Bio        *string                `json:"bio,omitempty"`
	Stats      map[string]interface{} `json:"stats,omitempty"`
	PhotoURL   *string                `json:"photo_url,omitempty"`
}

// Response DTOs
type CharacterResponse struct {
	ID            uuid.UUID                  `json:"id"`
	Code          string                     `json:"code"`
	CharacterType CharacterType              `json:"character_type"`
	IsActive      bool                       `json:"is_active"`
	CreatedBy     *uuid.UUID                 `json:"created_by,omitempty"`
	CreatedAt     time.Time                  `json:"created_at"`
	UpdatedAt     time.Time                  `json:"updated_at"`
	Contents      []CharacterContentResponse `json:"contents,omitempty"`
}

type CharacterContentResponse struct {
	ID         uuid.UUID              `json:"id"`
	LanguageID int                    `json:"language_id"`
	Name       string                 `json:"name"`
	Role       *string                `json:"role,omitempty"`
	Bio        *string                `json:"bio,omitempty"`
	Stats      map[string]interface{} `json:"stats,omitempty"`
	PhotoURL   *string                `json:"photo_url,omitempty"`
	Language   *i18nModels.Language   `json:"language,omitempty"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}
