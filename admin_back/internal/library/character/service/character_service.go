package service

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"github.com/psynarios/admin_back/internal/common/errors"
	"github.com/psynarios/admin_back/internal/library/character/models"
	"github.com/psynarios/admin_back/internal/library/character/repository"
)

type CharacterService interface {
	// Core CRUD operations
	CreateCharacter(ctx context.Context, req *models.CharacterCreateRequest, createdBy *uuid.UUID) (*models.CharacterResponse, error)
	GetCharacter(ctx context.Context, id uuid.UUID, languageID *int) (*models.CharacterResponse, error)
	GetCharacterByCode(ctx context.Context, code string, languageID *int) (*models.CharacterResponse, error)
	GetCharacters(ctx context.Context, limit, offset int, languageID *int, activeOnly bool) ([]*models.CharacterResponse, error)
	UpdateCharacter(ctx context.Context, id uuid.UUID, req *models.CharacterUpdateRequest) (*models.CharacterResponse, error)
	DeleteCharacter(ctx context.Context, id uuid.UUID) error

	// Content operations
	CreateCharacterContent(ctx context.Context, characterID uuid.UUID, req *models.CharacterContentRequest) (*models.CharacterContentResponse, error)
	UpdateCharacterContent(ctx context.Context, characterID uuid.UUID, languageID int, req *models.CharacterContentRequest) (*models.CharacterContentResponse, error)
	DeleteCharacterContent(ctx context.Context, characterID uuid.UUID, languageID int) error

	// Advanced operations
	GetCharactersByScenario(ctx context.Context, scenarioID uuid.UUID, languageID *int) ([]*models.CharacterResponse, error)
	SearchCharacters(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.CharacterResponse, error)
	GetCharacterStatistics(ctx context.Context) (*CharacterStatistics, error)
}

type CharacterStatistics struct {
	TotalCharacters  int64                          `json:"total_characters"`
	ActiveCharacters int64                          `json:"active_characters"`
	ByType           map[models.CharacterType]int64 `json:"by_type"`
}

type characterService struct {
	repo repository.CharacterRepository
}

func NewCharacterService(repo repository.CharacterRepository) CharacterService {
	return &characterService{repo: repo}
}

func (s *characterService) CreateCharacter(ctx context.Context, req *models.CharacterCreateRequest, createdBy *uuid.UUID) (*models.CharacterResponse, error) {
	// Check if code already exists
	exists, err := s.repo.ExistsByCode(ctx, req.Code, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to check code existence: %w", err)
	}
	if exists {
		return nil, errors.NewConflictError("character with this code already exists", nil)
	}

	// Create character entity
	character := &models.Character{
		ID:            uuid.New(),
		Code:          req.Code,
		CharacterType: req.CharacterType,
		IsActive:      true,
		CreatedBy:     createdBy,
	}

	err = s.repo.Create(ctx, character)
	if err != nil {
		return nil, fmt.Errorf("failed to create character: %w", err)
	}

	// Create content for each language
	for _, contentReq := range req.Contents {
		content := &models.CharacterContent{
			ID:          uuid.New(),
			CharacterID: character.ID,
			LanguageID:  contentReq.LanguageID,
			Name:        contentReq.Name,
			Role:        contentReq.Role,
			Bio:         contentReq.Bio,
			Stats:       contentReq.Stats,
			PhotoURL:    contentReq.PhotoURL,
		}

		err = s.repo.CreateContent(ctx, content)
		if err != nil {
			return nil, fmt.Errorf("failed to create character content: %w", err)
		}
	}

	return s.GetCharacter(ctx, character.ID, nil)
}

func (s *characterService) GetCharacter(ctx context.Context, id uuid.UUID, languageID *int) (*models.CharacterResponse, error) {
	character, err := s.repo.GetWithContents(ctx, id, languageID)
	if err != nil {
		return nil, err
	}

	return s.toResponse(character), nil
}

func (s *characterService) GetCharacterByCode(ctx context.Context, code string, languageID *int) (*models.CharacterResponse, error) {
	character, err := s.repo.GetByCode(ctx, code)
	if err != nil {
		return nil, err
	}

	return s.GetCharacter(ctx, character.ID, languageID)
}

func (s *characterService) GetCharacters(ctx context.Context, limit, offset int, languageID *int, activeOnly bool) ([]*models.CharacterResponse, error) {
	characters, err := s.repo.GetAll(ctx, limit, offset, activeOnly)
	if err != nil {
		return nil, err
	}

	// Load contents for each character
	responses := make([]*models.CharacterResponse, len(characters))
	for i, character := range characters {
		charWithContents, err := s.repo.GetWithContents(ctx, character.ID, languageID)
		if err != nil {
			return nil, err
		}
		responses[i] = s.toResponse(charWithContents)
	}

	return responses, nil
}

func (s *characterService) UpdateCharacter(ctx context.Context, id uuid.UUID, req *models.CharacterUpdateRequest) (*models.CharacterResponse, error) {
	character, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Update core fields if provided
	if req.Code != nil {
		exists, err := s.repo.ExistsByCode(ctx, *req.Code, &id)
		if err != nil {
			return nil, fmt.Errorf("failed to check code existence: %w", err)
		}
		if exists {
			return nil, errors.NewConflictError("character with this code already exists", nil)
		}
		character.Code = *req.Code
	}

	if req.CharacterType != nil {
		character.CharacterType = *req.CharacterType
	}

	if req.IsActive != nil {
		character.IsActive = *req.IsActive
	}

	err = s.repo.Update(ctx, character)
	if err != nil {
		return nil, fmt.Errorf("failed to update character: %w", err)
	}

	// Update contents if provided
	if req.Contents != nil {
		for _, contentReq := range req.Contents {
			existingContent, err := s.repo.GetContentByCharacterAndLanguage(ctx, id, contentReq.LanguageID)
			if err != nil && !errors.IsNotFoundError(err) {
				return nil, err
			}

			if existingContent != nil {
				// Update existing content
				existingContent.Name = contentReq.Name
				existingContent.Role = contentReq.Role
				existingContent.Bio = contentReq.Bio
				existingContent.Stats = contentReq.Stats
				existingContent.PhotoURL = contentReq.PhotoURL
				err = s.repo.UpdateContent(ctx, existingContent)
				if err != nil {
					return nil, fmt.Errorf("failed to update character content: %w", err)
				}
			} else {
				// Create new content
				content := &models.CharacterContent{
					ID:          uuid.New(),
					CharacterID: id,
					LanguageID:  contentReq.LanguageID,
					Name:        contentReq.Name,
					Role:        contentReq.Role,
					Bio:         contentReq.Bio,
					Stats:       contentReq.Stats,
					PhotoURL:    contentReq.PhotoURL,
				}
				err = s.repo.CreateContent(ctx, content)
				if err != nil {
					return nil, fmt.Errorf("failed to create character content: %w", err)
				}
			}
		}
	}

	return s.GetCharacter(ctx, id, nil)
}

func (s *characterService) DeleteCharacter(ctx context.Context, id uuid.UUID) error {
	return s.repo.Delete(ctx, id)
}

func (s *characterService) CreateCharacterContent(ctx context.Context, characterID uuid.UUID, req *models.CharacterContentRequest) (*models.CharacterContentResponse, error) {
	// Check if content already exists for this character and language
	existingContent, err := s.repo.GetContentByCharacterAndLanguage(ctx, characterID, req.LanguageID)
	if err != nil && !errors.IsNotFoundError(err) {
		return nil, err
	}
	if existingContent != nil {
		return nil, errors.NewConflictError("character content for this language already exists", nil)
	}

	content := &models.CharacterContent{
		ID:          uuid.New(),
		CharacterID: characterID,
		LanguageID:  req.LanguageID,
		Name:        req.Name,
		Role:        req.Role,
		Bio:         req.Bio,
		Stats:       req.Stats,
		PhotoURL:    req.PhotoURL,
	}

	err = s.repo.CreateContent(ctx, content)
	if err != nil {
		return nil, fmt.Errorf("failed to create character content: %w", err)
	}

	createdContent, err := s.repo.GetContentByCharacterAndLanguage(ctx, characterID, req.LanguageID)
	if err != nil {
		return nil, err
	}

	return s.toContentResponse(createdContent), nil
}

func (s *characterService) UpdateCharacterContent(ctx context.Context, characterID uuid.UUID, languageID int, req *models.CharacterContentRequest) (*models.CharacterContentResponse, error) {
	// Get existing content
	content, err := s.repo.GetContentByCharacterAndLanguage(ctx, characterID, languageID)
	if err != nil {
		return nil, err
	}

	// Update fields
	content.Name = req.Name
	content.Role = req.Role
	content.Bio = req.Bio
	content.Stats = req.Stats
	content.PhotoURL = req.PhotoURL

	err = s.repo.UpdateContent(ctx, content)
	if err != nil {
		return nil, fmt.Errorf("failed to update character content: %w", err)
	}

	// Get updated content with language info
	updatedContent, err := s.repo.GetContentByCharacterAndLanguage(ctx, characterID, languageID)
	if err != nil {
		return nil, err
	}

	return s.toContentResponse(updatedContent), nil
}

func (s *characterService) DeleteCharacterContent(ctx context.Context, characterID uuid.UUID, languageID int) error {
	return s.repo.DeleteContent(ctx, characterID, languageID)
}

func (s *characterService) GetCharactersByScenario(ctx context.Context, scenarioID uuid.UUID, languageID *int) ([]*models.CharacterResponse, error) {
	characters, err := s.repo.GetByScenario(ctx, scenarioID, languageID)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.CharacterResponse, len(characters))
	for i, character := range characters {
		responses[i] = s.toResponse(character)
	}

	return responses, nil
}

func (s *characterService) SearchCharacters(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.CharacterResponse, error) {
	characters, err := s.repo.Search(ctx, query, languageID, limit, offset)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.CharacterResponse, len(characters))
	for i, character := range characters {
		responses[i] = s.toResponse(character)
	}

	return responses, nil
}

func (s *characterService) GetCharacterStatistics(ctx context.Context) (*CharacterStatistics, error) {
	totalCount, err := s.repo.Count(ctx, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get total character count: %w", err)
	}

	activeCount, err := s.repo.Count(ctx, true)
	if err != nil {
		return nil, fmt.Errorf("failed to get active character count: %w", err)
	}

	// Note: You might want to implement GetCountByType in repository for detailed stats
	return &CharacterStatistics{
		TotalCharacters:  totalCount,
		ActiveCharacters: activeCount,
		ByType:           make(map[models.CharacterType]int64),
	}, nil
}

// Helper methods
func (s *characterService) toResponse(character *models.Character) *models.CharacterResponse {
	response := &models.CharacterResponse{
		ID:            character.ID,
		Code:          character.Code,
		CharacterType: character.CharacterType,
		IsActive:      character.IsActive,
		CreatedBy:     character.CreatedBy,
		CreatedAt:     character.CreatedAt,
		UpdatedAt:     character.UpdatedAt,
	}

	if len(character.Contents) > 0 {
		response.Contents = make([]models.CharacterContentResponse, len(character.Contents))
		for i, content := range character.Contents {
			response.Contents[i] = models.CharacterContentResponse{
				ID:         content.ID,
				LanguageID: content.LanguageID,
				Name:       content.Name,
				Role:       content.Role,
				Bio:        content.Bio,
				Stats:      content.Stats,
				PhotoURL:   content.PhotoURL,
				Language:   content.Language,
				CreatedAt:  content.CreatedAt,
				UpdatedAt:  content.UpdatedAt,
			}
		}
	}

	return response
}

func (s *characterService) toContentResponse(content *models.CharacterContent) *models.CharacterContentResponse {
	return &models.CharacterContentResponse{
		ID:         content.ID,
		LanguageID: content.LanguageID,
		Name:       content.Name,
		Role:       content.Role,
		Bio:        content.Bio,
		Stats:      content.Stats,
		PhotoURL:   content.PhotoURL,
		Language:   content.Language,
		CreatedAt:  content.CreatedAt,
		UpdatedAt:  content.UpdatedAt,
	}
}
