package postgres

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	i18nModels "github.com/psynarios/admin_back/internal/i18n/models"
	"github.com/psynarios/admin_back/internal/library/character/models"
	"github.com/psynarios/admin_back/internal/library/character/repository"
)

type characterRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

func NewCharacterRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.CharacterRepository {
	return &characterRepository{
		db:     db,
		logger: logger.With().Str("component", "character_repository").Logger(),
	}
}

func (r *characterRepository) Create(ctx context.Context, character *models.Character) error {
	query := `
		INSERT INTO characters (id, code, character_type, is_active, created_by, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
	`

	if character.ID == uuid.Nil {
		character.ID = uuid.New()
	}

	now := time.Now()
	character.CreatedAt = now
	character.UpdatedAt = now

	_, err := r.db.Exec(
		ctx,
		query,
		character.ID,
		character.Code,
		character.CharacterType,
		character.IsActive,
		character.CreatedBy,
		character.CreatedAt,
		character.UpdatedAt,
	)

	if err != nil {
		r.logger.Error().Err(err).Str("code", character.Code).Msg("Failed to create character")
		return fmt.Errorf("failed to create character: %w", err)
	}

	r.logger.Info().Str("id", character.ID.String()).Str("code", character.Code).Msg("Character created successfully")
	return nil
}

func (r *characterRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Character, error) {
	query := `
		SELECT id, code, character_type, is_active, created_by, created_at, updated_at
		FROM characters
		WHERE id = $1
	`

	var character models.Character
	row := r.db.QueryRow(ctx, query, id)

	err := row.Scan(
		&character.ID,
		&character.Code,
		&character.CharacterType,
		&character.IsActive,
		&character.CreatedBy,
		&character.CreatedAt,
		&character.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("character not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get character: %w", err)
	}

	return &character, nil
}

func (r *characterRepository) GetByCode(ctx context.Context, code string) (*models.Character, error) {
	query := `
		SELECT id, code, character_type, is_active, created_by, created_at, updated_at
		FROM characters
		WHERE code = $1
	`

	var character models.Character
	row := r.db.QueryRow(ctx, query, code)

	err := row.Scan(
		&character.ID,
		&character.Code,
		&character.CharacterType,
		&character.IsActive,
		&character.CreatedBy,
		&character.CreatedAt,
		&character.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("character not found: %s", code)
		}
		return nil, fmt.Errorf("failed to get character by code: %w", err)
	}

	return &character, nil
}

func (r *characterRepository) GetAll(ctx context.Context, limit, offset int, activeOnly bool) ([]*models.Character, error) {
	query := `
		SELECT id, code, character_type, is_active, created_by, created_at, updated_at
		FROM characters
	`
	args := []interface{}{}
	argIndex := 1

	if activeOnly {
		query += " WHERE is_active = $" + fmt.Sprintf("%d", argIndex)
		args = append(args, true)
		argIndex++
	}

	query += " ORDER BY created_at DESC"

	if limit > 0 {
		query += " LIMIT $" + fmt.Sprintf("%d", argIndex)
		args = append(args, limit)
		argIndex++
	}

	if offset > 0 {
		query += " OFFSET $" + fmt.Sprintf("%d", argIndex)
		args = append(args, offset)
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get characters: %w", err)
	}
	defer rows.Close()

	var characters []*models.Character
	for rows.Next() {
		var character models.Character
		err := rows.Scan(
			&character.ID,
			&character.Code,
			&character.CharacterType,
			&character.IsActive,
			&character.CreatedBy,
			&character.CreatedAt,
			&character.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan character: %w", err)
		}
		characters = append(characters, &character)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating characters: %w", err)
	}

	return characters, nil
}

func (r *characterRepository) Update(ctx context.Context, character *models.Character) error {
	query := `
		UPDATE characters
		SET code = $2, character_type = $3, is_active = $4, updated_at = $5
		WHERE id = $1
	`

	character.UpdatedAt = time.Now()

	result, err := r.db.Exec(
		ctx,
		query,
		character.ID,
		character.Code,
		character.CharacterType,
		character.IsActive,
		character.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update character: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("character not found: %s", character.ID)
	}

	r.logger.Info().Str("id", character.ID.String()).Msg("Character updated successfully")
	return nil
}

func (r *characterRepository) Delete(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM characters WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete character: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("character not found: %s", id)
	}

	r.logger.Info().Str("id", id.String()).Msg("Character deleted successfully")
	return nil
}

func (r *characterRepository) CreateContent(ctx context.Context, content *models.CharacterContent) error {
	query := `
		INSERT INTO character_contents (id, character_id, language_id, name, role, bio, stats, photo_url, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`

	if content.ID == uuid.Nil {
		content.ID = uuid.New()
	}

	now := time.Now()
	content.CreatedAt = now
	content.UpdatedAt = now

	_, err := r.db.Exec(
		ctx,
		query,
		content.ID,
		content.CharacterID,
		content.LanguageID,
		content.Name,
		content.Role,
		content.Bio,
		content.Stats,
		content.PhotoURL,
		content.CreatedAt,
		content.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to create character content: %w", err)
	}

	return nil
}

func (r *characterRepository) GetContentByCharacterAndLanguage(ctx context.Context, characterID uuid.UUID, languageID int) (*models.CharacterContent, error) {
	query := `
		SELECT cc.id, cc.character_id, cc.language_id, cc.name, cc.role, cc.bio, cc.stats, cc.photo_url, cc.created_at, cc.updated_at,
			   l.id, l.code, l.name, l.native_name, l.is_active, l.is_default
		FROM character_contents cc
		LEFT JOIN languages l ON cc.language_id = l.id
		WHERE cc.character_id = $1 AND cc.language_id = $2
	`

	var content models.CharacterContent
	var langID sql.NullInt32
	var langCode sql.NullString
	var langName sql.NullString
	var langNativeName sql.NullString
	var langIsActive sql.NullBool
	var langIsDefault sql.NullBool

	row := r.db.QueryRow(ctx, query, characterID, languageID)

	err := row.Scan(
		&content.ID,
		&content.CharacterID,
		&content.LanguageID,
		&content.Name,
		&content.Role,
		&content.Bio,
		&content.Stats,
		&content.PhotoURL,
		&content.CreatedAt,
		&content.UpdatedAt,
		&langID,
		&langCode,
		&langName,
		&langNativeName,
		&langIsActive,
		&langIsDefault,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("character content not found")
		}
		return nil, fmt.Errorf("failed to get character content: %w", err)
	}

	// Set language if available
	if langID.Valid {
		content.Language = &i18nModels.Language{
			ID:         int(langID.Int32),
			Code:       langCode.String,
			Name:       langName.String,
			NativeName: langNativeName.String,
			IsActive:   langIsActive.Bool,
			IsDefault:  langIsDefault.Bool,
		}
	}

	return &content, nil
}

func (r *characterRepository) GetContentsByCharacter(ctx context.Context, characterID uuid.UUID) ([]*models.CharacterContent, error) {
	query := `
		SELECT cc.id, cc.character_id, cc.language_id, cc.name, cc.role, cc.bio, cc.stats, cc.photo_url, cc.created_at, cc.updated_at,
			   l.id, l.code, l.name, l.native_name, l.is_active, l.is_default
		FROM character_contents cc
		LEFT JOIN languages l ON cc.language_id = l.id
		WHERE cc.character_id = $1
		ORDER BY l.is_default DESC, l.code ASC
	`

	rows, err := r.db.Query(ctx, query, characterID)
	if err != nil {
		return nil, fmt.Errorf("failed to get character contents: %w", err)
	}
	defer rows.Close()

	var contents []*models.CharacterContent
	for rows.Next() {
		var content models.CharacterContent
		var languageID sql.NullInt32
		var languageCode sql.NullString
		var languageName sql.NullString
		var languageNativeName sql.NullString
		var languageIsActive sql.NullBool
		var languageIsDefault sql.NullBool

		err := rows.Scan(
			&content.ID,
			&content.CharacterID,
			&content.LanguageID,
			&content.Name,
			&content.Role,
			&content.Bio,
			&content.Stats,
			&content.PhotoURL,
			&content.CreatedAt,
			&content.UpdatedAt,
			&languageID,
			&languageCode,
			&languageName,
			&languageNativeName,
			&languageIsActive,
			&languageIsDefault,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan character content: %w", err)
		}

		// Set language if available
		if languageID.Valid {
			content.Language = &i18nModels.Language{
				ID:         int(languageID.Int32),
				Code:       languageCode.String,
				Name:       languageName.String,
				NativeName: languageNativeName.String,
				IsActive:   languageIsActive.Bool,
				IsDefault:  languageIsDefault.Bool,
			}
		}

		contents = append(contents, &content)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating character contents: %w", err)
	}

	return contents, nil
}

func (r *characterRepository) UpdateContent(ctx context.Context, content *models.CharacterContent) error {
	query := `
		UPDATE character_contents
		SET name = $3, role = $4, bio = $5, stats = $6, photo_url = $7, updated_at = $8
		WHERE character_id = $1 AND language_id = $2
	`

	content.UpdatedAt = time.Now()

	result, err := r.db.Exec(
		ctx,
		query,
		content.CharacterID,
		content.LanguageID,
		content.Name,
		content.Role,
		content.Bio,
		content.Stats,
		content.PhotoURL,
		content.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update character content: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("character content not found")
	}

	return nil
}

func (r *characterRepository) DeleteContent(ctx context.Context, characterID uuid.UUID, languageID int) error {
	query := `DELETE FROM character_contents WHERE character_id = $1 AND language_id = $2`

	result, err := r.db.Exec(ctx, query, characterID, languageID)
	if err != nil {
		return fmt.Errorf("failed to delete character content: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("character content not found")
	}

	return nil
}

func (r *characterRepository) GetWithContents(ctx context.Context, id uuid.UUID, languageID *int) (*models.Character, error) {
	character, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	var contents []*models.CharacterContent
	if languageID != nil {
		content, err := r.GetContentByCharacterAndLanguage(ctx, id, *languageID)
		if err != nil && !strings.Contains(err.Error(), "not found") {
			return nil, err
		}
		if content != nil {
			contents = []*models.CharacterContent{content}
		}
	} else {
		contents, err = r.GetContentsByCharacter(ctx, id)
		if err != nil {
			return nil, err
		}
	}

	character.Contents = make([]models.CharacterContent, len(contents))
	for i, content := range contents {
		character.Contents[i] = *content
	}

	return character, nil
}

func (r *characterRepository) GetByScenario(ctx context.Context, scenarioID uuid.UUID, languageID *int) ([]*models.Character, error) {
	query := `
		SELECT DISTINCT c.id, c.code, c.character_type, c.is_active, c.created_by, c.created_at, c.updated_at
		FROM characters c
		INNER JOIN interactions i ON c.id = i.character_id
		WHERE i.scenario_id = $1 AND c.is_active = true
		ORDER BY c.created_at ASC
	`

	rows, err := r.db.Query(ctx, query, scenarioID)
	if err != nil {
		return nil, fmt.Errorf("failed to get characters by scenario: %w", err)
	}
	defer rows.Close()

	var characters []*models.Character
	for rows.Next() {
		var character models.Character
		err := rows.Scan(
			&character.ID,
			&character.Code,
			&character.CharacterType,
			&character.IsActive,
			&character.CreatedBy,
			&character.CreatedAt,
			&character.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan character: %w", err)
		}

		// Load contents for each character
		var contents []*models.CharacterContent
		if languageID != nil {
			content, err := r.GetContentByCharacterAndLanguage(ctx, character.ID, *languageID)
			if err != nil && !strings.Contains(err.Error(), "not found") {
				return nil, err
			}
			if content != nil {
				contents = []*models.CharacterContent{content}
			}
		} else {
			contents, err = r.GetContentsByCharacter(ctx, character.ID)
			if err != nil {
				return nil, err
			}
		}

		character.Contents = make([]models.CharacterContent, len(contents))
		for i, content := range contents {
			character.Contents[i] = *content
		}

		characters = append(characters, &character)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating characters: %w", err)
	}

	return characters, nil
}

func (r *characterRepository) Search(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.Character, error) {
	searchQuery := `
		SELECT DISTINCT c.id, c.code, c.character_type, c.is_active, c.created_by, c.created_at, c.updated_at
		FROM characters c
		LEFT JOIN character_contents cc ON c.id = cc.character_id
		WHERE c.is_active = true AND (
			c.code ILIKE $1 OR 
			cc.name ILIKE $1 OR 
			cc.role ILIKE $1 OR 
			cc.bio ILIKE $1
		)
	`

	args := []interface{}{"%" + query + "%"}
	argIndex := 2

	if languageID != nil {
		searchQuery += " AND cc.language_id = $" + fmt.Sprintf("%d", argIndex)
		args = append(args, *languageID)
		argIndex++
	}

	searchQuery += " ORDER BY c.created_at DESC"

	if limit > 0 {
		searchQuery += " LIMIT $" + fmt.Sprintf("%d", argIndex)
		args = append(args, limit)
		argIndex++
	}

	if offset > 0 {
		searchQuery += " OFFSET $" + fmt.Sprintf("%d", argIndex)
		args = append(args, offset)
	}

	rows, err := r.db.Query(ctx, searchQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to search characters: %w", err)
	}
	defer rows.Close()

	var characters []*models.Character
	for rows.Next() {
		var character models.Character
		err := rows.Scan(
			&character.ID,
			&character.Code,
			&character.CharacterType,
			&character.IsActive,
			&character.CreatedBy,
			&character.CreatedAt,
			&character.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan character: %w", err)
		}

		// Load contents for each character
		var contents []*models.CharacterContent
		if languageID != nil {
			content, err := r.GetContentByCharacterAndLanguage(ctx, character.ID, *languageID)
			if err != nil && !strings.Contains(err.Error(), "not found") {
				return nil, err
			}
			if content != nil {
				contents = []*models.CharacterContent{content}
			}
		} else {
			contents, err = r.GetContentsByCharacter(ctx, character.ID)
			if err != nil {
				return nil, err
			}
		}

		character.Contents = make([]models.CharacterContent, len(contents))
		for i, content := range contents {
			character.Contents[i] = *content
		}

		characters = append(characters, &character)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating characters: %w", err)
	}

	return characters, nil
}

func (r *characterRepository) ExistsByCode(ctx context.Context, code string, excludeID *uuid.UUID) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM characters WHERE code = $1`
	args := []interface{}{code}

	if excludeID != nil {
		query += " AND id != $2"
		args = append(args, *excludeID)
	}

	query += ")"

	var exists bool
	err := r.db.QueryRow(ctx, query, args...).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check character existence: %w", err)
	}

	return exists, nil
}

func (r *characterRepository) Count(ctx context.Context, activeOnly bool) (int64, error) {
	query := `SELECT COUNT(*) FROM characters`
	args := []interface{}{}

	if activeOnly {
		query += " WHERE is_active = $1"
		args = append(args, true)
	}

	var count int64
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count characters: %w", err)
	}

	return count, nil
}
