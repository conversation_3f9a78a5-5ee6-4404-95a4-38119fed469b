package repository

import (
	"context"

	"github.com/google/uuid"
	"github.com/psynarios/admin_back/internal/library/character/models"
)

type CharacterRepository interface {
	// Core CRUD operations
	Create(ctx context.Context, character *models.Character) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Character, error)
	GetByCode(ctx context.Context, code string) (*models.Character, error)
	GetAll(ctx context.Context, limit, offset int, activeOnly bool) ([]*models.Character, error)
	Update(ctx context.Context, character *models.Character) error
	Delete(ctx context.Context, id uuid.UUID) error

	// Content operations
	CreateContent(ctx context.Context, content *models.CharacterContent) error
	GetContentByCharacterAndLanguage(ctx context.Context, characterID uuid.UUID, languageID int) (*models.CharacterContent, error)
	GetContentsByCharacter(ctx context.Context, characterID uuid.UUID) ([]*models.CharacterContent, error)
	UpdateContent(ctx context.Context, content *models.CharacterContent) error
	DeleteContent(ctx context.Context, characterID uuid.UUID, languageID int) error

	// Advanced queries
	GetWithContents(ctx context.Context, id uuid.UUID, languageID *int) (*models.Character, error)
	GetByScenario(ctx context.Context, scenarioID uuid.UUID, languageID *int) ([]*models.Character, error)
	Search(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.Character, error)

	// Utility methods
	ExistsByCode(ctx context.Context, code string, excludeID *uuid.UUID) (bool, error)
	Count(ctx context.Context, activeOnly bool) (int64, error)
}
