package postgres

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	i18nModels "github.com/psynarios/admin_back/internal/i18n/models"
	"github.com/psynarios/admin_back/internal/library/interaction/models"
	"github.com/psynarios/admin_back/internal/library/interaction/repository"
)

// interactionRepository implements the InteractionRepository interface
type interactionRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

// NewInteractionRepository creates a new interaction repository
func NewInteractionRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.InteractionRepository {
	return &interactionRepository{
		db:     db,
		logger: logger.With().Str("component", "interaction_repository").<PERSON><PERSON>(),
	}
}

// Create creates a new interaction
func (r *interactionRepository) Create(ctx context.Context, interaction *models.Interaction) error {
	query := `
		INSERT INTO interactions (id, code, character_id, scenario_id, is_active, created_by, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	if interaction.ID == uuid.Nil {
		interaction.ID = uuid.New()
	}

	now := time.Now()
	interaction.CreatedAt = now
	interaction.UpdatedAt = now

	_, err := r.db.Exec(
		ctx,
		query,
		interaction.ID,
		interaction.Code,
		interaction.CharacterID,
		interaction.ScenarioID,
		interaction.IsActive,
		interaction.CreatedBy,
		interaction.CreatedAt,
		interaction.UpdatedAt,
	)

	if err != nil {
		r.logger.Error().Err(err).
			Str("code", interaction.Code).
			Str("character_id", interaction.CharacterID.String()).
			Str("scenario_id", interaction.ScenarioID.String()).
			Msg("Failed to create interaction")
		return fmt.Errorf("failed to create interaction: %w", err)
	}

	r.logger.Info().
		Str("id", interaction.ID.String()).
		Str("code", interaction.Code).
		Msg("Interaction created successfully")
	return nil
}

// GetByID gets an interaction by ID
func (r *interactionRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Interaction, error) {
	query := `
		SELECT id, code, character_id, scenario_id, is_active, created_by, created_at, updated_at
		FROM interactions
		WHERE id = $1
	`

	var interaction models.Interaction
	row := r.db.QueryRow(ctx, query, id)

	err := row.Scan(
		&interaction.ID,
		&interaction.Code,
		&interaction.CharacterID,
		&interaction.ScenarioID,
		&interaction.IsActive,
		&interaction.CreatedBy,
		&interaction.CreatedAt,
		&interaction.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("interaction not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get interaction: %w", err)
	}

	return &interaction, nil
}

// GetByCode gets an interaction by code
func (r *interactionRepository) GetByCode(ctx context.Context, code string) (*models.Interaction, error) {
	query := `
		SELECT id, code, character_id, scenario_id, is_active, created_by, created_at, updated_at
		FROM interactions
		WHERE code = $1
	`

	var interaction models.Interaction
	row := r.db.QueryRow(ctx, query, code)

	err := row.Scan(
		&interaction.ID,
		&interaction.Code,
		&interaction.CharacterID,
		&interaction.ScenarioID,
		&interaction.IsActive,
		&interaction.CreatedBy,
		&interaction.CreatedAt,
		&interaction.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("interaction not found with code: %s", code)
		}
		return nil, fmt.Errorf("failed to get interaction by code: %w", err)
	}

	return &interaction, nil
}

// GetAll gets all interactions with pagination and filtering
func (r *interactionRepository) GetAll(ctx context.Context, limit, offset int, activeOnly bool) ([]*models.Interaction, error) {
	query := `
		SELECT id, code, character_id, scenario_id, is_active, created_by, created_at, updated_at
		FROM interactions
	`
	args := []interface{}{}
	argIndex := 1

	if activeOnly {
		query += " WHERE is_active = $" + fmt.Sprintf("%d", argIndex)
		args = append(args, true)
		argIndex++
	}

	query += " ORDER BY created_at DESC"

	if limit > 0 {
		query += " LIMIT $" + fmt.Sprintf("%d", argIndex)
		args = append(args, limit)
		argIndex++
	}

	if offset > 0 {
		query += " OFFSET $" + fmt.Sprintf("%d", argIndex)
		args = append(args, offset)
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get interactions: %w", err)
	}
	defer rows.Close()

	var interactions []*models.Interaction
	for rows.Next() {
		var interaction models.Interaction
		err := rows.Scan(
			&interaction.ID,
			&interaction.Code,
			&interaction.CharacterID,
			&interaction.ScenarioID,
			&interaction.IsActive,
			&interaction.CreatedBy,
			&interaction.CreatedAt,
			&interaction.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan interaction: %w", err)
		}
		interactions = append(interactions, &interaction)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating interactions: %w", err)
	}

	r.logger.Debug().Int("count", len(interactions)).Msg("Retrieved interactions")
	return interactions, nil
}

// Update updates an interaction
func (r *interactionRepository) Update(ctx context.Context, interaction *models.Interaction) error {
	query := `
		UPDATE interactions
		SET code = $2, character_id = $3, scenario_id = $4, is_active = $5, updated_at = $6
		WHERE id = $1
	`

	interaction.UpdatedAt = time.Now()

	result, err := r.db.Exec(
		ctx,
		query,
		interaction.ID,
		interaction.Code,
		interaction.CharacterID,
		interaction.ScenarioID,
		interaction.IsActive,
		interaction.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update interaction: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("interaction not found: %s", interaction.ID)
	}

	r.logger.Info().
		Str("id", interaction.ID.String()).
		Str("code", interaction.Code).
		Msg("Interaction updated successfully")
	return nil
}

// Delete deletes an interaction by ID
func (r *interactionRepository) Delete(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM interactions WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete interaction: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("interaction not found: %s", id)
	}

	r.logger.Info().Str("id", id.String()).Msg("Interaction deleted successfully")
	return nil
}

// CreateContent creates content for an interaction
func (r *interactionRepository) CreateContent(ctx context.Context, content *models.InteractionContent) error {
	query := `
		INSERT INTO interaction_contents (
			id, interaction_id, language_id, instruction, correction, question, 
			instruction_video_url, correction_video_url, additional_media_url, 
			created_at, updated_at
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
	`

	if content.ID == uuid.Nil {
		content.ID = uuid.New()
	}

	now := time.Now()
	content.CreatedAt = now
	content.UpdatedAt = now

	_, err := r.db.Exec(
		ctx,
		query,
		content.ID,
		content.InteractionID,
		content.LanguageID,
		content.Instruction,
		content.Correction,
		content.Question,
		content.InstructionVideoURL,
		content.CorrectionVideoURL,
		content.AdditionalMediaURL,
		content.CreatedAt,
		content.UpdatedAt,
	)

	if err != nil {
		r.logger.Error().Err(err).
			Str("interaction_id", content.InteractionID.String()).
			Int("language_id", content.LanguageID).
			Msg("Failed to create interaction content")
		return fmt.Errorf("failed to create interaction content: %w", err)
	}

	return nil
}

// GetContentByInteractionAndLanguage gets content for specific interaction and language
func (r *interactionRepository) GetContentByInteractionAndLanguage(ctx context.Context, interactionID uuid.UUID, languageID int) (*models.InteractionContent, error) {
	query := `
		SELECT 
			ic.id, ic.interaction_id, ic.language_id, ic.instruction, ic.correction, ic.question, 
			ic.instruction_video_url, ic.correction_video_url, ic.additional_media_url, 
			ic.created_at, ic.updated_at,
			l.id, l.code, l.name, l.native_name, l.is_active, l.is_default
		FROM interaction_contents ic
		LEFT JOIN languages l ON ic.language_id = l.id
		WHERE ic.interaction_id = $1 AND ic.language_id = $2
	`

	var content models.InteractionContent
	var langID sql.NullInt32
	var langCode sql.NullString
	var langName sql.NullString
	var langNativeName sql.NullString
	var langIsActive sql.NullBool
	var langIsDefault sql.NullBool

	row := r.db.QueryRow(ctx, query, interactionID, languageID)

	err := row.Scan(
		&content.ID,
		&content.InteractionID,
		&content.LanguageID,
		&content.Instruction,
		&content.Correction,
		&content.Question,
		&content.InstructionVideoURL,
		&content.CorrectionVideoURL,
		&content.AdditionalMediaURL,
		&content.CreatedAt,
		&content.UpdatedAt,
		&langID,
		&langCode,
		&langName,
		&langNativeName,
		&langIsActive,
		&langIsDefault,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("interaction content not found for interaction %s and language %d", interactionID, languageID)
		}
		return nil, fmt.Errorf("failed to get interaction content: %w", err)
	}

	// Set language if available
	if langID.Valid {
		content.Language = &i18nModels.Language{
			ID:         int(langID.Int32),
			Code:       langCode.String,
			Name:       langName.String,
			NativeName: langNativeName.String,
			IsActive:   langIsActive.Bool,
			IsDefault:  langIsDefault.Bool,
		}
	}

	return &content, nil
}

// GetContentsByInteraction gets all contents for an interaction
func (r *interactionRepository) GetContentsByInteraction(ctx context.Context, interactionID uuid.UUID) ([]*models.InteractionContent, error) {
	query := `
		SELECT 
			ic.id, ic.interaction_id, ic.language_id, ic.instruction, ic.correction, ic.question, 
			ic.instruction_video_url, ic.correction_video_url, ic.additional_media_url, 
			ic.created_at, ic.updated_at,
			l.id, l.code, l.name, l.native_name, l.is_active, l.is_default
		FROM interaction_contents ic
		LEFT JOIN languages l ON ic.language_id = l.id
		WHERE ic.interaction_id = $1
		ORDER BY l.is_default DESC, l.code ASC
	`

	rows, err := r.db.Query(ctx, query, interactionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get interaction contents: %w", err)
	}
	defer rows.Close()

	var contents []*models.InteractionContent
	for rows.Next() {
		var content models.InteractionContent
		var languageID sql.NullInt32
		var languageCode sql.NullString
		var languageName sql.NullString
		var languageNativeName sql.NullString
		var languageIsActive sql.NullBool
		var languageIsDefault sql.NullBool

		err := rows.Scan(
			&content.ID,
			&content.InteractionID,
			&content.LanguageID,
			&content.Instruction,
			&content.Correction,
			&content.Question,
			&content.InstructionVideoURL,
			&content.CorrectionVideoURL,
			&content.AdditionalMediaURL,
			&content.CreatedAt,
			&content.UpdatedAt,
			&languageID,
			&languageCode,
			&languageName,
			&languageNativeName,
			&languageIsActive,
			&languageIsDefault,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan interaction content: %w", err)
		}

		// Set language if available
		if languageID.Valid {
			content.Language = &i18nModels.Language{
				ID:         int(languageID.Int32),
				Code:       languageCode.String,
				Name:       languageName.String,
				NativeName: languageNativeName.String,
				IsActive:   languageIsActive.Bool,
				IsDefault:  languageIsDefault.Bool,
			}
		}

		contents = append(contents, &content)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating interaction contents: %w", err)
	}

	return contents, nil
}

// UpdateContent updates content for an interaction
func (r *interactionRepository) UpdateContent(ctx context.Context, content *models.InteractionContent) error {
	query := `
		UPDATE interaction_contents
		SET 
			instruction = $3, 
			correction = $4, 
			question = $5, 
			instruction_video_url = $6, 
			correction_video_url = $7, 
			additional_media_url = $8, 
			updated_at = $9
		WHERE interaction_id = $1 AND language_id = $2
	`

	content.UpdatedAt = time.Now()

	result, err := r.db.Exec(
		ctx,
		query,
		content.InteractionID,
		content.LanguageID,
		content.Instruction,
		content.Correction,
		content.Question,
		content.InstructionVideoURL,
		content.CorrectionVideoURL,
		content.AdditionalMediaURL,
		content.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update interaction content: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("interaction content not found for interaction %s and language %d", content.InteractionID, content.LanguageID)
	}

	r.logger.Info().
		Str("interaction_id", content.InteractionID.String()).
		Int("language_id", content.LanguageID).
		Msg("Interaction content updated successfully")
	return nil
}

// DeleteContent deletes content for an interaction and language
func (r *interactionRepository) DeleteContent(ctx context.Context, interactionID uuid.UUID, languageID int) error {
	query := `DELETE FROM interaction_contents WHERE interaction_id = $1 AND language_id = $2`

	result, err := r.db.Exec(ctx, query, interactionID, languageID)
	if err != nil {
		return fmt.Errorf("failed to delete interaction content: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("interaction content not found for interaction %s and language %d", interactionID, languageID)
	}

	r.logger.Info().
		Str("interaction_id", interactionID.String()).
		Int("language_id", languageID).
		Msg("Interaction content deleted successfully")
	return nil
}

// AddBehaviour adds a behaviour to an interaction
func (r *interactionRepository) AddBehaviour(ctx context.Context, interactionID, behaviourID uuid.UUID) error {
	query := `
		INSERT INTO interaction_behaviours (id, interaction_id, behaviour_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
		ON CONFLICT (interaction_id, behaviour_id) DO NOTHING
	`

	now := time.Now()
	_, err := r.db.Exec(ctx, query, uuid.New(), interactionID, behaviourID, now, now)
	if err != nil {
		r.logger.Error().Err(err).
			Str("interaction_id", interactionID.String()).
			Str("behaviour_id", behaviourID.String()).
			Msg("Failed to add behaviour to interaction")
		return fmt.Errorf("failed to add behaviour to interaction: %w", err)
	}

	r.logger.Info().
		Str("interaction_id", interactionID.String()).
		Str("behaviour_id", behaviourID.String()).
		Msg("Behaviour added to interaction successfully")
	return nil
}

// RemoveBehaviour removes a behaviour from an interaction
func (r *interactionRepository) RemoveBehaviour(ctx context.Context, interactionID, behaviourID uuid.UUID) error {
	query := `DELETE FROM interaction_behaviours WHERE interaction_id = $1 AND behaviour_id = $2`

	result, err := r.db.Exec(ctx, query, interactionID, behaviourID)
	if err != nil {
		return fmt.Errorf("failed to remove behaviour from interaction: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("behaviour relationship not found between interaction %s and behaviour %s", interactionID, behaviourID)
	}

	r.logger.Info().
		Str("interaction_id", interactionID.String()).
		Str("behaviour_id", behaviourID.String()).
		Msg("Behaviour removed from interaction successfully")
	return nil
}

// GetBehaviours gets all behaviour IDs for an interaction
func (r *interactionRepository) GetBehaviours(ctx context.Context, interactionID uuid.UUID) ([]uuid.UUID, error) {
	query := `
		SELECT behaviour_id 
		FROM interaction_behaviours 
		WHERE interaction_id = $1
		ORDER BY created_at ASC
	`

	rows, err := r.db.Query(ctx, query, interactionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get interaction behaviours: %w", err)
	}
	defer rows.Close()

	var behaviourIDs []uuid.UUID
	for rows.Next() {
		var behaviourID uuid.UUID
		err := rows.Scan(&behaviourID)
		if err != nil {
			return nil, fmt.Errorf("failed to scan behaviour ID: %w", err)
		}
		behaviourIDs = append(behaviourIDs, behaviourID)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating behaviour IDs: %w", err)
	}

	return behaviourIDs, nil
}

// SetBehaviours sets the behaviours for an interaction (replaces all existing)
func (r *interactionRepository) SetBehaviours(ctx context.Context, interactionID uuid.UUID, behaviourIDs []uuid.UUID) error {
	// Start transaction
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Delete existing behaviours
	deleteQuery := `DELETE FROM interaction_behaviours WHERE interaction_id = $1`
	_, err = tx.Exec(ctx, deleteQuery, interactionID)
	if err != nil {
		r.logger.Error().Err(err).
			Str("interaction_id", interactionID.String()).
			Msg("Failed to delete existing behaviours")
		return fmt.Errorf("failed to delete existing behaviours: %w", err)
	}

	// Insert new behaviours
	if len(behaviourIDs) > 0 {
		insertQuery := `
			INSERT INTO interaction_behaviours (id, interaction_id, behaviour_id, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5)
		`
		now := time.Now()

		for _, behaviourID := range behaviourIDs {
			_, err = tx.Exec(ctx, insertQuery, uuid.New(), interactionID, behaviourID, now, now)
			if err != nil {
				r.logger.Error().Err(err).
					Str("interaction_id", interactionID.String()).
					Str("behaviour_id", behaviourID.String()).
					Msg("Failed to insert behaviour")
				return fmt.Errorf("failed to insert behaviour %s: %w", behaviourID, err)
			}
		}
	}

	// Commit transaction
	err = tx.Commit(ctx)
	if err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	r.logger.Info().
		Str("interaction_id", interactionID.String()).
		Int("behaviour_count", len(behaviourIDs)).
		Msg("Interaction behaviours set successfully")
	return nil
}

// GetWithContents gets an interaction with its content
func (r *interactionRepository) GetWithContents(ctx context.Context, id uuid.UUID, languageID *int) (*models.Interaction, error) {
	// Get the base interaction
	interaction, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Get contents
	var contents []*models.InteractionContent
	if languageID != nil {
		// Get content for specific language
		content, err := r.GetContentByInteractionAndLanguage(ctx, id, *languageID)
		if err != nil && !strings.Contains(err.Error(), "not found") {
			return nil, err
		}
		if content != nil {
			contents = []*models.InteractionContent{content}
		}
	} else {
		// Get all contents
		contents, err = r.GetContentsByInteraction(ctx, id)
		if err != nil {
			return nil, err
		}
	}

	// Convert to slice of values for the model
	interaction.Contents = make([]models.InteractionContent, len(contents))
	for i, content := range contents {
		interaction.Contents[i] = *content
	}

	return interaction, nil
}

// GetByScenario gets all interactions for a scenario
func (r *interactionRepository) GetByScenario(ctx context.Context, scenarioID uuid.UUID, languageID *int) ([]*models.Interaction, error) {
	query := `
		SELECT id, code, character_id, scenario_id, is_active, created_by, created_at, updated_at
		FROM interactions
		WHERE scenario_id = $1 AND is_active = true
		ORDER BY created_at ASC
	`

	rows, err := r.db.Query(ctx, query, scenarioID)
	if err != nil {
		return nil, fmt.Errorf("failed to get interactions by scenario: %w", err)
	}
	defer rows.Close()

	var interactions []*models.Interaction
	for rows.Next() {
		var interaction models.Interaction
		err := rows.Scan(
			&interaction.ID,
			&interaction.Code,
			&interaction.CharacterID,
			&interaction.ScenarioID,
			&interaction.IsActive,
			&interaction.CreatedBy,
			&interaction.CreatedAt,
			&interaction.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan interaction: %w", err)
		}

		// Load contents for each interaction
		var contents []*models.InteractionContent
		if languageID != nil {
			content, err := r.GetContentByInteractionAndLanguage(ctx, interaction.ID, *languageID)
			if err != nil && !strings.Contains(err.Error(), "not found") {
				return nil, err
			}
			if content != nil {
				contents = []*models.InteractionContent{content}
			}
		} else {
			contents, err = r.GetContentsByInteraction(ctx, interaction.ID)
			if err != nil {
				return nil, err
			}
		}

		interaction.Contents = make([]models.InteractionContent, len(contents))
		for i, content := range contents {
			interaction.Contents[i] = *content
		}

		interactions = append(interactions, &interaction)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating interactions: %w", err)
	}

	return interactions, nil
}

// GetByCharacter gets all interactions for a character
func (r *interactionRepository) GetByCharacter(ctx context.Context, characterID uuid.UUID, languageID *int) ([]*models.Interaction, error) {
	query := `
		SELECT id, code, character_id, scenario_id, is_active, created_by, created_at, updated_at
		FROM interactions
		WHERE character_id = $1 AND is_active = true
		ORDER BY created_at ASC
	`

	rows, err := r.db.Query(ctx, query, characterID)
	if err != nil {
		return nil, fmt.Errorf("failed to get interactions by character: %w", err)
	}
	defer rows.Close()

	var interactions []*models.Interaction
	for rows.Next() {
		var interaction models.Interaction
		err := rows.Scan(
			&interaction.ID,
			&interaction.Code,
			&interaction.CharacterID,
			&interaction.ScenarioID,
			&interaction.IsActive,
			&interaction.CreatedBy,
			&interaction.CreatedAt,
			&interaction.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan interaction: %w", err)
		}

		// Load contents for each interaction
		var contents []*models.InteractionContent
		if languageID != nil {
			content, err := r.GetContentByInteractionAndLanguage(ctx, interaction.ID, *languageID)
			if err != nil && !strings.Contains(err.Error(), "not found") {
				return nil, err
			}
			if content != nil {
				contents = []*models.InteractionContent{content}
			}
		} else {
			contents, err = r.GetContentsByInteraction(ctx, interaction.ID)
			if err != nil {
				return nil, err
			}
		}

		interaction.Contents = make([]models.InteractionContent, len(contents))
		for i, content := range contents {
			interaction.Contents[i] = *content
		}

		interactions = append(interactions, &interaction)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating interactions: %w", err)
	}

	return interactions, nil
}

// Search searches interactions by query string
func (r *interactionRepository) Search(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.Interaction, error) {
	searchQuery := `
		SELECT DISTINCT i.id, i.code, i.character_id, i.scenario_id, i.is_active, i.created_by, i.created_at, i.updated_at
		FROM interactions i
		LEFT JOIN interaction_contents ic ON i.id = ic.interaction_id
		WHERE i.is_active = true AND (
			i.code ILIKE $1 OR 
			ic.instruction ILIKE $1 OR 
			ic.correction ILIKE $1 OR 
			ic.question ILIKE $1
		)
	`

	args := []interface{}{"%" + query + "%"}
	argIndex := 2

	if languageID != nil {
		searchQuery += " AND ic.language_id = $" + fmt.Sprintf("%d", argIndex)
		args = append(args, *languageID)
		argIndex++
	}

	searchQuery += " ORDER BY i.created_at DESC"

	if limit > 0 {
		searchQuery += " LIMIT $" + fmt.Sprintf("%d", argIndex)
		args = append(args, limit)
		argIndex++
	}

	if offset > 0 {
		searchQuery += " OFFSET $" + fmt.Sprintf("%d", argIndex)
		args = append(args, offset)
	}

	rows, err := r.db.Query(ctx, searchQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to search interactions: %w", err)
	}
	defer rows.Close()

	var interactions []*models.Interaction
	for rows.Next() {
		var interaction models.Interaction
		err := rows.Scan(
			&interaction.ID,
			&interaction.Code,
			&interaction.CharacterID,
			&interaction.ScenarioID,
			&interaction.IsActive,
			&interaction.CreatedBy,
			&interaction.CreatedAt,
			&interaction.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan interaction: %w", err)
		}

		// Load contents for each interaction
		var contents []*models.InteractionContent
		if languageID != nil {
			content, err := r.GetContentByInteractionAndLanguage(ctx, interaction.ID, *languageID)
			if err != nil && !strings.Contains(err.Error(), "not found") {
				return nil, err
			}
			if content != nil {
				contents = []*models.InteractionContent{content}
			}
		} else {
			contents, err = r.GetContentsByInteraction(ctx, interaction.ID)
			if err != nil {
				return nil, err
			}
		}

		interaction.Contents = make([]models.InteractionContent, len(contents))
		for i, content := range contents {
			interaction.Contents[i] = *content
		}

		interactions = append(interactions, &interaction)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating interactions: %w", err)
	}

	return interactions, nil
}

// Count returns the total number of interactions, optionally filtering by active status.
func (r *interactionRepository) Count(ctx context.Context, activeOnly bool) (int64, error) {
	query := "SELECT COUNT(*) FROM interactions"
	args := []interface{}{}
	if activeOnly {
		query += " WHERE is_active = $1"
		args = append(args, true)
	}
	var count int64
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count interactions: %w", err)
	}
	return count, nil
}

// ExistsByCode checks if an interaction exists by its code, optionally filtering by scenario ID.
func (r *interactionRepository) ExistsByCode(ctx context.Context, code string, scenarioID *uuid.UUID) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM interactions WHERE code = $1`
	args := []interface{}{code}
	if scenarioID != nil {
		query += " AND scenario_id = $2"
		args = append(args, *scenarioID)
	}
	query += ")"
	var exists bool
	err := r.db.QueryRow(ctx, query, args...).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check existence by code: %w", err)
	}
	return exists, nil
}
