package repository

import (
	"context"

	"github.com/google/uuid"
	"github.com/psynarios/admin_back/internal/library/interaction/models"
)

type InteractionRepository interface {
	// Core CRUD operations
	Create(ctx context.Context, interaction *models.Interaction) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Interaction, error)
	GetByCode(ctx context.Context, code string) (*models.Interaction, error)
	GetAll(ctx context.Context, limit, offset int, activeOnly bool) ([]*models.Interaction, error)
	Update(ctx context.Context, interaction *models.Interaction) error
	Delete(ctx context.Context, id uuid.UUID) error

	// Content operations
	CreateContent(ctx context.Context, content *models.InteractionContent) error
	GetContentByInteractionAndLanguage(ctx context.Context, interactionID uuid.UUID, languageID int) (*models.InteractionContent, error)
	GetContentsByInteraction(ctx context.Context, interactionID uuid.UUID) ([]*models.InteractionContent, error)
	UpdateContent(ctx context.Context, content *models.InteractionContent) error
	DeleteContent(ctx context.Context, interactionID uuid.UUID, languageID int) error

	// Behaviour relationship operations
	AddBehaviour(ctx context.Context, interactionID, behaviourID uuid.UUID) error
	RemoveBehaviour(ctx context.Context, interactionID, behaviourID uuid.UUID) error
	GetBehaviours(ctx context.Context, interactionID uuid.UUID) ([]uuid.UUID, error)
	SetBehaviours(ctx context.Context, interactionID uuid.UUID, behaviourIDs []uuid.UUID) error

	// Advanced queries
	GetWithContents(ctx context.Context, id uuid.UUID, languageID *int) (*models.Interaction, error)
	GetByScenario(ctx context.Context, scenarioID uuid.UUID, languageID *int) ([]*models.Interaction, error)
	GetByCharacter(ctx context.Context, characterID uuid.UUID, languageID *int) ([]*models.Interaction, error)
	Search(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.Interaction, error)

	// Utility methods
	ExistsByCode(ctx context.Context, code string, excludeID *uuid.UUID) (bool, error)
	Count(ctx context.Context, activeOnly bool) (int64, error)
}
