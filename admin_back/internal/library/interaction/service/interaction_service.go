package service

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"github.com/psynarios/admin_back/internal/common/errors"
	"github.com/psynarios/admin_back/internal/library/interaction/models"
	"github.com/psynarios/admin_back/internal/library/interaction/repository"
)

type InteractionService interface {
	// Core CRUD operations
	CreateInteraction(ctx context.Context, req *models.InteractionCreateRequest, createdBy *uuid.UUID) (*models.InteractionResponse, error)
	GetInteraction(ctx context.Context, id uuid.UUID, languageID *int) (*models.InteractionResponse, error)
	GetInteractionByCode(ctx context.Context, code string, languageID *int) (*models.InteractionResponse, error)
	GetInteractions(ctx context.Context, limit, offset int, languageID *int, activeOnly bool) ([]*models.InteractionResponse, error)
	UpdateInteraction(ctx context.Context, id uuid.UUID, req *models.InteractionUpdateRequest) (*models.InteractionResponse, error)
	DeleteInteraction(ctx context.Context, id uuid.UUID) error

	// Content operations
	CreateInteractionContent(ctx context.Context, interactionID uuid.UUID, req *models.InteractionContentRequest) (*models.InteractionContentResponse, error)
	UpdateInteractionContent(ctx context.Context, interactionID uuid.UUID, languageID int, req *models.InteractionContentRequest) (*models.InteractionContentResponse, error)
	DeleteInteractionContent(ctx context.Context, interactionID uuid.UUID, languageID int) error

	// Behaviour operations
	AddBehaviourToInteraction(ctx context.Context, interactionID, behaviourID uuid.UUID) error
	RemoveBehaviourFromInteraction(ctx context.Context, interactionID, behaviourID uuid.UUID) error
	SetInteractionBehaviours(ctx context.Context, interactionID uuid.UUID, behaviourIDs []uuid.UUID) error

	// Advanced operations
	GetInteractionsByScenario(ctx context.Context, scenarioID uuid.UUID, languageID *int) ([]*models.InteractionResponse, error)
	GetInteractionsByCharacter(ctx context.Context, characterID uuid.UUID, languageID *int) ([]*models.InteractionResponse, error)
	SearchInteractions(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.InteractionResponse, error)
	GetInteractionStatistics(ctx context.Context) (*InteractionStatistics, error)
}

type InteractionStatistics struct {
	TotalInteractions  int64 `json:"total_interactions"`
	ActiveInteractions int64 `json:"active_interactions"`
}

type interactionService struct {
	repo repository.InteractionRepository
}

func NewInteractionService(repo repository.InteractionRepository) InteractionService {
	return &interactionService{repo: repo}
}

func (s *interactionService) CreateInteraction(ctx context.Context, req *models.InteractionCreateRequest, createdBy *uuid.UUID) (*models.InteractionResponse, error) {
	// Check if code already exists
	exists, err := s.repo.ExistsByCode(ctx, req.Code, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to check code existence: %w", err)
	}
	if exists {
		return nil, errors.NewConflictError("interaction with this code already exists", nil)
	}

	// Create interaction entity
	interaction := &models.Interaction{
		ID:          uuid.New(),
		Code:        req.Code,
		CharacterID: req.CharacterID,
		ScenarioID:  req.ScenarioID,
		IsActive:    true,
		CreatedBy:   createdBy,
	}

	err = s.repo.Create(ctx, interaction)
	if err != nil {
		return nil, fmt.Errorf("failed to create interaction: %w", err)
	}

	// Create content for each language
	for _, contentReq := range req.Contents {
		content := &models.InteractionContent{
			ID:                  uuid.New(),
			InteractionID:       interaction.ID,
			LanguageID:          contentReq.LanguageID,
			Instruction:         contentReq.Instruction,
			Correction:          contentReq.Correction,
			Question:            contentReq.Question,
			InstructionVideoURL: contentReq.InstructionVideoURL,
			CorrectionVideoURL:  contentReq.CorrectionVideoURL,
			AdditionalMediaURL:  contentReq.AdditionalMediaURL,
		}

		err = s.repo.CreateContent(ctx, content)
		if err != nil {
			return nil, fmt.Errorf("failed to create interaction content: %w", err)
		}
	}

	// Set behaviours if provided
	if len(req.BehaviourIDs) > 0 {
		err = s.repo.SetBehaviours(ctx, interaction.ID, req.BehaviourIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to set interaction behaviours: %w", err)
		}
	}

	return s.GetInteraction(ctx, interaction.ID, nil)
}

func (s *interactionService) GetInteraction(ctx context.Context, id uuid.UUID, languageID *int) (*models.InteractionResponse, error) {
	interaction, err := s.repo.GetWithContents(ctx, id, languageID)
	if err != nil {
		return nil, err
	}

	return s.toResponse(interaction), nil
}

func (s *interactionService) GetInteractionByCode(ctx context.Context, code string, languageID *int) (*models.InteractionResponse, error) {
	interaction, err := s.repo.GetByCode(ctx, code)
	if err != nil {
		return nil, err
	}

	return s.GetInteraction(ctx, interaction.ID, languageID)
}

func (s *interactionService) GetInteractions(ctx context.Context, limit, offset int, languageID *int, activeOnly bool) ([]*models.InteractionResponse, error) {
	interactions, err := s.repo.GetAll(ctx, limit, offset, activeOnly)
	if err != nil {
		return nil, err
	}

	// Load contents for each interaction
	responses := make([]*models.InteractionResponse, len(interactions))
	for i, interaction := range interactions {
		interactionWithContents, err := s.repo.GetWithContents(ctx, interaction.ID, languageID)
		if err != nil {
			return nil, err
		}
		responses[i] = s.toResponse(interactionWithContents)
	}

	return responses, nil
}

func (s *interactionService) UpdateInteraction(ctx context.Context, id uuid.UUID, req *models.InteractionUpdateRequest) (*models.InteractionResponse, error) {
	interaction, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Update core fields if provided
	if req.Code != nil {
		exists, err := s.repo.ExistsByCode(ctx, *req.Code, &id)
		if err != nil {
			return nil, fmt.Errorf("failed to check code existence: %w", err)
		}
		if exists {
			return nil, errors.NewConflictError("interaction with this code already exists", nil)
		}
		interaction.Code = *req.Code
	}

	if req.CharacterID != nil {
		interaction.CharacterID = *req.CharacterID
	}

	if req.ScenarioID != nil {
		interaction.ScenarioID = *req.ScenarioID
	}

	if req.IsActive != nil {
		interaction.IsActive = *req.IsActive
	}

	err = s.repo.Update(ctx, interaction)
	if err != nil {
		return nil, fmt.Errorf("failed to update interaction: %w", err)
	}

	// Update contents if provided
	if req.Contents != nil {
		for _, contentReq := range req.Contents {
			existingContent, err := s.repo.GetContentByInteractionAndLanguage(ctx, id, contentReq.LanguageID)
			if err != nil && !errors.IsNotFoundError(err) {
				return nil, err
			}

			if existingContent != nil {
				// Update existing content
				existingContent.Instruction = contentReq.Instruction
				existingContent.Correction = contentReq.Correction
				existingContent.Question = contentReq.Question
				existingContent.InstructionVideoURL = contentReq.InstructionVideoURL
				existingContent.CorrectionVideoURL = contentReq.CorrectionVideoURL
				existingContent.AdditionalMediaURL = contentReq.AdditionalMediaURL
				err = s.repo.UpdateContent(ctx, existingContent)
				if err != nil {
					return nil, fmt.Errorf("failed to update interaction content: %w", err)
				}
			} else {
				// Create new content
				content := &models.InteractionContent{
					ID:                  uuid.New(),
					InteractionID:       id,
					LanguageID:          contentReq.LanguageID,
					Instruction:         contentReq.Instruction,
					Correction:          contentReq.Correction,
					Question:            contentReq.Question,
					InstructionVideoURL: contentReq.InstructionVideoURL,
					CorrectionVideoURL:  contentReq.CorrectionVideoURL,
					AdditionalMediaURL:  contentReq.AdditionalMediaURL,
				}
				err = s.repo.CreateContent(ctx, content)
				if err != nil {
					return nil, fmt.Errorf("failed to create interaction content: %w", err)
				}
			}
		}
	}

	// Update behaviours if provided
	if req.BehaviourIDs != nil {
		err = s.repo.SetBehaviours(ctx, id, req.BehaviourIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to update interaction behaviours: %w", err)
		}
	}

	return s.GetInteraction(ctx, id, nil)
}

func (s *interactionService) DeleteInteraction(ctx context.Context, id uuid.UUID) error {
	return s.repo.Delete(ctx, id)
}

func (s *interactionService) CreateInteractionContent(ctx context.Context, interactionID uuid.UUID, req *models.InteractionContentRequest) (*models.InteractionContentResponse, error) {
	// Verify interaction exists
	_, err := s.repo.GetByID(ctx, interactionID)
	if err != nil {
		return nil, err
	}

	content := &models.InteractionContent{
		ID:                  uuid.New(),
		InteractionID:       interactionID,
		LanguageID:          req.LanguageID,
		Instruction:         req.Instruction,
		Correction:          req.Correction,
		Question:            req.Question,
		InstructionVideoURL: req.InstructionVideoURL,
		CorrectionVideoURL:  req.CorrectionVideoURL,
		AdditionalMediaURL:  req.AdditionalMediaURL,
	}

	err = s.repo.CreateContent(ctx, content)
	if err != nil {
		return nil, fmt.Errorf("failed to create interaction content: %w", err)
	}

	// Get created content with language info
	createdContent, err := s.repo.GetContentByInteractionAndLanguage(ctx, interactionID, req.LanguageID)
	if err != nil {
		return nil, err
	}

	return s.toContentResponse(createdContent), nil
}

func (s *interactionService) UpdateInteractionContent(ctx context.Context, interactionID uuid.UUID, languageID int, req *models.InteractionContentRequest) (*models.InteractionContentResponse, error) {
	// Get existing content
	content, err := s.repo.GetContentByInteractionAndLanguage(ctx, interactionID, languageID)
	if err != nil {
		return nil, err
	}

	// Update fields
	content.Instruction = req.Instruction
	content.Correction = req.Correction
	content.Question = req.Question
	content.InstructionVideoURL = req.InstructionVideoURL
	content.CorrectionVideoURL = req.CorrectionVideoURL
	content.AdditionalMediaURL = req.AdditionalMediaURL

	err = s.repo.UpdateContent(ctx, content)
	if err != nil {
		return nil, fmt.Errorf("failed to update interaction content: %w", err)
	}

	// Get updated content with language info
	updatedContent, err := s.repo.GetContentByInteractionAndLanguage(ctx, interactionID, languageID)
	if err != nil {
		return nil, err
	}

	return s.toContentResponse(updatedContent), nil
}

func (s *interactionService) DeleteInteractionContent(ctx context.Context, interactionID uuid.UUID, languageID int) error {
	return s.repo.DeleteContent(ctx, interactionID, languageID)
}

func (s *interactionService) AddBehaviourToInteraction(ctx context.Context, interactionID, behaviourID uuid.UUID) error {
	return s.repo.AddBehaviour(ctx, interactionID, behaviourID)
}

func (s *interactionService) RemoveBehaviourFromInteraction(ctx context.Context, interactionID, behaviourID uuid.UUID) error {
	return s.repo.RemoveBehaviour(ctx, interactionID, behaviourID)
}

func (s *interactionService) SetInteractionBehaviours(ctx context.Context, interactionID uuid.UUID, behaviourIDs []uuid.UUID) error {
	return s.repo.SetBehaviours(ctx, interactionID, behaviourIDs)
}

func (s *interactionService) GetInteractionsByScenario(ctx context.Context, scenarioID uuid.UUID, languageID *int) ([]*models.InteractionResponse, error) {
	interactions, err := s.repo.GetByScenario(ctx, scenarioID, languageID)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.InteractionResponse, len(interactions))
	for i, interaction := range interactions {
		responses[i] = s.toResponse(interaction)
	}

	return responses, nil
}

func (s *interactionService) GetInteractionsByCharacter(ctx context.Context, characterID uuid.UUID, languageID *int) ([]*models.InteractionResponse, error) {
	interactions, err := s.repo.GetByCharacter(ctx, characterID, languageID)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.InteractionResponse, len(interactions))
	for i, interaction := range interactions {
		responses[i] = s.toResponse(interaction)
	}

	return responses, nil
}

func (s *interactionService) SearchInteractions(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.InteractionResponse, error) {
	interactions, err := s.repo.Search(ctx, query, languageID, limit, offset)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.InteractionResponse, len(interactions))
	for i, interaction := range interactions {
		responses[i] = s.toResponse(interaction)
	}

	return responses, nil
}

func (s *interactionService) GetInteractionStatistics(ctx context.Context) (*InteractionStatistics, error) {
	totalCount, err := s.repo.Count(ctx, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get total interaction count: %w", err)
	}

	activeCount, err := s.repo.Count(ctx, true)
	if err != nil {
		return nil, fmt.Errorf("failed to get active interaction count: %w", err)
	}

	return &InteractionStatistics{
		TotalInteractions:  totalCount,
		ActiveInteractions: activeCount,
	}, nil
}

// Helper methods
func (s *interactionService) toResponse(interaction *models.Interaction) *models.InteractionResponse {
	response := &models.InteractionResponse{
		ID:          interaction.ID,
		Code:        interaction.Code,
		CharacterID: interaction.CharacterID,
		ScenarioID:  interaction.ScenarioID,
		IsActive:    interaction.IsActive,
		CreatedBy:   interaction.CreatedBy,
		CreatedAt:   interaction.CreatedAt,
		UpdatedAt:   interaction.UpdatedAt,
	}

	if len(interaction.Contents) > 0 {
		response.Contents = make([]models.InteractionContentResponse, len(interaction.Contents))
		for i, content := range interaction.Contents {
			response.Contents[i] = models.InteractionContentResponse{
				ID:                  content.ID,
				LanguageID:          content.LanguageID,
				Instruction:         content.Instruction,
				Correction:          content.Correction,
				Question:            content.Question,
				InstructionVideoURL: content.InstructionVideoURL,
				CorrectionVideoURL:  content.CorrectionVideoURL,
				AdditionalMediaURL:  content.AdditionalMediaURL,
				Language:            content.Language,
				CreatedAt:           content.CreatedAt,
				UpdatedAt:           content.UpdatedAt,
			}
		}
	}

	return response
}

func (s *interactionService) toContentResponse(content *models.InteractionContent) *models.InteractionContentResponse {
	return &models.InteractionContentResponse{
		ID:                  content.ID,
		LanguageID:          content.LanguageID,
		Instruction:         content.Instruction,
		Correction:          content.Correction,
		Question:            content.Question,
		InstructionVideoURL: content.InstructionVideoURL,
		CorrectionVideoURL:  content.CorrectionVideoURL,
		AdditionalMediaURL:  content.AdditionalMediaURL,
		Language:            content.Language,
		CreatedAt:           content.CreatedAt,
		UpdatedAt:           content.UpdatedAt,
	}
}
