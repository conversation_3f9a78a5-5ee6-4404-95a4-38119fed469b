package models

import (
	"time"

	"github.com/google/uuid"
	i18nModels "github.com/psynarios/admin_back/internal/i18n/models"
	behaviourModels "github.com/psynarios/admin_back/internal/library/behaviour/models"
	characterModels "github.com/psynarios/admin_back/internal/library/character/models"
)

// Core interaction entity (language-agnostic)
type Interaction struct {
	ID          uuid.UUID  `json:"id" db:"id"`
	Code        string     `json:"code" db:"code" validate:"required,max=50"`
	CharacterID uuid.UUID  `json:"character_id" db:"character_id"`
	ScenarioID  uuid.UUID  `json:"scenario_id" db:"scenario_id"`
	IsActive    bool       `json:"is_active" db:"is_active"`
	CreatedBy   *uuid.UUID `json:"created_by,omitempty" db:"created_by"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at" db:"updated_at"`

	// Relations - loaded based on context
	Contents   []InteractionContent        `json:"contents,omitempty"`
	Character  *characterModels.Character  `json:"character,omitempty"`
	Behaviours []behaviourModels.Behaviour `json:"behaviours,omitempty"`
}

// Language-specific content for interactions
type InteractionContent struct {
	ID                  uuid.UUID `json:"id" db:"id"`
	InteractionID       uuid.UUID `json:"interaction_id" db:"interaction_id"`
	LanguageID          int       `json:"language_id" db:"language_id"`
	Instruction         *string   `json:"instruction,omitempty" db:"instruction"`
	Correction          *string   `json:"correction,omitempty" db:"correction"`
	Question            *string   `json:"question,omitempty" db:"question"`
	InstructionVideoURL *string   `json:"instruction_video_url,omitempty" db:"instruction_video_url"`
	CorrectionVideoURL  *string   `json:"correction_video_url,omitempty" db:"correction_video_url"`
	AdditionalMediaURL  *string   `json:"additional_media_url,omitempty" db:"additional_media_url"`
	CreatedAt           time.Time `json:"created_at" db:"created_at"`
	UpdatedAt           time.Time `json:"updated_at" db:"updated_at"`

	// Relations
	Language *i18nModels.Language `json:"language,omitempty"`
}

// Junction table for many-to-many relationship
type InteractionBehaviour struct {
	ID            uuid.UUID `json:"id" db:"id"`
	InteractionID uuid.UUID `json:"interaction_id" db:"interaction_id"`
	BehaviourID   uuid.UUID `json:"behaviour_id" db:"behaviour_id"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time `json:"updated_at" db:"updated_at"`
}

// Request DTOs
type InteractionCreateRequest struct {
	Code         string                      `json:"code" binding:"required,max=50"`
	CharacterID  uuid.UUID                   `json:"character_id" binding:"required"`
	ScenarioID   uuid.UUID                   `json:"scenario_id" binding:"required"`
	Contents     []InteractionContentRequest `json:"contents" binding:"required,min=1,dive"`
	BehaviourIDs []uuid.UUID                 `json:"behaviour_ids,omitempty"`
}

type InteractionUpdateRequest struct {
	Code         *string                     `json:"code,omitempty" binding:"omitempty,max=50"`
	CharacterID  *uuid.UUID                  `json:"character_id,omitempty"`
	ScenarioID   *uuid.UUID                  `json:"scenario_id,omitempty"`
	IsActive     *bool                       `json:"is_active,omitempty"`
	Contents     []InteractionContentRequest `json:"contents,omitempty" binding:"omitempty,dive"`
	BehaviourIDs []uuid.UUID                 `json:"behaviour_ids,omitempty"`
}

type InteractionContentRequest struct {
	LanguageID          int     `json:"language_id" binding:"required"`
	Instruction         *string `json:"instruction,omitempty"`
	Correction          *string `json:"correction,omitempty"`
	Question            *string `json:"question,omitempty"`
	InstructionVideoURL *string `json:"instruction_video_url,omitempty"`
	CorrectionVideoURL  *string `json:"correction_video_url,omitempty"`
	AdditionalMediaURL  *string `json:"additional_media_url,omitempty"`
}

// Response DTOs
type InteractionResponse struct {
	ID          uuid.UUID                           `json:"id"`
	Code        string                              `json:"code"`
	CharacterID uuid.UUID                           `json:"character_id"`
	ScenarioID  uuid.UUID                           `json:"scenario_id"`
	IsActive    bool                                `json:"is_active"`
	CreatedBy   *uuid.UUID                          `json:"created_by,omitempty"`
	CreatedAt   time.Time                           `json:"created_at"`
	UpdatedAt   time.Time                           `json:"updated_at"`
	Contents    []InteractionContentResponse        `json:"contents,omitempty"`
	Character   *characterModels.CharacterResponse  `json:"character,omitempty"`
	Behaviours  []behaviourModels.BehaviourResponse `json:"behaviours,omitempty"`
}

type InteractionContentResponse struct {
	ID                  uuid.UUID            `json:"id"`
	LanguageID          int                  `json:"language_id"`
	Instruction         *string              `json:"instruction,omitempty"`
	Correction          *string              `json:"correction,omitempty"`
	Question            *string              `json:"question,omitempty"`
	InstructionVideoURL *string              `json:"instruction_video_url,omitempty"`
	CorrectionVideoURL  *string              `json:"correction_video_url,omitempty"`
	AdditionalMediaURL  *string              `json:"additional_media_url,omitempty"`
	Language            *i18nModels.Language `json:"language,omitempty"`
	CreatedAt           time.Time            `json:"created_at"`
	UpdatedAt           time.Time            `json:"updated_at"`
}
