package models

import (
	"time"

	"github.com/google/uuid"
	i18nModels "github.com/psynarios/admin_back/internal/i18n/models"
)

// Core behaviour entity (language-agnostic)
type Behaviour struct {
	ID        uuid.UUID  `json:"id" db:"id"`
	Code      string     `json:"code" db:"code" validate:"required,max=50"`
	IsActive  bool       `json:"is_active" db:"is_active"`
	CreatedBy *uuid.UUID `json:"created_by,omitempty" db:"created_by"`
	CreatedAt time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt time.Time  `json:"updated_at" db:"updated_at"`

	// Relations - loaded based on context
	Contents []BehaviourContent `json:"contents,omitempty"`
}

// Language-specific content for behaviours
type BehaviourContent struct {
	ID          uuid.UUID `json:"id" db:"id"`
	BehaviourID uuid.UUID `json:"behaviour_id" db:"behaviour_id"`
	LanguageID  int       `json:"language_id" db:"language_id"`
	Title       string    `json:"title" db:"title" validate:"required,max=255"`
	Description *string   `json:"description,omitempty" db:"description"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`

	// Relations
	Language *i18nModels.Language `json:"language,omitempty"`
}

// Request DTOs for API endpoints
type BehaviourCreateRequest struct {
	Code     string                    `json:"code" binding:"required,max=50"`
	Contents []BehaviourContentRequest `json:"contents" binding:"required,min=1,dive"`
}

type BehaviourUpdateRequest struct {
	Code     *string                   `json:"code,omitempty" binding:"omitempty,max=50"`
	IsActive *bool                     `json:"is_active,omitempty"`
	Contents []BehaviourContentRequest `json:"contents,omitempty" binding:"omitempty,dive"`
}

type BehaviourContentRequest struct {
	LanguageID  int     `json:"language_id" binding:"required"`
	Title       string  `json:"title" binding:"required,max=255"`
	Description *string `json:"description,omitempty"`
}

// Response DTOs
type BehaviourResponse struct {
	ID        uuid.UUID                  `json:"id"`
	Code      string                     `json:"code"`
	IsActive  bool                       `json:"is_active"`
	CreatedBy *uuid.UUID                 `json:"created_by,omitempty"`
	CreatedAt time.Time                  `json:"created_at"`
	UpdatedAt time.Time                  `json:"updated_at"`
	Contents  []BehaviourContentResponse `json:"contents,omitempty"`
}

type BehaviourContentResponse struct {
	ID          uuid.UUID            `json:"id"`
	LanguageID  int                  `json:"language_id"`
	Title       string               `json:"title"`
	Description *string              `json:"description,omitempty"`
	Language    *i18nModels.Language `json:"language,omitempty"`
	CreatedAt   time.Time            `json:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at"`
}
