package repository

import (
	"context"

	"github.com/google/uuid"
	"github.com/psynarios/admin_back/internal/library/behaviour/models"
)

type BehaviourRepository interface {
	// Core CRUD operations
	Create(ctx context.Context, behaviour *models.Behaviour) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Behaviour, error)
	GetByCode(ctx context.Context, code string) (*models.Behaviour, error)
	GetAll(ctx context.Context, limit, offset int, activeOnly bool) ([]*models.Behaviour, error)
	Update(ctx context.Context, behaviour *models.Behaviour) error
	Delete(ctx context.Context, id uuid.UUID) error

	// Content operations
	CreateContent(ctx context.Context, content *models.BehaviourContent) error
	GetContentByBehaviourAndLanguage(ctx context.Context, behaviourID uuid.UUID, languageID int) (*models.BehaviourContent, error)
	GetContentsByBehaviour(ctx context.Context, behaviourID uuid.UUID) ([]*models.BehaviourContent, error)
	UpdateContent(ctx context.Context, content *models.BehaviourContent) error
	DeleteContent(ctx context.Context, behaviourID uuid.UUID, languageID int) error

	// Advanced queries
	GetWithContents(ctx context.Context, id uuid.UUID, languageID *int) (*models.Behaviour, error)
	GetActiveWithContents(ctx context.Context, limit, offset int, languageID *int) ([]*models.Behaviour, error)
	Search(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.Behaviour, error)

	// Utility methods
	ExistsByCode(ctx context.Context, code string, excludeID *uuid.UUID) (bool, error)
	Count(ctx context.Context, activeOnly bool) (int64, error)
}
