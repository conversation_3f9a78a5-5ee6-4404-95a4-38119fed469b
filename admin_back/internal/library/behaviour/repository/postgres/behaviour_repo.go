package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/library/behaviour/models"
	"github.com/psynarios/admin_back/internal/library/behaviour/repository"
)

type behaviourRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

func NewBehaviourRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.BehaviourRepository {
	return &behaviourRepository{
		db:     db,
		logger: logger.With().Str("component", "behaviour_repository").Logger(),
	}
}

func (r *behaviourRepository) Create(ctx context.Context, behaviour *models.Behaviour) error {
	now := time.Now()
	if behaviour.ID == uuid.Nil {
		behaviour.ID = uuid.New()
	}
	behaviour.CreatedAt = now
	behaviour.UpdatedAt = now

	query := `
        INSERT INTO behaviours (id, code, is_active, created_by, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6)
    `
	_, err := r.db.Exec(ctx, query,
		behaviour.ID,
		behaviour.Code,
		behaviour.IsActive,
		behaviour.CreatedBy,
		behaviour.CreatedAt,
		behaviour.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("failed to create behaviour: %w", err)
	}
	return nil
}

func (r *behaviourRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Behaviour, error) {
	query := `SELECT id, code, is_active, created_by, created_at, updated_at FROM behaviours WHERE id = $1`
	var behaviour models.Behaviour
	err := r.db.QueryRow(ctx, query, id).Scan(
		&behaviour.ID,
		&behaviour.Code,
		&behaviour.IsActive,
		&behaviour.CreatedBy,
		&behaviour.CreatedAt,
		&behaviour.UpdatedAt,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("behaviour not found")
		}
		return nil, fmt.Errorf("failed to get behaviour: %w", err)
	}
	return &behaviour, nil
}

func (r *behaviourRepository) GetByCode(ctx context.Context, code string) (*models.Behaviour, error) {
	query := `SELECT id, code, is_active, created_by, created_at, updated_at FROM behaviours WHERE code = $1`
	var behaviour models.Behaviour
	err := r.db.QueryRow(ctx, query, code).Scan(
		&behaviour.ID,
		&behaviour.Code,
		&behaviour.IsActive,
		&behaviour.CreatedBy,
		&behaviour.CreatedAt,
		&behaviour.UpdatedAt,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("behaviour not found")
		}
		return nil, fmt.Errorf("failed to get behaviour: %w", err)
	}
	return &behaviour, nil
}

func (r *behaviourRepository) GetAll(ctx context.Context, limit, offset int, activeOnly bool) ([]*models.Behaviour, error) {
	query := `SELECT id, code, is_active, created_by, created_at, updated_at FROM behaviours`
	args := []interface{}{}
	argIdx := 1

	if activeOnly {
		query += fmt.Sprintf(" WHERE is_active = $%d", argIdx)
		args = append(args, true)
		argIdx++
	}
	query += " ORDER BY created_at DESC"
	if limit > 0 {
		query += fmt.Sprintf(" LIMIT $%d", argIdx)
		args = append(args, limit)
		argIdx++
	}
	if offset > 0 {
		query += fmt.Sprintf(" OFFSET $%d", argIdx)
		args = append(args, offset)
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get behaviours: %w", err)
	}
	defer rows.Close()

	var behaviours []*models.Behaviour
	for rows.Next() {
		var b models.Behaviour
		err := rows.Scan(
			&b.ID,
			&b.Code,
			&b.IsActive,
			&b.CreatedBy,
			&b.CreatedAt,
			&b.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan behaviour: %w", err)
		}
		behaviours = append(behaviours, &b)
	}
	return behaviours, nil
}

func (r *behaviourRepository) Update(ctx context.Context, behaviour *models.Behaviour) error {
	behaviour.UpdatedAt = time.Now()
	query := `
        UPDATE behaviours 
        SET code = $1, is_active = $2, updated_at = $3
        WHERE id = $4
    `
	ct, err := r.db.Exec(ctx, query,
		behaviour.Code,
		behaviour.IsActive,
		behaviour.UpdatedAt,
		behaviour.ID,
	)
	if err != nil {
		return fmt.Errorf("failed to update behaviour: %w", err)
	}
	if ct.RowsAffected() == 0 {
		return fmt.Errorf("behaviour not found")
	}
	return nil
}

func (r *behaviourRepository) Delete(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM behaviours WHERE id = $1`
	ct, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete behaviour: %w", err)
	}
	if ct.RowsAffected() == 0 {
		return fmt.Errorf("behaviour not found")
	}
	return nil
}

// --- Content/i18n methods ---

func (r *behaviourRepository) CreateContent(ctx context.Context, content *models.BehaviourContent) error {
	now := time.Now()
	if content.ID == uuid.Nil {
		content.ID = uuid.New()
	}
	content.CreatedAt = now
	content.UpdatedAt = now

	query := `
        INSERT INTO behaviour_contents (id, behaviour_id, language_id, title, description, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
    `
	_, err := r.db.Exec(ctx, query,
		content.ID,
		content.BehaviourID,
		content.LanguageID,
		content.Title,
		content.Description,
		content.CreatedAt,
		content.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("failed to create behaviour content: %w", err)
	}
	return nil
}

func (r *behaviourRepository) GetContentByBehaviourAndLanguage(ctx context.Context, behaviourID uuid.UUID, languageID int) (*models.BehaviourContent, error) {
	query := `
        SELECT id, behaviour_id, language_id, title, description, created_at, updated_at
        FROM behaviour_contents
        WHERE behaviour_id = $1 AND language_id = $2
    `
	var content models.BehaviourContent
	err := r.db.QueryRow(ctx, query, behaviourID, languageID).Scan(
		&content.ID,
		&content.BehaviourID,
		&content.LanguageID,
		&content.Title,
		&content.Description,
		&content.CreatedAt,
		&content.UpdatedAt,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("behaviour content not found")
		}
		return nil, fmt.Errorf("failed to get behaviour content: %w", err)
	}
	return &content, nil
}

func (r *behaviourRepository) GetContentsByBehaviour(ctx context.Context, behaviourID uuid.UUID) ([]*models.BehaviourContent, error) {
	query := `
        SELECT id, behaviour_id, language_id, title, description, created_at, updated_at
        FROM behaviour_contents
        WHERE behaviour_id = $1
        ORDER BY language_id ASC
    `
	rows, err := r.db.Query(ctx, query, behaviourID)
	if err != nil {
		return nil, fmt.Errorf("failed to get behaviour contents: %w", err)
	}
	defer rows.Close()

	var contents []*models.BehaviourContent
	for rows.Next() {
		var c models.BehaviourContent
		err := rows.Scan(
			&c.ID,
			&c.BehaviourID,
			&c.LanguageID,
			&c.Title,
			&c.Description,
			&c.CreatedAt,
			&c.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan behaviour content: %w", err)
		}
		contents = append(contents, &c)
	}
	return contents, nil
}

func (r *behaviourRepository) UpdateContent(ctx context.Context, content *models.BehaviourContent) error {
	content.UpdatedAt = time.Now()
	query := `
        UPDATE behaviour_contents 
        SET title = $1, description = $2, updated_at = $3
        WHERE behaviour_id = $4 AND language_id = $5
    `
	ct, err := r.db.Exec(ctx, query,
		content.Title,
		content.Description,
		content.UpdatedAt,
		content.BehaviourID,
		content.LanguageID,
	)
	if err != nil {
		return fmt.Errorf("failed to update behaviour content: %w", err)
	}
	if ct.RowsAffected() == 0 {
		return fmt.Errorf("behaviour content not found")
	}
	return nil
}

func (r *behaviourRepository) DeleteContent(ctx context.Context, behaviourID uuid.UUID, languageID int) error {
	query := `DELETE FROM behaviour_contents WHERE behaviour_id = $1 AND language_id = $2`
	ct, err := r.db.Exec(ctx, query, behaviourID, languageID)
	if err != nil {
		return fmt.Errorf("failed to delete behaviour content: %w", err)
	}
	if ct.RowsAffected() == 0 {
		return fmt.Errorf("behaviour content not found")
	}
	return nil
}

// --- Utility methods ---

func (r *behaviourRepository) ExistsByCode(ctx context.Context, code string, excludeID *uuid.UUID) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM behaviours WHERE code = $1`
	args := []interface{}{code}
	if excludeID != nil {
		query += " AND id != $2"
		args = append(args, *excludeID)
	}
	query += ")"
	var exists bool
	err := r.db.QueryRow(ctx, query, args...).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check behaviour existence: %w", err)
	}
	return exists, nil
}

func (r *behaviourRepository) Count(ctx context.Context, activeOnly bool) (int64, error) {
	query := `SELECT COUNT(*) FROM behaviours`
	args := []interface{}{}
	if activeOnly {
		query += " WHERE is_active = $1"
		args = append(args, true)
	}
	var count int64
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count behaviours: %w", err)
	}
	return count, nil
}

func (r *behaviourRepository) GetActiveWithContents(ctx context.Context, limit, offset int, languageID *int) ([]*models.Behaviour, error) {
	behaviours, err := r.GetAll(ctx, limit, offset, true)
	if err != nil {
		return nil, err
	}

	for _, behaviour := range behaviours {
		var contents []*models.BehaviourContent
		if languageID != nil {
			content, err := r.GetContentByBehaviourAndLanguage(ctx, behaviour.ID, *languageID)
			if err != nil && err.Error() != "behaviour content not found" {
				return nil, err
			}
			if content != nil {
				contents = []*models.BehaviourContent{content}
			}
		} else {
			contents, err = r.GetContentsByBehaviour(ctx, behaviour.ID)
			if err != nil {
				return nil, err
			}
		}
		behaviour.Contents = make([]models.BehaviourContent, len(contents))
		for i, content := range contents {
			behaviour.Contents[i] = *content
		}
	}

	return behaviours, nil
}

func (r *behaviourRepository) GetWithContents(ctx context.Context, id uuid.UUID, languageID *int) (*models.Behaviour, error) {
	behaviour, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	var contents []*models.BehaviourContent
	if languageID != nil {
		content, err := r.GetContentByBehaviourAndLanguage(ctx, id, *languageID)
		if err != nil && err.Error() != "behaviour content not found" {
			return nil, err
		}
		if content != nil {
			contents = []*models.BehaviourContent{content}
		}
	} else {
		contents, err = r.GetContentsByBehaviour(ctx, id)
		if err != nil {
			return nil, err
		}
	}

	behaviour.Contents = make([]models.BehaviourContent, len(contents))
	for i, content := range contents {
		behaviour.Contents[i] = *content
	}

	return behaviour, nil
}

func (r *behaviourRepository) Search(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.Behaviour, error) {
	var behaviours []*models.Behaviour

	searchQuery := `
        SELECT DISTINCT b.id, b.code, b.is_active, b.created_by, b.created_at, b.updated_at
        FROM behaviours b
        LEFT JOIN behaviour_contents bc ON b.id = bc.behaviour_id
        WHERE b.is_active = true AND (
            b.code ILIKE $1 OR 
            bc.title ILIKE $1 OR 
            bc.description ILIKE $1
        )`
	args := []interface{}{"%" + query + "%"}
	argIdx := 2

	if languageID != nil {
		searchQuery += fmt.Sprintf(" AND bc.language_id = $%d", argIdx)
		args = append(args, *languageID)
		argIdx++
	}

	searchQuery += " ORDER BY b.created_at DESC"
	if limit > 0 {
		searchQuery += fmt.Sprintf(" LIMIT $%d", argIdx)
		args = append(args, limit)
		argIdx++
	}
	if offset > 0 {
		searchQuery += fmt.Sprintf(" OFFSET $%d", argIdx)
		args = append(args, offset)
	}

	rows, err := r.db.Query(ctx, searchQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to search behaviours: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var b models.Behaviour
		err := rows.Scan(
			&b.ID,
			&b.Code,
			&b.IsActive,
			&b.CreatedBy,
			&b.CreatedAt,
			&b.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan behaviour: %w", err)
		}
		behaviours = append(behaviours, &b)
	}

	// Load contents for each behaviour
	for _, behaviour := range behaviours {
		var contents []*models.BehaviourContent
		if languageID != nil {
			content, err := r.GetContentByBehaviourAndLanguage(ctx, behaviour.ID, *languageID)
			if err != nil && err.Error() != "behaviour content not found" {
				return nil, err
			}
			if content != nil {
				contents = []*models.BehaviourContent{content}
			}
		} else {
			contents, err = r.GetContentsByBehaviour(ctx, behaviour.ID)
			if err != nil {
				return nil, err
			}
		}
		behaviour.Contents = make([]models.BehaviourContent, len(contents))
		for i, content := range contents {
			behaviour.Contents[i] = *content
		}
	}

	return behaviours, nil
}
