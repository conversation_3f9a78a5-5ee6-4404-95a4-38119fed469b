package service

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"github.com/psynarios/admin_back/internal/common/errors"
	"github.com/psynarios/admin_back/internal/library/behaviour/models"
	"github.com/psynarios/admin_back/internal/library/behaviour/repository"
)

type BehaviourService interface {
	// Core CRUD operations
	CreateBehaviour(ctx context.Context, req *models.BehaviourCreateRequest, createdBy *uuid.UUID) (*models.BehaviourResponse, error)
	GetBehaviour(ctx context.Context, id uuid.UUID, languageID *int) (*models.BehaviourResponse, error)
	GetBehaviourByCode(ctx context.Context, code string, languageID *int) (*models.BehaviourResponse, error)
	GetBehaviours(ctx context.Context, limit, offset int, languageID *int, activeOnly bool) ([]*models.BehaviourResponse, error)
	UpdateBehaviour(ctx context.Context, id uuid.UUID, req *models.BehaviourUpdateRequest) (*models.BehaviourResponse, error)
	DeleteBehaviour(ctx context.Context, id uuid.UUID) error

	// Content operations
	CreateBehaviourContent(ctx context.Context, behaviourID uuid.UUID, req *models.BehaviourContentRequest) (*models.BehaviourContentResponse, error)
	UpdateBehaviourContent(ctx context.Context, behaviourID uuid.UUID, languageID int, req *models.BehaviourContentRequest) (*models.BehaviourContentResponse, error)
	DeleteBehaviourContent(ctx context.Context, behaviourID uuid.UUID, languageID int) error

	// Advanced operations
	SearchBehaviours(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.BehaviourResponse, error)
	GetBehaviourStatistics(ctx context.Context) (*BehaviourStatistics, error)
}

type BehaviourStatistics struct {
	TotalBehaviours  int64 `json:"total_behaviours"`
	ActiveBehaviours int64 `json:"active_behaviours"`
}

type behaviourService struct {
	repo repository.BehaviourRepository
}

func NewBehaviourService(repo repository.BehaviourRepository) BehaviourService {
	return &behaviourService{
		repo: repo,
	}
}

func (s *behaviourService) CreateBehaviour(ctx context.Context, req *models.BehaviourCreateRequest, createdBy *uuid.UUID) (*models.BehaviourResponse, error) {
	// Check if code already exists
	exists, err := s.repo.ExistsByCode(ctx, req.Code, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to check code existence: %w", err)
	}
	if exists {
		return nil, errors.NewConflictError("behaviour with this code already exists", nil)
	}

	// Create behaviour entity
	behaviour := &models.Behaviour{
		ID:        uuid.New(),
		Code:      req.Code,
		IsActive:  true,
		CreatedBy: createdBy,
	}

	err = s.repo.Create(ctx, behaviour)
	if err != nil {
		return nil, fmt.Errorf("failed to create behaviour: %w", err)
	}

	// Create content for each language
	for _, contentReq := range req.Contents {
		content := &models.BehaviourContent{
			ID:          uuid.New(),
			BehaviourID: behaviour.ID,
			LanguageID:  contentReq.LanguageID,
			Title:       contentReq.Title,
			Description: contentReq.Description,
		}

		err = s.repo.CreateContent(ctx, content)
		if err != nil {
			return nil, fmt.Errorf("failed to create behaviour content: %w", err)
		}
	}

	// Return created behaviour with contents
	return s.GetBehaviour(ctx, behaviour.ID, nil)
}

func (s *behaviourService) GetBehaviour(ctx context.Context, id uuid.UUID, languageID *int) (*models.BehaviourResponse, error) {
	behaviour, err := s.repo.GetWithContents(ctx, id, languageID)
	if err != nil {
		return nil, err
	}
	return s.toResponse(behaviour), nil
}

func (s *behaviourService) GetBehaviourByCode(ctx context.Context, code string, languageID *int) (*models.BehaviourResponse, error) {
	behaviour, err := s.repo.GetByCode(ctx, code)
	if err != nil {
		return nil, err
	}
	return s.GetBehaviour(ctx, behaviour.ID, languageID)
}

func (s *behaviourService) GetBehaviours(ctx context.Context, limit, offset int, languageID *int, activeOnly bool) ([]*models.BehaviourResponse, error) {
	behaviours, err := s.repo.GetActiveWithContents(ctx, limit, offset, languageID)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.BehaviourResponse, len(behaviours))
	for i, behaviour := range behaviours {
		responses[i] = s.toResponse(behaviour)
	}

	return responses, nil
}

func (s *behaviourService) UpdateBehaviour(ctx context.Context, id uuid.UUID, req *models.BehaviourUpdateRequest) (*models.BehaviourResponse, error) {
	behaviour, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Update core fields if provided
	if req.Code != nil {
		// Check if new code already exists (excluding current behaviour)
		exists, err := s.repo.ExistsByCode(ctx, *req.Code, &id)
		if err != nil {
			return nil, fmt.Errorf("failed to check code existence: %w", err)
		}
		if exists {
			return nil, errors.NewConflictError("behaviour with this code already exists", nil)
		}
		behaviour.Code = *req.Code
	}

	if req.IsActive != nil {
		behaviour.IsActive = *req.IsActive
	}

	err = s.repo.Update(ctx, behaviour)
	if err != nil {
		return nil, fmt.Errorf("failed to update behaviour: %w", err)
	}

	// Update contents if provided
	if req.Contents != nil {
		for _, contentReq := range req.Contents {
			// Check if content exists for this language
			existingContent, err := s.repo.GetContentByBehaviourAndLanguage(ctx, id, contentReq.LanguageID)
			if err != nil && !errors.IsNotFoundError(err) {
				return nil, err
			}

			if existingContent != nil {
				// Update existing content
				existingContent.Title = contentReq.Title
				existingContent.Description = contentReq.Description
				err = s.repo.UpdateContent(ctx, existingContent)
				if err != nil {
					return nil, fmt.Errorf("failed to update behaviour content: %w", err)
				}
			} else {
				// Create new content
				content := &models.BehaviourContent{
					ID:          uuid.New(),
					BehaviourID: id,
					LanguageID:  contentReq.LanguageID,
					Title:       contentReq.Title,
					Description: contentReq.Description,
				}
				err = s.repo.CreateContent(ctx, content)
				if err != nil {
					return nil, fmt.Errorf("failed to create behaviour content: %w", err)
				}
			}
		}
	}

	return s.GetBehaviour(ctx, id, nil)
}

func (s *behaviourService) DeleteBehaviour(ctx context.Context, id uuid.UUID) error {
	return s.repo.Delete(ctx, id)
}

func (s *behaviourService) CreateBehaviourContent(ctx context.Context, behaviourID uuid.UUID, req *models.BehaviourContentRequest) (*models.BehaviourContentResponse, error) {
	// Verify behaviour exists
	_, err := s.repo.GetByID(ctx, behaviourID)
	if err != nil {
		return nil, err
	}

	content := &models.BehaviourContent{
		ID:          uuid.New(),
		BehaviourID: behaviourID,
		LanguageID:  req.LanguageID,
		Title:       req.Title,
		Description: req.Description,
	}

	err = s.repo.CreateContent(ctx, content)
	if err != nil {
		return nil, fmt.Errorf("failed to create behaviour content: %w", err)
	}

	createdContent, err := s.repo.GetContentByBehaviourAndLanguage(ctx, behaviourID, req.LanguageID)
	if err != nil {
		return nil, err
	}

	return s.toContentResponse(createdContent), nil
}

func (s *behaviourService) UpdateBehaviourContent(ctx context.Context, behaviourID uuid.UUID, languageID int, req *models.BehaviourContentRequest) (*models.BehaviourContentResponse, error) {
	// Get existing content
	content, err := s.repo.GetContentByBehaviourAndLanguage(ctx, behaviourID, languageID)
	if err != nil {
		return nil, err
	}

	// Update fields
	content.Title = req.Title
	content.Description = req.Description

	err = s.repo.UpdateContent(ctx, content)
	if err != nil {
		return nil, fmt.Errorf("failed to update behaviour content: %w", err)
	}

	// Get updated content with language info
	updatedContent, err := s.repo.GetContentByBehaviourAndLanguage(ctx, behaviourID, languageID)
	if err != nil {
		return nil, err
	}

	return s.toContentResponse(updatedContent), nil
}

func (s *behaviourService) DeleteBehaviourContent(ctx context.Context, behaviourID uuid.UUID, languageID int) error {
	return s.repo.DeleteContent(ctx, behaviourID, languageID)
}

func (s *behaviourService) SearchBehaviours(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.BehaviourResponse, error) {
	behaviours, err := s.repo.Search(ctx, query, languageID, limit, offset)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.BehaviourResponse, len(behaviours))
	for i, behaviour := range behaviours {
		responses[i] = s.toResponse(behaviour)
	}

	return responses, nil
}

func (s *behaviourService) GetBehaviourStatistics(ctx context.Context) (*BehaviourStatistics, error) {
	totalCount, err := s.repo.Count(ctx, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get total behaviour count: %w", err)
	}

	activeCount, err := s.repo.Count(ctx, true)
	if err != nil {
		return nil, fmt.Errorf("failed to get active behaviour count: %w", err)
	}

	return &BehaviourStatistics{
		TotalBehaviours:  totalCount,
		ActiveBehaviours: activeCount,
	}, nil
}

// Helper methods for converting models to responses
func (s *behaviourService) toResponse(behaviour *models.Behaviour) *models.BehaviourResponse {
	response := &models.BehaviourResponse{
		ID:        behaviour.ID,
		Code:      behaviour.Code,
		IsActive:  behaviour.IsActive,
		CreatedBy: behaviour.CreatedBy,
		CreatedAt: behaviour.CreatedAt,
		UpdatedAt: behaviour.UpdatedAt,
	}

	if len(behaviour.Contents) > 0 {
		response.Contents = make([]models.BehaviourContentResponse, len(behaviour.Contents))
		for i, content := range behaviour.Contents {
			response.Contents[i] = models.BehaviourContentResponse{
				ID:          content.ID,
				LanguageID:  content.LanguageID,
				Title:       content.Title,
				Description: content.Description,
				Language:    content.Language,
				CreatedAt:   content.CreatedAt,
				UpdatedAt:   content.UpdatedAt,
			}
		}
	}

	return response
}

func (s *behaviourService) toContentResponse(content *models.BehaviourContent) *models.BehaviourContentResponse {
	return &models.BehaviourContentResponse{
		ID:          content.ID,
		LanguageID:  content.LanguageID,
		Title:       content.Title,
		Description: content.Description,
		Language:    content.Language,
		CreatedAt:   content.CreatedAt,
		UpdatedAt:   content.UpdatedAt,
	}
}
