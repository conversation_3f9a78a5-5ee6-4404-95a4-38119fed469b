package enum

// DifficultyLevel représente le niveau de difficulté
type DifficultyLevel string

const (
	DifficultyDebutant    DifficultyLevel = "Débutant"
	DifficultyConfirme    DifficultyLevel = "Confirmé"
	DifficultyExperimente DifficultyLevel = "Expérimenté"
)

type ScenarioType string

const (
	ScenarioTypeInterpersonal ScenarioType = "Situations Interpersonnelles"
	ScenarioTypeDialogue      ScenarioType = "Dialogue Intérieur"
	ScenarioTypePerceptual    ScenarioType = "Nuances Perceptives"
)

type CharacterType string

const (
	CharacterTypeCharacter CharacterType = "Character"
	CharacterTypeNarrator  CharacterType = "Narrator"
)
