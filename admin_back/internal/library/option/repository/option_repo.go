package repository

import (
	"context"

	"github.com/google/uuid"
	"github.com/psynarios/admin_back/internal/library/option/models"
)

type OptionRepository interface {
	// Core CRUD operations
	Create(ctx context.Context, option *models.Option) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Option, error)
	GetByCode(ctx context.Context, code string) (*models.Option, error)
	GetAll(ctx context.Context, limit, offset int, activeOnly bool) ([]*models.Option, error)
	Update(ctx context.Context, option *models.Option) error
	Delete(ctx context.Context, id uuid.UUID) error

	// Content operations
	CreateContent(ctx context.Context, content *models.OptionContent) error
	GetContentByOptionAndLanguage(ctx context.Context, optionID uuid.UUID, languageID int) (*models.OptionContent, error)
	GetContentsByOption(ctx context.Context, optionID uuid.UUID) ([]*models.OptionContent, error)
	UpdateContent(ctx context.Context, content *models.OptionContent) error
	DeleteContent(ctx context.Context, optionID uuid.UUID, languageID int) error

	// Advanced queries
	GetWithContents(ctx context.Context, id uuid.UUID, languageID *int) (*models.Option, error)
	GetByInteraction(ctx context.Context, interactionID uuid.UUID, languageID *int) ([]*models.Option, error)
	GetCorrectByInteraction(ctx context.Context, interactionID uuid.UUID, languageID *int) ([]*models.Option, error)
	Search(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.Option, error)

	// Utility methods
	ExistsByCode(ctx context.Context, code string, excludeID *uuid.UUID) (bool, error)
	Count(ctx context.Context, activeOnly bool) (int64, error)
	CountByInteraction(ctx context.Context, interactionID uuid.UUID, correctOnly bool) (int64, error)
}
