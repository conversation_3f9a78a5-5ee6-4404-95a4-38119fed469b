package postgres

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	i18nModels "github.com/psynarios/admin_back/internal/i18n/models"
	"github.com/psynarios/admin_back/internal/library/option/models"
	"github.com/psynarios/admin_back/internal/library/option/repository"
)

// optionRepository implements the OptionRepository interface
type optionRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

// NewOptionRepository creates a new option repository
func NewOptionRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.OptionRepository {
	return &optionRepository{
		db:     db,
		logger: logger.With().Str("component", "option_repository").<PERSON>gger(),
	}
}

// Create creates a new option
func (r *optionRepository) Create(ctx context.Context, option *models.Option) error {
	query := `
		INSERT INTO options (id, code, is_correct, interaction_id, is_active, created_by, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	if option.ID == uuid.Nil {
		option.ID = uuid.New()
	}

	now := time.Now()
	option.CreatedAt = now
	option.UpdatedAt = now

	_, err := r.db.Exec(
		ctx,
		query,
		option.ID,
		option.Code,
		option.IsCorrect,
		option.InteractionID,
		option.IsActive,
		option.CreatedBy,
		option.CreatedAt,
		option.UpdatedAt,
	)

	if err != nil {
		r.logger.Error().Err(err).
			Str("code", option.Code).
			Str("interaction_id", option.InteractionID.String()).
			Bool("is_correct", option.IsCorrect).
			Msg("Failed to create option")
		return fmt.Errorf("failed to create option: %w", err)
	}

	r.logger.Info().
		Str("id", option.ID.String()).
		Str("code", option.Code).
		Bool("is_correct", option.IsCorrect).
		Msg("Option created successfully")
	return nil
}

// GetByID gets an option by ID
func (r *optionRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Option, error) {
	query := `
		SELECT id, code, is_correct, interaction_id, is_active, created_by, created_at, updated_at
		FROM options
		WHERE id = $1
	`

	var option models.Option
	row := r.db.QueryRow(ctx, query, id)

	err := row.Scan(
		&option.ID,
		&option.Code,
		&option.IsCorrect,
		&option.InteractionID,
		&option.IsActive,
		&option.CreatedBy,
		&option.CreatedAt,
		&option.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("option not found: %s", id)
		}
		return nil, fmt.Errorf("failed to get option: %w", err)
	}

	return &option, nil
}

// GetByCode gets an option by code
func (r *optionRepository) GetByCode(ctx context.Context, code string) (*models.Option, error) {
	query := `
		SELECT id, code, is_correct, interaction_id, is_active, created_by, created_at, updated_at
		FROM options
		WHERE code = $1
	`

	var option models.Option
	row := r.db.QueryRow(ctx, query, code)

	err := row.Scan(
		&option.ID,
		&option.Code,
		&option.IsCorrect,
		&option.InteractionID,
		&option.IsActive,
		&option.CreatedBy,
		&option.CreatedAt,
		&option.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("option not found with code: %s", code)
		}
		return nil, fmt.Errorf("failed to get option by code: %w", err)
	}

	return &option, nil
}

// GetAll gets all options with pagination and filtering
func (r *optionRepository) GetAll(ctx context.Context, limit, offset int, activeOnly bool) ([]*models.Option, error) {
	query := `
		SELECT id, code, is_correct, interaction_id, is_active, created_by, created_at, updated_at
		FROM options
	`
	args := []interface{}{}
	argIndex := 1

	if activeOnly {
		query += " WHERE is_active = $" + fmt.Sprintf("%d", argIndex)
		args = append(args, true)
		argIndex++
	}

	query += " ORDER BY created_at DESC"

	if limit > 0 {
		query += " LIMIT $" + fmt.Sprintf("%d", argIndex)
		args = append(args, limit)
		argIndex++
	}

	if offset > 0 {
		query += " OFFSET $" + fmt.Sprintf("%d", argIndex)
		args = append(args, offset)
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get options: %w", err)
	}
	defer rows.Close()

	var options []*models.Option
	for rows.Next() {
		var option models.Option
		err := rows.Scan(
			&option.ID,
			&option.Code,
			&option.IsCorrect,
			&option.InteractionID,
			&option.IsActive,
			&option.CreatedBy,
			&option.CreatedAt,
			&option.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan option: %w", err)
		}
		options = append(options, &option)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating options: %w", err)
	}

	r.logger.Debug().Int("count", len(options)).Msg("Retrieved options")
	return options, nil
}

// Update updates an option
func (r *optionRepository) Update(ctx context.Context, option *models.Option) error {
	query := `
		UPDATE options
		SET code = $2, is_correct = $3, interaction_id = $4, is_active = $5, updated_at = $6
		WHERE id = $1
	`

	option.UpdatedAt = time.Now()

	result, err := r.db.Exec(
		ctx,
		query,
		option.ID,
		option.Code,
		option.IsCorrect,
		option.InteractionID,
		option.IsActive,
		option.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update option: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("option not found: %s", option.ID)
	}

	r.logger.Info().
		Str("id", option.ID.String()).
		Str("code", option.Code).
		Msg("Option updated successfully")
	return nil
}

// Delete deletes an option by ID
func (r *optionRepository) Delete(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM options WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete option: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("option not found: %s", id)
	}

	r.logger.Info().Str("id", id.String()).Msg("Option deleted successfully")
	return nil
}

// CreateContent creates content for an option
func (r *optionRepository) CreateContent(ctx context.Context, content *models.OptionContent) error {
	query := `
		INSERT INTO option_contents (id, option_id, language_id, text, hint, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
	`

	if content.ID == uuid.Nil {
		content.ID = uuid.New()
	}

	now := time.Now()
	content.CreatedAt = now
	content.UpdatedAt = now

	_, err := r.db.Exec(
		ctx,
		query,
		content.ID,
		content.OptionID,
		content.LanguageID,
		content.Text,
		content.Hint,
		content.CreatedAt,
		content.UpdatedAt,
	)

	if err != nil {
		r.logger.Error().Err(err).
			Str("option_id", content.OptionID.String()).
			Int("language_id", content.LanguageID).
			Msg("Failed to create option content")
		return fmt.Errorf("failed to create option content: %w", err)
	}

	return nil
}

// GetContentByOptionAndLanguage gets content for specific option and language
func (r *optionRepository) GetContentByOptionAndLanguage(ctx context.Context, optionID uuid.UUID, languageID int) (*models.OptionContent, error) {
	query := `
		SELECT 
			oc.id, oc.option_id, oc.language_id, oc.text, oc.hint, oc.created_at, oc.updated_at,
			l.id, l.code, l.name, l.native_name, l.is_active, l.is_default
		FROM option_contents oc
		LEFT JOIN languages l ON oc.language_id = l.id
		WHERE oc.option_id = $1 AND oc.language_id = $2
	`

	var content models.OptionContent
	var langID sql.NullInt32
	var langCode sql.NullString
	var langName sql.NullString
	var langNativeName sql.NullString
	var langIsActive sql.NullBool
	var langIsDefault sql.NullBool

	row := r.db.QueryRow(ctx, query, optionID, languageID)

	err := row.Scan(
		&content.ID,
		&content.OptionID,
		&content.LanguageID,
		&content.Text,
		&content.Hint,
		&content.CreatedAt,
		&content.UpdatedAt,
		&langID,
		&langCode,
		&langName,
		&langNativeName,
		&langIsActive,
		&langIsDefault,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("option content not found for option %s and language %d", optionID, languageID)
		}
		return nil, fmt.Errorf("failed to get option content: %w", err)
	}

	// Set language if available
	if langID.Valid {
		content.Language = &i18nModels.Language{
			ID:         int(langID.Int32),
			Code:       langCode.String,
			Name:       langName.String,
			NativeName: langNativeName.String,
			IsActive:   langIsActive.Bool,
			IsDefault:  langIsDefault.Bool,
		}
	}

	return &content, nil
}

// GetContentsByOption gets all contents for an option
func (r *optionRepository) GetContentsByOption(ctx context.Context, optionID uuid.UUID) ([]*models.OptionContent, error) {
	query := `
		SELECT 
			oc.id, oc.option_id, oc.language_id, oc.text, oc.hint, oc.created_at, oc.updated_at,
			l.id, l.code, l.name, l.native_name, l.is_active, l.is_default
		FROM option_contents oc
		LEFT JOIN languages l ON oc.language_id = l.id
		WHERE oc.option_id = $1
		ORDER BY l.is_default DESC, l.code ASC
	`

	rows, err := r.db.Query(ctx, query, optionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get option contents: %w", err)
	}
	defer rows.Close()

	var contents []*models.OptionContent
	for rows.Next() {
		var content models.OptionContent
		var langID sql.NullInt32
		var langCode sql.NullString
		var langName sql.NullString
		var langNativeName sql.NullString
		var langIsActive sql.NullBool
		var langIsDefault sql.NullBool

		err := rows.Scan(
			&content.ID,
			&content.OptionID,
			&content.LanguageID,
			&content.Text,
			&content.Hint,
			&content.CreatedAt,
			&content.UpdatedAt,
			&langID,
			&langCode,
			&langName,
			&langNativeName,
			&langIsActive,
			&langIsDefault,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan option content: %w", err)
		}

		// Set language if available
		if langID.Valid {
			content.Language = &i18nModels.Language{
				ID:         int(langID.Int32),
				Code:       langCode.String,
				Name:       langName.String,
				NativeName: langNativeName.String,
				IsActive:   langIsActive.Bool,
				IsDefault:  langIsDefault.Bool,
			}
		}

		contents = append(contents, &content)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating option contents: %w", err)
	}

	return contents, nil
}

// UpdateContent updates content for an option
func (r *optionRepository) UpdateContent(ctx context.Context, content *models.OptionContent) error {
	query := `
		UPDATE option_contents
		SET text = $3, hint = $4, updated_at = $5
		WHERE option_id = $1 AND language_id = $2
	`

	content.UpdatedAt = time.Now()

	result, err := r.db.Exec(
		ctx,
		query,
		content.OptionID,
		content.LanguageID,
		content.Text,
		content.Hint,
		content.UpdatedAt,
	)

	if err != nil {
		return fmt.Errorf("failed to update option content: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("option content not found for option %s and language %d", content.OptionID, content.LanguageID)
	}

	r.logger.Info().
		Str("option_id", content.OptionID.String()).
		Int("language_id", content.LanguageID).
		Msg("Option content updated successfully")
	return nil
}

// DeleteContent deletes content for an option and language
func (r *optionRepository) DeleteContent(ctx context.Context, optionID uuid.UUID, languageID int) error {
	query := `DELETE FROM option_contents WHERE option_id = $1 AND language_id = $2`

	result, err := r.db.Exec(ctx, query, optionID, languageID)
	if err != nil {
		return fmt.Errorf("failed to delete option content: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("option content not found for option %s and language %d", optionID, languageID)
	}

	r.logger.Info().
		Str("option_id", optionID.String()).
		Int("language_id", languageID).
		Msg("Option content deleted successfully")
	return nil
}

// GetWithContents gets an option with its content
func (r *optionRepository) GetWithContents(ctx context.Context, id uuid.UUID, languageID *int) (*models.Option, error) {
	// Get the base option
	option, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Get contents
	var contents []*models.OptionContent
	if languageID != nil {
		// Get content for specific language
		content, err := r.GetContentByOptionAndLanguage(ctx, id, *languageID)
		if err != nil && !strings.Contains(err.Error(), "not found") {
			return nil, err
		}
		if content != nil {
			contents = []*models.OptionContent{content}
		}
	} else {
		// Get all contents
		contents, err = r.GetContentsByOption(ctx, id)
		if err != nil {
			return nil, err
		}
	}

	// Convert to slice of values for the model
	option.Contents = make([]models.OptionContent, len(contents))
	for i, content := range contents {
		option.Contents[i] = *content
	}

	return option, nil
}

// GetByInteraction gets all options for an interaction
func (r *optionRepository) GetByInteraction(ctx context.Context, interactionID uuid.UUID, languageID *int) ([]*models.Option, error) {
	query := `
		SELECT id, code, is_correct, interaction_id, is_active, created_by, created_at, updated_at
		FROM options
		WHERE interaction_id = $1 AND is_active = true
		ORDER BY created_at ASC
	`

	rows, err := r.db.Query(ctx, query, interactionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get options by interaction: %w", err)
	}
	defer rows.Close()

	var options []*models.Option
	for rows.Next() {
		var option models.Option
		err := rows.Scan(
			&option.ID,
			&option.Code,
			&option.IsCorrect,
			&option.InteractionID,
			&option.IsActive,
			&option.CreatedBy,
			&option.CreatedAt,
			&option.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan option: %w", err)
		}

		// Load contents for each option
		var contents []*models.OptionContent
		if languageID != nil {
			content, err := r.GetContentByOptionAndLanguage(ctx, option.ID, *languageID)
			if err != nil && !strings.Contains(err.Error(), "not found") {
				return nil, err
			}
			if content != nil {
				contents = []*models.OptionContent{content}
			}
		} else {
			contents, err = r.GetContentsByOption(ctx, option.ID)
			if err != nil {
				return nil, err
			}
		}

		option.Contents = make([]models.OptionContent, len(contents))
		for i, content := range contents {
			option.Contents[i] = *content
		}

		options = append(options, &option)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating options: %w", err)
	}

	return options, nil
}

// GetCorrectByInteraction gets only correct options for an interaction
func (r *optionRepository) GetCorrectByInteraction(ctx context.Context, interactionID uuid.UUID, languageID *int) ([]*models.Option, error) {
	query := `
		SELECT id, code, is_correct, interaction_id, is_active, created_by, created_at, updated_at
		FROM options
		WHERE interaction_id = $1 AND is_correct = true AND is_active = true
		ORDER BY created_at ASC
	`

	rows, err := r.db.Query(ctx, query, interactionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get correct options by interaction: %w", err)
	}
	defer rows.Close()

	var options []*models.Option
	for rows.Next() {
		var option models.Option
		err := rows.Scan(
			&option.ID,
			&option.Code,
			&option.IsCorrect,
			&option.InteractionID,
			&option.IsActive,
			&option.CreatedBy,
			&option.CreatedAt,
			&option.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan option: %w", err)
		}

		// Load contents for each option
		var contents []*models.OptionContent
		if languageID != nil {
			content, err := r.GetContentByOptionAndLanguage(ctx, option.ID, *languageID)
			if err != nil && !strings.Contains(err.Error(), "not found") {
				return nil, err
			}
			if content != nil {
				contents = []*models.OptionContent{content}
			}
		} else {
			contents, err = r.GetContentsByOption(ctx, option.ID)
			if err != nil {
				return nil, err
			}
		}

		option.Contents = make([]models.OptionContent, len(contents))
		for i, content := range contents {
			option.Contents[i] = *content
		}

		options = append(options, &option)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating options: %w", err)
	}

	return options, nil
}

// Search searches options by query string
func (r *optionRepository) Search(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.Option, error) {
	searchQuery := `
		SELECT DISTINCT o.id, o.code, o.is_correct, o.interaction_id, o.is_active, o.created_by, o.created_at, o.updated_at
		FROM options o
		LEFT JOIN option_contents oc ON o.id = oc.option_id
		WHERE o.is_active = true AND (
			o.code ILIKE $1 OR 
			oc.text ILIKE $1 OR 
			oc.hint ILIKE $1
		)
	`

	args := []interface{}{"%" + query + "%"}
	argIndex := 2

	if languageID != nil {
		searchQuery += " AND oc.language_id = $" + fmt.Sprintf("%d", argIndex)
		args = append(args, *languageID)
		argIndex++
	}

	searchQuery += " ORDER BY o.created_at DESC"

	if limit > 0 {
		searchQuery += " LIMIT $" + fmt.Sprintf("%d", argIndex)
		args = append(args, limit)
		argIndex++
	}

	if offset > 0 {
		searchQuery += " OFFSET $" + fmt.Sprintf("%d", argIndex)
		args = append(args, offset)
	}

	rows, err := r.db.Query(ctx, searchQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to search options: %w", err)
	}
	defer rows.Close()

	var options []*models.Option
	for rows.Next() {
		var option models.Option
		err := rows.Scan(
			&option.ID,
			&option.Code,
			&option.IsCorrect,
			&option.InteractionID,
			&option.IsActive,
			&option.CreatedBy,
			&option.CreatedAt,
			&option.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan option: %w", err)
		}

		// Load contents for each option
		var contents []*models.OptionContent
		if languageID != nil {
			content, err := r.GetContentByOptionAndLanguage(ctx, option.ID, *languageID)
			if err != nil && !strings.Contains(err.Error(), "not found") {
				return nil, err
			}
			if content != nil {
				contents = []*models.OptionContent{content}
			}
		} else {
			contents, err = r.GetContentsByOption(ctx, option.ID)
			if err != nil {
				return nil, err
			}
		}

		option.Contents = make([]models.OptionContent, len(contents))
		for i, content := range contents {
			option.Contents[i] = *content
		}

		options = append(options, &option)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating options: %w", err)
	}

	r.logger.Debug().
		Str("query", query).
		Int("results", len(options)).
		Msg("Search options completed")

	return options, nil
}

// ExistsByCode checks if an option exists by code
func (r *optionRepository) ExistsByCode(ctx context.Context, code string, excludeID *uuid.UUID) (bool, error) {
	query := `SELECT EXISTS(SELECT 1 FROM options WHERE code = $1`
	args := []interface{}{code}

	if excludeID != nil {
		query += " AND id != $2"
		args = append(args, *excludeID)
	}

	query += ")"

	var exists bool
	err := r.db.QueryRow(ctx, query, args...).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("failed to check option existence: %w", err)
	}

	return exists, nil
}

// Count counts options with optional filtering
func (r *optionRepository) Count(ctx context.Context, activeOnly bool) (int64, error) {
	query := `SELECT COUNT(*) FROM options`
	args := []interface{}{}

	if activeOnly {
		query += " WHERE is_active = $1"
		args = append(args, true)
	}

	var count int64
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count options: %w", err)
	}

	return count, nil
}

// CountByInteraction counts options for a specific interaction
func (r *optionRepository) CountByInteraction(ctx context.Context, interactionID uuid.UUID, correctOnly bool) (int64, error) {
	query := `SELECT COUNT(*) FROM options WHERE interaction_id = $1 AND is_active = true`
	args := []interface{}{interactionID}

	if correctOnly {
		query += " AND is_correct = $2"
		args = append(args, true)
	}

	var count int64
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count options by interaction: %w", err)
	}

	return count, nil
}
