package service

import (
	"context"
	"fmt"

	"github.com/google/uuid"

	"github.com/psynarios/admin_back/internal/common/errors"
	"github.com/psynarios/admin_back/internal/library/option/models"
	"github.com/psynarios/admin_back/internal/library/option/repository"
)

type OptionService interface {
	// Core CRUD operations
	CreateOption(ctx context.Context, req *models.OptionCreateRequest, createdBy *uuid.UUID) (*models.OptionResponse, error)
	GetOption(ctx context.Context, id uuid.UUID, languageID *int) (*models.OptionResponse, error)
	GetOptionByCode(ctx context.Context, code string, languageID *int) (*models.OptionResponse, error)
	GetOptions(ctx context.Context, limit, offset int, languageID *int, activeOnly bool) ([]*models.OptionResponse, error)
	UpdateOption(ctx context.Context, id uuid.UUID, req *models.OptionUpdateRequest) (*models.OptionResponse, error)
	DeleteOption(ctx context.Context, id uuid.UUID) error

	// Content operations
	CreateOptionContent(ctx context.Context, optionID uuid.UUID, req *models.OptionContentRequest) (*models.OptionContentResponse, error)
	UpdateOptionContent(ctx context.Context, optionID uuid.UUID, languageID int, req *models.OptionContentRequest) (*models.OptionContentResponse, error)
	DeleteOptionContent(ctx context.Context, optionID uuid.UUID, languageID int) error

	// Advanced operations
	GetOptionsByInteraction(ctx context.Context, interactionID uuid.UUID, languageID *int) ([]*models.OptionResponse, error)
	GetCorrectOptionsByInteraction(ctx context.Context, interactionID uuid.UUID, languageID *int) ([]*models.OptionResponse, error)
	SearchOptions(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.OptionResponse, error)
	GetOptionStatistics(ctx context.Context) (*OptionStatistics, error)
	ValidateOptionCorrectness(ctx context.Context, interactionID uuid.UUID) error
}

type OptionStatistics struct {
	TotalOptions   int64 `json:"total_options"`
	ActiveOptions  int64 `json:"active_options"`
	CorrectOptions int64 `json:"correct_options"`
}

type optionService struct {
	repo repository.OptionRepository
}

func NewOptionService(repo repository.OptionRepository) OptionService {
	return &optionService{repo: repo}
}

func (s *optionService) CreateOption(ctx context.Context, req *models.OptionCreateRequest, createdBy *uuid.UUID) (*models.OptionResponse, error) {
	// Check if code already exists
	exists, err := s.repo.ExistsByCode(ctx, req.Code, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to check code existence: %w", err)
	}
	if exists {
		return nil, errors.NewConflictError("option with this code already exists", nil)
	}

	// Create option entity
	option := &models.Option{
		ID:            uuid.New(),
		Code:          req.Code,
		IsCorrect:     req.IsCorrect,
		InteractionID: req.InteractionID,
		IsActive:      true,
		CreatedBy:     createdBy,
	}

	err = s.repo.Create(ctx, option)
	if err != nil {
		return nil, fmt.Errorf("failed to create option: %w", err)
	}

	// Create content for each language
	for _, contentReq := range req.Contents {
		content := &models.OptionContent{
			ID:         uuid.New(),
			OptionID:   option.ID,
			LanguageID: contentReq.LanguageID,
			Text:       contentReq.Text,
			Hint:       contentReq.Hint,
		}

		err = s.repo.CreateContent(ctx, content)
		if err != nil {
			return nil, fmt.Errorf("failed to create option content: %w", err)
		}
	}

	return s.GetOption(ctx, option.ID, nil)
}

func (s *optionService) GetOption(ctx context.Context, id uuid.UUID, languageID *int) (*models.OptionResponse, error) {
	option, err := s.repo.GetWithContents(ctx, id, languageID)
	if err != nil {
		return nil, err
	}

	return s.toResponse(option), nil
}

func (s *optionService) GetOptionByCode(ctx context.Context, code string, languageID *int) (*models.OptionResponse, error) {
	option, err := s.repo.GetByCode(ctx, code)
	if err != nil {
		return nil, err
	}

	return s.GetOption(ctx, option.ID, languageID)
}

func (s *optionService) GetOptions(ctx context.Context, limit, offset int, languageID *int, activeOnly bool) ([]*models.OptionResponse, error) {
	options, err := s.repo.GetAll(ctx, limit, offset, activeOnly)
	if err != nil {
		return nil, err
	}

	// Load contents for each option
	responses := make([]*models.OptionResponse, len(options))
	for i, option := range options {
		optionWithContents, err := s.repo.GetWithContents(ctx, option.ID, languageID)
		if err != nil {
			return nil, err
		}
		responses[i] = s.toResponse(optionWithContents)
	}

	return responses, nil
}

func (s *optionService) UpdateOption(ctx context.Context, id uuid.UUID, req *models.OptionUpdateRequest) (*models.OptionResponse, error) {
	option, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Update core fields if provided
	if req.Code != nil {
		exists, err := s.repo.ExistsByCode(ctx, *req.Code, &id)
		if err != nil {
			return nil, fmt.Errorf("failed to check code existence: %w", err)
		}
		if exists {
			return nil, errors.NewConflictError("option with this code already exists", nil)
		}
		option.Code = *req.Code
	}

	if req.IsCorrect != nil {
		option.IsCorrect = *req.IsCorrect
	}

	if req.InteractionID != nil {
		option.InteractionID = *req.InteractionID
	}

	if req.IsActive != nil {
		option.IsActive = *req.IsActive
	}

	err = s.repo.Update(ctx, option)
	if err != nil {
		return nil, fmt.Errorf("failed to update option: %w", err)
	}

	// Update contents if provided
	if req.Contents != nil {
		for _, contentReq := range req.Contents {
			existingContent, err := s.repo.GetContentByOptionAndLanguage(ctx, id, contentReq.LanguageID)
			if err != nil && !errors.IsNotFoundError(err) {
				return nil, err
			}

			if existingContent != nil {
				// Update existing content
				existingContent.Text = contentReq.Text
				existingContent.Hint = contentReq.Hint
				err = s.repo.UpdateContent(ctx, existingContent)
				if err != nil {
					return nil, fmt.Errorf("failed to update option content: %w", err)
				}
			} else {
				// Create new content
				content := &models.OptionContent{
					ID:         uuid.New(),
					OptionID:   id,
					LanguageID: contentReq.LanguageID,
					Text:       contentReq.Text,
					Hint:       contentReq.Hint,
				}
				err = s.repo.CreateContent(ctx, content)
				if err != nil {
					return nil, fmt.Errorf("failed to create option content: %w", err)
				}
			}
		}
	}

	return s.GetOption(ctx, id, nil)
}

func (s *optionService) DeleteOption(ctx context.Context, id uuid.UUID) error {
	return s.repo.Delete(ctx, id)
}

func (s *optionService) CreateOptionContent(ctx context.Context, optionID uuid.UUID, req *models.OptionContentRequest) (*models.OptionContentResponse, error) {
	// Verify option exists
	_, err := s.repo.GetByID(ctx, optionID)
	if err != nil {
		return nil, err
	}

	content := &models.OptionContent{
		ID:         uuid.New(),
		OptionID:   optionID,
		LanguageID: req.LanguageID,
		Text:       req.Text,
		Hint:       req.Hint,
	}

	err = s.repo.CreateContent(ctx, content)
	if err != nil {
		return nil, fmt.Errorf("failed to create option content: %w", err)
	}

	// Get created content with language info
	createdContent, err := s.repo.GetContentByOptionAndLanguage(ctx, optionID, req.LanguageID)
	if err != nil {
		return nil, err
	}

	return s.toContentResponse(createdContent), nil
}

func (s *optionService) UpdateOptionContent(ctx context.Context, optionID uuid.UUID, languageID int, req *models.OptionContentRequest) (*models.OptionContentResponse, error) {
	// Get existing content
	content, err := s.repo.GetContentByOptionAndLanguage(ctx, optionID, languageID)
	if err != nil {
		return nil, err
	}

	// Update fields
	content.Text = req.Text
	content.Hint = req.Hint

	err = s.repo.UpdateContent(ctx, content)
	if err != nil {
		return nil, fmt.Errorf("failed to update option content: %w", err)
	}

	// Get updated content with language info
	updatedContent, err := s.repo.GetContentByOptionAndLanguage(ctx, optionID, languageID)
	if err != nil {
		return nil, err
	}

	return s.toContentResponse(updatedContent), nil
}

func (s *optionService) DeleteOptionContent(ctx context.Context, optionID uuid.UUID, languageID int) error {
	return s.repo.DeleteContent(ctx, optionID, languageID)
}

func (s *optionService) GetOptionsByInteraction(ctx context.Context, interactionID uuid.UUID, languageID *int) ([]*models.OptionResponse, error) {
	options, err := s.repo.GetByInteraction(ctx, interactionID, languageID)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.OptionResponse, len(options))
	for i, option := range options {
		responses[i] = s.toResponse(option)
	}

	return responses, nil
}

func (s *optionService) GetCorrectOptionsByInteraction(ctx context.Context, interactionID uuid.UUID, languageID *int) ([]*models.OptionResponse, error) {
	options, err := s.repo.GetCorrectByInteraction(ctx, interactionID, languageID)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.OptionResponse, len(options))
	for i, option := range options {
		responses[i] = s.toResponse(option)
	}

	return responses, nil
}

func (s *optionService) SearchOptions(ctx context.Context, query string, languageID *int, limit, offset int) ([]*models.OptionResponse, error) {
	options, err := s.repo.Search(ctx, query, languageID, limit, offset)
	if err != nil {
		return nil, err
	}

	responses := make([]*models.OptionResponse, len(options))
	for i, option := range options {
		responses[i] = s.toResponse(option)
	}

	return responses, nil
}

func (s *optionService) GetOptionStatistics(ctx context.Context) (*OptionStatistics, error) {
	totalCount, err := s.repo.Count(ctx, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get total option count: %w", err)
	}

	activeCount, err := s.repo.Count(ctx, true)
	if err != nil {
		return nil, fmt.Errorf("failed to get active option count: %w", err)
	}

	// Note: You might want to implement CountCorrect in repository for correct options count
	return &OptionStatistics{
		TotalOptions:   totalCount,
		ActiveOptions:  activeCount,
		CorrectOptions: 0, // Implement this by adding CountCorrect method to repository
	}, nil
}

func (s *optionService) ValidateOptionCorrectness(ctx context.Context, interactionID uuid.UUID) error {
	// Get total options for this interaction
	totalCount, err := s.repo.CountByInteraction(ctx, interactionID, false)
	if err != nil {
		return fmt.Errorf("failed to count total options: %w", err)
	}

	if totalCount == 0 {
		return errors.NewValidationError("interaction must have at least one option", nil)
	}

	// Get correct options for this interaction
	correctCount, err := s.repo.CountByInteraction(ctx, interactionID, true)
	if err != nil {
		return fmt.Errorf("failed to count correct options: %w", err)
	}

	if correctCount == 0 {
		return errors.NewValidationError("interaction must have at least one correct option", nil)
	}

	if correctCount == totalCount {
		return errors.NewValidationError("not all options can be correct - there must be at least one incorrect option", nil)
	}

	return nil
}

// Helper methods
func (s *optionService) toResponse(option *models.Option) *models.OptionResponse {
	response := &models.OptionResponse{
		ID:            option.ID,
		Code:          option.Code,
		IsCorrect:     option.IsCorrect,
		InteractionID: option.InteractionID,
		IsActive:      option.IsActive,
		CreatedBy:     option.CreatedBy,
		CreatedAt:     option.CreatedAt,
		UpdatedAt:     option.UpdatedAt,
	}

	if len(option.Contents) > 0 {
		response.Contents = make([]models.OptionContentResponse, len(option.Contents))
		for i, content := range option.Contents {
			response.Contents[i] = models.OptionContentResponse{
				ID:         content.ID,
				LanguageID: content.LanguageID,
				Text:       content.Text,
				Hint:       content.Hint,
				Language:   content.Language,
				CreatedAt:  content.CreatedAt,
				UpdatedAt:  content.UpdatedAt,
			}
		}
	}

	return response
}

func (s *optionService) toContentResponse(content *models.OptionContent) *models.OptionContentResponse {
	return &models.OptionContentResponse{
		ID:         content.ID,
		LanguageID: content.LanguageID,
		Text:       content.Text,
		Hint:       content.Hint,
		Language:   content.Language,
		CreatedAt:  content.CreatedAt,
		UpdatedAt:  content.UpdatedAt,
	}
}
