package models

import (
	"time"

	"github.com/google/uuid"
	i18nModels "github.com/psynarios/admin_back/internal/i18n/models"
)

// Core option entity (language-agnostic)
type Option struct {
	ID            uuid.UUID  `json:"id" db:"id"`
	Code          string     `json:"code" db:"code" validate:"required,max=50"`
	IsCorrect     bool       `json:"is_correct" db:"is_correct"`
	InteractionID uuid.UUID  `json:"interaction_id" db:"interaction_id"`
	IsActive      bool       `json:"is_active" db:"is_active"`
	CreatedBy     *uuid.UUID `json:"created_by,omitempty" db:"created_by"`
	CreatedAt     time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at" db:"updated_at"`

	// Relations - loaded based on context
	Contents []OptionContent `json:"contents,omitempty"`
}

// Language-specific content for options
type OptionContent struct {
	ID         uuid.UUID `json:"id" db:"id"`
	OptionID   uuid.UUID `json:"option_id" db:"option_id"`
	LanguageID int       `json:"language_id" db:"language_id"`
	Text       string    `json:"text" db:"text" validate:"required"`
	Hint       *string   `json:"hint,omitempty" db:"hint"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`

	// Relations
	Language *i18nModels.Language `json:"language,omitempty"`
}

// Request DTOs
type OptionCreateRequest struct {
	Code          string                 `json:"code" binding:"required,max=50"`
	IsCorrect     bool                   `json:"is_correct"`
	InteractionID uuid.UUID              `json:"interaction_id" binding:"required"`
	Contents      []OptionContentRequest `json:"contents" binding:"required,min=1,dive"`
}

type OptionUpdateRequest struct {
	Code          *string                `json:"code,omitempty" binding:"omitempty,max=50"`
	IsCorrect     *bool                  `json:"is_correct,omitempty"`
	InteractionID *uuid.UUID             `json:"interaction_id,omitempty"`
	IsActive      *bool                  `json:"is_active,omitempty"`
	Contents      []OptionContentRequest `json:"contents,omitempty" binding:"omitempty,dive"`
}

type OptionContentRequest struct {
	LanguageID int     `json:"language_id" binding:"required"`
	Text       string  `json:"text" binding:"required"`
	Hint       *string `json:"hint,omitempty"`
}

// Response DTOs
type OptionResponse struct {
	ID            uuid.UUID               `json:"id"`
	Code          string                  `json:"code"`
	IsCorrect     bool                    `json:"is_correct"`
	InteractionID uuid.UUID               `json:"interaction_id"`
	IsActive      bool                    `json:"is_active"`
	CreatedBy     *uuid.UUID              `json:"created_by,omitempty"`
	CreatedAt     time.Time               `json:"created_at"`
	UpdatedAt     time.Time               `json:"updated_at"`
	Contents      []OptionContentResponse `json:"contents,omitempty"`
}

type OptionContentResponse struct {
	ID         uuid.UUID            `json:"id"`
	LanguageID int                  `json:"language_id"`
	Text       string               `json:"text"`
	Hint       *string              `json:"hint,omitempty"`
	Language   *i18nModels.Language `json:"language,omitempty"`
	CreatedAt  time.Time            `json:"created_at"`
	UpdatedAt  time.Time            `json:"updated_at"`
}
