package models

import (
	"time"

	i18nModels "github.com/psynarios/admin_back/internal/i18n/models"
	"github.com/psynarios/admin_back/internal/library/enum"

	"github.com/google/uuid"
)

// Core scenario entity (language-agnostic)
type Scenario struct {
	ID              uuid.UUID            `json:"id" db:"id"`
	Code            string               `json:"code" db:"code" validate:"required,max=50"`
	DifficultyLevel enum.DifficultyLevel `json:"difficulty_level" db:"difficulty_level"`
	ScenarioType    enum.ScenarioType    `json:"scenario_type" db:"scenario_type"`
	CharacterType   enum.CharacterType   `json:"character_type" db:"character_type"`
	IsActive        bool                 `json:"is_active" db:"is_active"`
	CreatedBy       *uuid.UUID           `json:"created_by,omitempty" db:"created_by"`
	CreatedAt       time.Time            `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time            `json:"updated_at" db:"updated_at"`

	// Relations - loaded based on context
	Contents []ScenarioContent `json:"contents,omitempty"`
}

// Language-specific content for scenarios
type ScenarioContent struct {
	ID              uuid.UUID `json:"id" db:"id"`
	ScenarioID      uuid.UUID `json:"scenario_id" db:"scenario_id"`
	LanguageID      int       `json:"language_id" db:"language_id"`
	Title           string    `json:"title" db:"title" validate:"required,max=255"`
	Description     *string   `json:"description,omitempty" db:"description"`
	ImageURL        *string   `json:"image_url,omitempty" db:"image_url"`
	IntroVideoURL   *string   `json:"intro_video_url,omitempty" db:"intro_video_url"`
	ClosingVideoURL *string   `json:"closing_video_url,omitempty" db:"closing_video_url"`
	CreatedAt       time.Time `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time `json:"updated_at" db:"updated_at"`

	// Relations
	Language *i18nModels.Language `json:"language,omitempty"`
}

// Request structures
type CreateScenarioRequest struct {
	Code            string                         `json:"code" validate:"required,max=50"`
	DifficultyLevel enum.DifficultyLevel           `json:"difficulty_level" validate:"required"`
	ScenarioType    enum.ScenarioType              `json:"scenario_type" validate:"required"`
	CharacterType   enum.CharacterType             `json:"character_type"`
	CreatedBy       uuid.UUID                      `json:"created_by"`
	Contents        []CreateScenarioContentRequest `json:"contents" validate:"required,min=1"`
}

type CreateScenarioContentRequest struct {
	LanguageID      int     `json:"language_id" validate:"required"`
	Title           string  `json:"title" validate:"required,max=255"`
	Description     *string `json:"description"`
	ImageURL        *string `json:"image_url"`
	IntroVideoURL   *string `json:"intro_video_url"`
	ClosingVideoURL *string `json:"closing_video_url"`
}

type UpdateScenarioRequest struct {
	Code            string                         `json:"code" validate:"required,max=50"`
	DifficultyLevel enum.DifficultyLevel           `json:"difficulty_level" validate:"required"`
	ScenarioType    enum.ScenarioType              `json:"scenario_type" validate:"required"`
	CharacterType   enum.CharacterType             `json:"character_type"`
	Contents        []CreateScenarioContentRequest `json:"contents" validate:"required,min=1"`
}

type ScenarioListRequest struct {
	LanguageID *int `json:"language_id,omitempty"`
	Limit      int  `json:"limit" validate:"min=1,max=100"`
	Offset     int  `json:"offset" validate:"min=0"`
}

type ScenarioResponse struct {
	Scenario *Scenario `json:"scenario"`
}

type ScenarioListResponse struct {
	Scenarios []*Scenario `json:"scenarios"`
	Total     int         `json:"total"`
	Limit     int         `json:"limit"`
	Offset    int         `json:"offset"`
}
