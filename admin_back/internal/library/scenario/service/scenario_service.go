// internal/library/scenario/service/scenario_service.go
package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/psynarios/admin_back/internal/i18n/service"
	"github.com/psynarios/admin_back/internal/library/enum"
	"github.com/psynarios/admin_back/internal/library/scenario/models"
	"github.com/psynarios/admin_back/internal/library/scenario/repository"
	"github.com/rs/zerolog"
)

type ScenarioService struct {
	scenarioRepo repository.Repository
	i18nService  *service.I18nService
	logger       zerolog.Logger
}

func NewScenarioService(
	scenarioRepo repository.Repository,
	i18nService *service.I18nService,
	logger zerolog.Logger,
) *ScenarioService {
	return &ScenarioService{
		scenarioRepo: scenarioRepo,
		i18nService:  i18nService,
		logger:       logger.With().Str("component", "scenario_service").<PERSON><PERSON>(),
	}
}

// CreateScenario creates a new scenario with multilingual content
func (s *ScenarioService) CreateScenario(ctx context.Context, req *models.CreateScenarioRequest) (*models.Scenario, error) {
	s.logger.Info().
		Str("code", req.Code).
		Str("difficulty", string(req.DifficultyLevel)).
		Str("type", string(req.ScenarioType)).
		Int("content_count", len(req.Contents)).
		Msg("Creating scenario")

	// Validate languages
	for i, content := range req.Contents {
		if err := s.i18nService.ValidateLanguageID(ctx, content.LanguageID); err != nil {
			s.logger.Error().
				Err(err).
				Int("content_index", i).
				Int("language_id", content.LanguageID).
				Msg("Invalid language ID in content")
			return nil, fmt.Errorf("invalid language ID %d in content %d: %w", content.LanguageID, i, err)
		}
	}

	// Check if code already exists
	exists, err := s.scenarioRepo.CodeExists(ctx, req.Code)
	if err != nil {
		s.logger.Error().Err(err).Str("code", req.Code).Msg("Failed to check code existence")
		return nil, fmt.Errorf("failed to check code existence: %w", err)
	}
	if exists {
		s.logger.Warn().Str("code", req.Code).Msg("Scenario code already exists")
		return nil, fmt.Errorf("scenario code '%s' already exists", req.Code)
	}

	// Validate URLs if provided
	if err := s.validateMediaURLs(req.Contents); err != nil {
		s.logger.Error().Err(err).Msg("Invalid media URLs")
		return nil, fmt.Errorf("invalid media URLs: %w", err)
	}

	// Create core scenario
	scenario := &models.Scenario{
		Code:            req.Code,
		DifficultyLevel: req.DifficultyLevel,
		ScenarioType:    req.ScenarioType,
		CharacterType:   req.CharacterType,
		IsActive:        true,
		CreatedBy:       &req.CreatedBy,
	}

	err = s.scenarioRepo.Create(ctx, scenario)
	if err != nil {
		s.logger.Error().Err(err).Str("code", req.Code).Msg("Failed to create scenario")
		return nil, fmt.Errorf("failed to create scenario: %w", err)
	}

	// Create content for each language
	for i, contentReq := range req.Contents {
		content := &models.ScenarioContent{
			ScenarioID:      scenario.ID,
			LanguageID:      contentReq.LanguageID,
			Title:           contentReq.Title,
			Description:     contentReq.Description,
			ImageURL:        contentReq.ImageURL,
			IntroVideoURL:   contentReq.IntroVideoURL,
			ClosingVideoURL: contentReq.ClosingVideoURL,
		}

		err = s.scenarioRepo.CreateContent(ctx, content)
		if err != nil {
			s.logger.Error().
				Err(err).
				Str("scenario_id", scenario.ID.String()).
				Int("language_id", contentReq.LanguageID).
				Int("content_index", i).
				Msg("Failed to create scenario content")
			return nil, fmt.Errorf("failed to create content for language %d: %w", contentReq.LanguageID, err)
		}
	}

	s.logger.Info().
		Str("scenario_id", scenario.ID.String()).
		Str("code", scenario.Code).
		Msg("Scenario created successfully")

	// Return scenario with contents
	return s.GetScenarioByID(ctx, scenario.ID)
}

// GetScenarioByID retrieves a scenario by ID with all language contents
func (s *ScenarioService) GetScenarioByID(ctx context.Context, id uuid.UUID) (*models.Scenario, error) {
	s.logger.Debug().Str("scenario_id", id.String()).Msg("Getting scenario by ID")

	scenario, err := s.scenarioRepo.GetByIDWithContents(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Failed to get scenario")
		return nil, fmt.Errorf("failed to get scenario: %w", err)
	}

	s.logger.Debug().
		Str("scenario_id", id.String()).
		Str("code", scenario.Code).
		Int("content_count", len(scenario.Contents)).
		Msg("Scenario retrieved successfully")

	return scenario, nil
}

// GetScenarioByCode retrieves a scenario by code with all language contents
func (s *ScenarioService) GetScenarioByCode(ctx context.Context, code string) (*models.Scenario, error) {
	s.logger.Debug().Str("code", code).Msg("Getting scenario by code")

	scenario, err := s.scenarioRepo.GetByCodeWithContents(ctx, code)
	if err != nil {
		s.logger.Error().Err(err).Str("code", code).Msg("Failed to get scenario by code")
		return nil, fmt.Errorf("failed to get scenario: %w", err)
	}

	s.logger.Debug().
		Str("scenario_id", scenario.ID.String()).
		Str("code", code).
		Int("content_count", len(scenario.Contents)).
		Msg("Scenario retrieved by code successfully")

	return scenario, nil
}

// GetScenarioByLanguage retrieves a scenario with content for a specific language
func (s *ScenarioService) GetScenarioByLanguage(ctx context.Context, id uuid.UUID, languageID int) (*models.Scenario, error) {
	s.logger.Debug().
		Str("scenario_id", id.String()).
		Int("language_id", languageID).
		Msg("Getting scenario by language")

	if err := s.i18nService.ValidateLanguageID(ctx, languageID); err != nil {
		s.logger.Error().Err(err).Int("language_id", languageID).Msg("Invalid language ID")
		return nil, fmt.Errorf("invalid language ID: %w", err)
	}

	scenario, err := s.scenarioRepo.GetByIDWithLanguageContent(ctx, id, languageID)
	if err != nil {
		s.logger.Error().
			Err(err).
			Str("scenario_id", id.String()).
			Int("language_id", languageID).
			Msg("Failed to get scenario by language")
		return nil, fmt.Errorf("failed to get scenario for language: %w", err)
	}

	s.logger.Debug().
		Str("scenario_id", id.String()).
		Int("language_id", languageID).
		Msg("Scenario retrieved by language successfully")

	return scenario, nil
}

// ListScenarios retrieves scenarios with optional language filtering
func (s *ScenarioService) ListScenarios(ctx context.Context, req *models.ScenarioListRequest) (*models.ScenarioListResponse, error) {
	s.logger.Debug().
		Interface("request", req).
		Msg("Listing scenarios")

	var scenarios []*models.Scenario
	var err error

	// Validate language if specified
	if req.LanguageID != nil {
		if err := s.i18nService.ValidateLanguageID(ctx, *req.LanguageID); err != nil {
			s.logger.Error().Err(err).Int("language_id", *req.LanguageID).Msg("Invalid language ID")
			return nil, fmt.Errorf("invalid language ID: %w", err)
		}

		scenarios, err = s.scenarioRepo.ListByLanguage(ctx, *req.LanguageID, req.Limit, req.Offset)
	} else {
		scenarios, err = s.scenarioRepo.List(ctx, req.Limit, req.Offset)
	}

	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to list scenarios")
		return nil, fmt.Errorf("failed to list scenarios: %w", err)
	}

	// Get total count
	total, err := s.scenarioRepo.Count(ctx)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to count scenarios")
		return nil, fmt.Errorf("failed to count scenarios: %w", err)
	}

	s.logger.Debug().
		Int("count", len(scenarios)).
		Int("total", total).
		Int("limit", req.Limit).
		Int("offset", req.Offset).
		Msg("Scenarios listed successfully")

	return &models.ScenarioListResponse{
		Scenarios: scenarios,
		Total:     total,
		Limit:     req.Limit,
		Offset:    req.Offset,
	}, nil
}

// UpdateScenario updates an existing scenario and its content
func (s *ScenarioService) UpdateScenario(ctx context.Context, id uuid.UUID, req *models.UpdateScenarioRequest) (*models.Scenario, error) {
	s.logger.Info().
		Str("scenario_id", id.String()).
		Str("code", req.Code).
		Msg("Updating scenario")

	// Check if scenario exists
	existing, err := s.scenarioRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Scenario not found")
		return nil, fmt.Errorf("scenario not found: %w", err)
	}

	// Validate languages
	for i, content := range req.Contents {
		if err := s.i18nService.ValidateLanguageID(ctx, content.LanguageID); err != nil {
			s.logger.Error().
				Err(err).
				Int("content_index", i).
				Int("language_id", content.LanguageID).
				Msg("Invalid language ID in content")
			return nil, fmt.Errorf("invalid language ID %d in content %d: %w", content.LanguageID, i, err)
		}
	}

	// Check if code conflicts with other scenarios
	if req.Code != existing.Code {
		exists, err := s.scenarioRepo.CodeExists(ctx, req.Code)
		if err != nil {
			s.logger.Error().Err(err).Str("code", req.Code).Msg("Failed to check code existence")
			return nil, fmt.Errorf("failed to check code existence: %w", err)
		}
		if exists {
			s.logger.Warn().Str("code", req.Code).Msg("Scenario code already exists")
			return nil, fmt.Errorf("scenario code '%s' already exists", req.Code)
		}
	}

	// Validate URLs if provided
	if err := s.validateMediaURLs(req.Contents); err != nil {
		s.logger.Error().Err(err).Msg("Invalid media URLs")
		return nil, fmt.Errorf("invalid media URLs: %w", err)
	}

	// Update core scenario
	existing.Code = req.Code
	existing.DifficultyLevel = req.DifficultyLevel
	existing.ScenarioType = req.ScenarioType
	existing.CharacterType = req.CharacterType

	err = s.scenarioRepo.Update(ctx, existing)
	if err != nil {
		s.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Failed to update scenario")
		return nil, fmt.Errorf("failed to update scenario: %w", err)
	}

	// Delete existing contents
	err = s.scenarioRepo.DeleteContents(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Failed to delete existing contents")
		return nil, fmt.Errorf("failed to delete existing contents: %w", err)
	}

	// Create new contents
	for i, contentReq := range req.Contents {
		content := &models.ScenarioContent{
			ScenarioID:      id,
			LanguageID:      contentReq.LanguageID,
			Title:           contentReq.Title,
			Description:     contentReq.Description,
			ImageURL:        contentReq.ImageURL,
			IntroVideoURL:   contentReq.IntroVideoURL,
			ClosingVideoURL: contentReq.ClosingVideoURL,
		}

		err = s.scenarioRepo.CreateContent(ctx, content)
		if err != nil {
			s.logger.Error().
				Err(err).
				Str("scenario_id", id.String()).
				Int("language_id", contentReq.LanguageID).
				Int("content_index", i).
				Msg("Failed to create scenario content")
			return nil, fmt.Errorf("failed to create content for language %d: %w", contentReq.LanguageID, err)
		}
	}

	s.logger.Info().
		Str("scenario_id", id.String()).
		Str("code", req.Code).
		Msg("Scenario updated successfully")

	return s.GetScenarioByID(ctx, id)
}

// DeleteScenario soft deletes a scenario
func (s *ScenarioService) DeleteScenario(ctx context.Context, id uuid.UUID) error {
	s.logger.Info().Str("scenario_id", id.String()).Msg("Deleting scenario")

	// Check if scenario exists
	_, err := s.scenarioRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Scenario not found")
		return fmt.Errorf("scenario not found: %w", err)
	}

	// Soft delete (set is_active to false)
	err = s.scenarioRepo.SoftDelete(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("scenario_id", id.String()).Msg("Failed to delete scenario")
		return fmt.Errorf("failed to delete scenario: %w", err)
	}

	s.logger.Info().Str("scenario_id", id.String()).Msg("Scenario deleted successfully")
	return nil
}

// GetScenariosByDifficulty retrieves scenarios filtered by difficulty level
func (s *ScenarioService) GetScenariosByDifficulty(ctx context.Context, difficulty enum.DifficultyLevel, languageID *int, limit, offset int) (*models.ScenarioListResponse, error) {
	s.logger.Debug().
		Str("difficulty", string(difficulty)).
		Interface("language_id", languageID).
		Int("limit", limit).
		Int("offset", offset).
		Msg("Getting scenarios by difficulty")

	// This would require a new repository method
	// For now, we'll get all scenarios and filter in memory (not optimal for production)
	allScenarios, err := s.scenarioRepo.List(ctx, 1000, 0) // Get a large batch
	if err != nil {
		return nil, fmt.Errorf("failed to get scenarios: %w", err)
	}

	var filteredScenarios []*models.Scenario
	for _, scenario := range allScenarios {
		if scenario.DifficultyLevel == difficulty {
			filteredScenarios = append(filteredScenarios, scenario)
		}
	}

	// Apply pagination to filtered results
	start := offset
	end := offset + limit
	if start > len(filteredScenarios) {
		start = len(filteredScenarios)
	}
	if end > len(filteredScenarios) {
		end = len(filteredScenarios)
	}

	paginatedScenarios := filteredScenarios[start:end]

	return &models.ScenarioListResponse{
		Scenarios: paginatedScenarios,
		Total:     len(filteredScenarios),
		Limit:     limit,
		Offset:    offset,
	}, nil
}

// GetScenariosByType retrieves scenarios filtered by type
func (s *ScenarioService) GetScenariosByType(ctx context.Context, scenarioType enum.ScenarioType, languageID *int, limit, offset int) (*models.ScenarioListResponse, error) {
	s.logger.Debug().
		Str("type", string(scenarioType)).
		Interface("language_id", languageID).
		Int("limit", limit).
		Int("offset", offset).
		Msg("Getting scenarios by type")

	// Similar to difficulty filtering - would benefit from a dedicated repository method
	allScenarios, err := s.scenarioRepo.List(ctx, 1000, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get scenarios: %w", err)
	}

	var filteredScenarios []*models.Scenario
	for _, scenario := range allScenarios {
		if scenario.ScenarioType == scenarioType {
			filteredScenarios = append(filteredScenarios, scenario)
		}
	}

	// Apply pagination
	start := offset
	end := offset + limit
	if start > len(filteredScenarios) {
		start = len(filteredScenarios)
	}
	if end > len(filteredScenarios) {
		end = len(filteredScenarios)
	}

	paginatedScenarios := filteredScenarios[start:end]

	return &models.ScenarioListResponse{
		Scenarios: paginatedScenarios,
		Total:     len(filteredScenarios),
		Limit:     limit,
		Offset:    offset,
	}, nil
}

// validateMediaURLs validates that provided URLs are properly formatted
func (s *ScenarioService) validateMediaURLs(contents []models.CreateScenarioContentRequest) error {
	for i, content := range contents {
		urls := []*string{content.ImageURL, content.IntroVideoURL, content.ClosingVideoURL}
		urlNames := []string{"image_url", "intro_video_url", "closing_video_url"}

		for j, url := range urls {
			if url != nil && *url != "" {
				if !s.isValidURL(*url) {
					return fmt.Errorf("invalid %s in content %d: %s", urlNames[j], i, *url)
				}
			}
		}
	}
	return nil
}

// isValidURL performs basic URL validation
func (s *ScenarioService) isValidURL(url string) bool {
	// Basic validation - you might want to use a more sophisticated URL validator
	if len(url) == 0 {
		return false
	}
	if len(url) >= 8 && url[:8] == "https://" {
		return true
	}
	if len(url) >= 7 && url[:7] == "http://" {
		return true
	}
	if url[0] == '/' {
		return true
	}
	return false
}

// GetScenarioStats returns statistics about scenarios
func (s *ScenarioService) GetScenarioStats(ctx context.Context) (map[string]interface{}, error) {
	s.logger.Debug().Msg("Getting scenario statistics")

	total, err := s.scenarioRepo.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get total count: %w", err)
	}

	// Get all scenarios to calculate stats
	allScenarios, err := s.scenarioRepo.List(ctx, 1000, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get scenarios for stats: %w", err)
	}

	// Calculate difficulty distribution
	difficultyStats := make(map[enum.DifficultyLevel]int)
	typeStats := make(map[enum.ScenarioType]int)

	for _, scenario := range allScenarios {
		difficultyStats[scenario.DifficultyLevel]++
		typeStats[scenario.ScenarioType]++
	}

	stats := map[string]interface{}{
		"total":                total,
		"difficulty_breakdown": difficultyStats,
		"type_breakdown":       typeStats,
		"last_updated":         time.Now(),
	}

	s.logger.Debug().Interface("stats", stats).Msg("Scenario statistics calculated")

	return stats, nil
}
