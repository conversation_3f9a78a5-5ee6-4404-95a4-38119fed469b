package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	i18nModels "github.com/psynarios/admin_back/internal/i18n/models"
	"github.com/psynarios/admin_back/internal/library/scenario/models"
	"github.com/psynarios/admin_back/internal/library/scenario/repository"
)

// scenarioRepository implements the Repository interface
type scenarioRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

// NewScenarioRepository creates a new scenario repository
func NewScenarioRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.Repository {
	return &scenarioRepository{
		db:     db,
		logger: logger.With().Str("component", "scenario_repository").Logger(),
	}
}

func (r *scenarioRepository) Create(ctx context.Context, scenario *models.Scenario) error {
	query := `
        INSERT INTO scenarios (id, code, difficulty_level, scenario_type, character_type, 
                             is_active, created_by, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING id, created_at, updated_at`

	now := time.Now()
	scenario.ID = uuid.New()
	scenario.CreatedAt = now
	scenario.UpdatedAt = now

	err := r.db.QueryRow(
		ctx,
		query,
		scenario.ID,
		scenario.Code,
		scenario.DifficultyLevel,
		scenario.ScenarioType,
		scenario.CharacterType,
		scenario.IsActive,
		scenario.CreatedBy,
		scenario.CreatedAt,
		scenario.UpdatedAt,
	).Scan(&scenario.ID, &scenario.CreatedAt, &scenario.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create scenario: %w", err)
	}

	r.logger.Info().Str("id", scenario.ID.String()).Str("code", scenario.Code).
		Msg("Scenario created successfully")

	return nil
}

func (r *scenarioRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Scenario, error) {
	query := `
        SELECT id, code, difficulty_level, scenario_type, character_type, is_active, 
               created_by, created_at, updated_at
        FROM scenarios
        WHERE id = $1 AND is_active = true`

	var scenario models.Scenario
	row := r.db.QueryRow(ctx, query, id)

	err := row.Scan(
		&scenario.ID,
		&scenario.Code,
		&scenario.DifficultyLevel,
		&scenario.ScenarioType,
		&scenario.CharacterType,
		&scenario.IsActive,
		&scenario.CreatedBy,
		&scenario.CreatedAt,
		&scenario.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("scenario with id %s not found", id)
		}
		return nil, fmt.Errorf("failed to get scenario: %w", err)
	}

	return &scenario, nil
}

func (r *scenarioRepository) GetByIDWithContents(ctx context.Context, id uuid.UUID) (*models.Scenario, error) {
	scenario, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	contents, err := r.GetContentsWithLanguage(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get contents: %w", err)
	}

	scenario.Contents = contents
	return scenario, nil
}

func (r *scenarioRepository) GetContentsWithLanguage(ctx context.Context, scenarioID uuid.UUID) ([]models.ScenarioContent, error) {
	query := `
        SELECT 
            sc.id, sc.scenario_id, sc.language_id, sc.title, sc.description, 
            sc.image_url, sc.intro_video_url, sc.closing_video_url,
            sc.created_at, sc.updated_at,
            l.id, l.code, l.name, l.native_name, l.is_active, l.is_default
        FROM scenario_contents sc
        LEFT JOIN languages l ON sc.language_id = l.id
        WHERE sc.scenario_id = $1
        ORDER BY l.is_default DESC, l.name ASC`

	rows, err := r.db.Query(ctx, query, scenarioID)
	if err != nil {
		return nil, fmt.Errorf("failed to get contents with language: %w", err)
	}
	defer rows.Close()

	var contents []models.ScenarioContent

	for rows.Next() {
		var content models.ScenarioContent
		var language i18nModels.Language

		err := rows.Scan(
			&content.ID, &content.ScenarioID, &content.LanguageID,
			&content.Title, &content.Description, &content.ImageURL,
			&content.IntroVideoURL, &content.ClosingVideoURL,
			&content.CreatedAt, &content.UpdatedAt,
			&language.ID, &language.Code, &language.Name,
			&language.NativeName, &language.IsActive, &language.IsDefault,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to scan content: %w", err)
		}

		content.Language = &language
		contents = append(contents, content)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating contents: %w", err)
	}

	return contents, nil
}

func (r *scenarioRepository) GetByCode(ctx context.Context, code string) (*models.Scenario, error) {
	query := `
        SELECT id, code, difficulty_level, scenario_type, character_type, is_active, 
               created_by, created_at, updated_at
        FROM scenarios
        WHERE code = $1 AND is_active = true`

	var scenario models.Scenario
	row := r.db.QueryRow(ctx, query, code)

	err := row.Scan(
		&scenario.ID,
		&scenario.Code,
		&scenario.DifficultyLevel,
		&scenario.ScenarioType,
		&scenario.CharacterType,
		&scenario.IsActive,
		&scenario.CreatedBy,
		&scenario.CreatedAt,
		&scenario.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("scenario with code %s not found", code)
		}
		return nil, fmt.Errorf("failed to get scenario: %w", err)
	}

	return &scenario, nil
}

func (r *scenarioRepository) CreateContent(ctx context.Context, content *models.ScenarioContent) error {
	query := `
        INSERT INTO scenario_contents (id, scenario_id, language_id, title, description,
                                     image_url, intro_video_url, closing_video_url, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id, created_at, updated_at`

	now := time.Now()
	content.ID = uuid.New()
	content.CreatedAt = now
	content.UpdatedAt = now

	err := r.db.QueryRow(
		ctx,
		query,
		content.ID,
		content.ScenarioID,
		content.LanguageID,
		content.Title,
		content.Description,
		content.ImageURL,
		content.IntroVideoURL,
		content.ClosingVideoURL,
		content.CreatedAt,
		content.UpdatedAt,
	).Scan(&content.ID, &content.CreatedAt, &content.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create scenario content: %w", err)
	}

	r.logger.Info().Str("id", content.ID.String()).
		Str("scenario_id", content.ScenarioID.String()).
		Int("language_id", content.LanguageID).
		Msg("Scenario content created successfully")

	return nil
}

func (r *scenarioRepository) CodeExists(ctx context.Context, code string) (bool, error) {
	query := `SELECT COUNT(*) FROM scenarios WHERE code = $1 AND is_active = true`

	var count int
	err := r.db.QueryRow(ctx, query, code).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("failed to check code existence: %w", err)
	}

	return count > 0, nil
}

func (r *scenarioRepository) Update(ctx context.Context, scenario *models.Scenario) error {
	query := `
        UPDATE scenarios
        SET code = $1, difficulty_level = $2, scenario_type = $3, character_type = $4, updated_at = $5
        WHERE id = $6
        RETURNING id, code, difficulty_level, scenario_type, character_type, is_active,
                 created_by, created_at, updated_at`

	scenario.UpdatedAt = time.Now()

	row := r.db.QueryRow(
		ctx,
		query,
		scenario.Code,
		scenario.DifficultyLevel,
		scenario.ScenarioType,
		scenario.CharacterType,
		scenario.UpdatedAt,
		scenario.ID,
	)

	err := row.Scan(
		&scenario.ID,
		&scenario.Code,
		&scenario.DifficultyLevel,
		&scenario.ScenarioType,
		&scenario.CharacterType,
		&scenario.IsActive,
		&scenario.CreatedBy,
		&scenario.CreatedAt,
		&scenario.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return fmt.Errorf("scenario with id %s not found", scenario.ID)
		}
		return fmt.Errorf("failed to update scenario: %w", err)
	}

	r.logger.Info().Str("id", scenario.ID.String()).Str("code", scenario.Code).
		Msg("Scenario updated successfully")

	return nil
}

func (r *scenarioRepository) DeleteContents(ctx context.Context, scenarioID uuid.UUID) error {
	query := `DELETE FROM scenario_contents WHERE scenario_id = $1`

	result, err := r.db.Exec(ctx, query, scenarioID)
	if err != nil {
		return fmt.Errorf("failed to delete contents: %w", err)
	}

	rowsAffected := result.RowsAffected()
	r.logger.Info().Str("scenario_id", scenarioID.String()).
		Int64("rows_affected", rowsAffected).
		Msg("Scenario contents deleted")

	return nil
}

func (r *scenarioRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	query := `
        UPDATE scenarios
        SET is_active = false, updated_at = $1
        WHERE id = $2`

	now := time.Now()
	result, err := r.db.Exec(ctx, query, now, id)
	if err != nil {
		return fmt.Errorf("failed to soft delete scenario: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("scenario with id %s not found", id)
	}

	r.logger.Info().Str("id", id.String()).Msg("Scenario soft deleted successfully")
	return nil
}

func (r *scenarioRepository) List(ctx context.Context, limit, offset int) ([]*models.Scenario, error) {
	query := `
        SELECT id, code, difficulty_level, scenario_type, character_type, is_active,
               created_by, created_at, updated_at
        FROM scenarios
        WHERE is_active = true
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2`

	rows, err := r.db.Query(ctx, query, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to list scenarios: %w", err)
	}
	defer rows.Close()

	var scenarios []*models.Scenario
	for rows.Next() {
		var scenario models.Scenario
		err := rows.Scan(
			&scenario.ID,
			&scenario.Code,
			&scenario.DifficultyLevel,
			&scenario.ScenarioType,
			&scenario.CharacterType,
			&scenario.IsActive,
			&scenario.CreatedBy,
			&scenario.CreatedAt,
			&scenario.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan scenario: %w", err)
		}
		scenarios = append(scenarios, &scenario)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating scenarios: %w", err)
	}

	return scenarios, nil
}

func (r *scenarioRepository) ListByLanguage(ctx context.Context, languageID int, limit, offset int) ([]*models.Scenario, error) {
	query := `
        SELECT DISTINCT s.id, s.code, s.difficulty_level, s.scenario_type, s.character_type,
               s.is_active, s.created_by, s.created_at, s.updated_at
        FROM scenarios s
        INNER JOIN scenario_contents sc ON s.id = sc.scenario_id
        WHERE s.is_active = true AND sc.language_id = $1
        ORDER BY s.created_at DESC
        LIMIT $2 OFFSET $3`

	rows, err := r.db.Query(ctx, query, languageID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get scenarios by language: %w", err)
	}
	defer rows.Close()

	var scenarios []*models.Scenario
	for rows.Next() {
		var scenario models.Scenario
		err := rows.Scan(
			&scenario.ID,
			&scenario.Code,
			&scenario.DifficultyLevel,
			&scenario.ScenarioType,
			&scenario.CharacterType,
			&scenario.IsActive,
			&scenario.CreatedBy,
			&scenario.CreatedAt,
			&scenario.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan scenario: %w", err)
		}
		scenarios = append(scenarios, &scenario)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating scenarios: %w", err)
	}

	return scenarios, nil
}

func (r *scenarioRepository) Count(ctx context.Context) (int, error) {
	query := `SELECT COUNT(*) FROM scenarios WHERE is_active = true`

	var count int
	err := r.db.QueryRow(ctx, query).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count scenarios: %w", err)
	}

	return count, nil
}

func (r *scenarioRepository) GetByCodeWithContents(ctx context.Context, code string) (*models.Scenario, error) {
	scenario, err := r.GetByCode(ctx, code)
	if err != nil {
		return nil, err
	}

	contents, err := r.GetContentsWithLanguage(ctx, scenario.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get contents: %w", err)
	}

	scenario.Contents = contents
	return scenario, nil
}

func (r *scenarioRepository) GetByIDWithLanguageContent(ctx context.Context, id uuid.UUID, languageID int) (*models.Scenario, error) {
	scenario, err := r.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	content, err := r.GetContentByScenarioAndLanguage(ctx, id, languageID)
	if err != nil {
		return nil, fmt.Errorf("failed to get content for language: %w", err)
	}

	if content != nil {
		scenario.Contents = []models.ScenarioContent{*content}
	} else {
		scenario.Contents = []models.ScenarioContent{}
	}
	return scenario, nil
}

func (r *scenarioRepository) GetContentsByScenarioID(ctx context.Context, scenarioID uuid.UUID) ([]*models.ScenarioContent, error) {
	query := `
        SELECT id, scenario_id, language_id, title, description, image_url, 
               intro_video_url, closing_video_url, created_at, updated_at
        FROM scenario_contents
        WHERE scenario_id = $1
        ORDER BY language_id`

	rows, err := r.db.Query(ctx, query, scenarioID)
	if err != nil {
		return nil, fmt.Errorf("failed to get contents: %w", err)
	}
	defer rows.Close()

	var contents []*models.ScenarioContent
	for rows.Next() {
		var content models.ScenarioContent
		err := rows.Scan(
			&content.ID,
			&content.ScenarioID,
			&content.LanguageID,
			&content.Title,
			&content.Description,
			&content.ImageURL,
			&content.IntroVideoURL,
			&content.ClosingVideoURL,
			&content.CreatedAt,
			&content.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan content: %w", err)
		}
		contents = append(contents, &content)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating contents: %w", err)
	}

	return contents, nil
}

func (r *scenarioRepository) GetContentByScenarioAndLanguage(ctx context.Context, scenarioID uuid.UUID, languageID int) (*models.ScenarioContent, error) {
	query := `
        SELECT sc.id, sc.scenario_id, sc.language_id, sc.title, sc.description, 
               sc.image_url, sc.intro_video_url, sc.closing_video_url,
               sc.created_at, sc.updated_at,
               l.id, l.code, l.name, l.native_name, l.is_active, l.is_default
        FROM scenario_contents sc
        LEFT JOIN languages l ON sc.language_id = l.id
        WHERE sc.scenario_id = $1 AND sc.language_id = $2`

	var content models.ScenarioContent
	var language i18nModels.Language

	row := r.db.QueryRow(ctx, query, scenarioID, languageID)
	err := row.Scan(
		&content.ID, &content.ScenarioID, &content.LanguageID,
		&content.Title, &content.Description, &content.ImageURL,
		&content.IntroVideoURL, &content.ClosingVideoURL,
		&content.CreatedAt, &content.UpdatedAt,
		&language.ID, &language.Code, &language.Name,
		&language.NativeName, &language.IsActive, &language.IsDefault,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("content not found for scenario %s and language %d", scenarioID, languageID)
		}
		return nil, fmt.Errorf("failed to get content: %w", err)
	}

	content.Language = &language
	return &content, nil
}

func (r *scenarioRepository) UpdateContent(ctx context.Context, content *models.ScenarioContent) error {
	query := `
        UPDATE scenario_contents
        SET title = $1, description = $2, image_url = $3, intro_video_url = $4,
            closing_video_url = $5, updated_at = $6
        WHERE scenario_id = $7 AND language_id = $8
        RETURNING id, scenario_id, language_id, title, description, image_url,
                 intro_video_url, closing_video_url, created_at, updated_at`

	content.UpdatedAt = time.Now()

	row := r.db.QueryRow(
		ctx,
		query,
		content.Title,
		content.Description,
		content.ImageURL,
		content.IntroVideoURL,
		content.ClosingVideoURL,
		content.UpdatedAt,
		content.ScenarioID,
		content.LanguageID,
	)

	err := row.Scan(
		&content.ID,
		&content.ScenarioID,
		&content.LanguageID,
		&content.Title,
		&content.Description,
		&content.ImageURL,
		&content.IntroVideoURL,
		&content.ClosingVideoURL,
		&content.CreatedAt,
		&content.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return fmt.Errorf("content not found for scenario %s and language %d", content.ScenarioID, content.LanguageID)
		}
		return fmt.Errorf("failed to update content: %w", err)
	}

	r.logger.Info().Str("scenario_id", content.ScenarioID.String()).
		Int("language_id", content.LanguageID).
		Msg("Scenario content updated successfully")

	return nil
}

func (r *scenarioRepository) Delete(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM scenarios WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete scenario: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("scenario with id %s not found", id)
	}

	r.logger.Info().Str("id", id.String()).Msg("Scenario deleted successfully")
	return nil
}
