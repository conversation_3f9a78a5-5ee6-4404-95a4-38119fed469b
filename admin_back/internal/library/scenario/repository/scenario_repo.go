package repository

import (
	"context"

	"github.com/google/uuid"
	"github.com/psynarios/admin_back/internal/library/scenario/models"
)

type Repository interface {
	// Core scenario operations
	Create(ctx context.Context, scenario *models.Scenario) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Scenario, error)
	GetByIDWithContents(ctx context.Context, id uuid.UUID) (*models.Scenario, error)
	GetByIDWithLanguageContent(ctx context.Context, id uuid.UUID, languageID int) (*models.Scenario, error)
	GetByCode(ctx context.Context, code string) (*models.Scenario, error)
	GetByCodeWithContents(ctx context.Context, code string) (*models.Scenario, error)
	List(ctx context.Context, limit, offset int) ([]*models.Scenario, error)
	ListByLanguage(ctx context.Context, languageID int, limit, offset int) ([]*models.Scenario, error)
	Count(ctx context.Context) (int, error)
	Update(ctx context.Context, scenario *models.Scenario) error
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	CodeExists(ctx context.Context, code string) (bool, error)

	// Content operations
	CreateContent(ctx context.Context, content *models.ScenarioContent) error
	GetContentsByScenarioID(ctx context.Context, scenarioID uuid.UUID) ([]*models.ScenarioContent, error)
	GetContentByScenarioAndLanguage(ctx context.Context, scenarioID uuid.UUID, languageID int) (*models.ScenarioContent, error)
	UpdateContent(ctx context.Context, content *models.ScenarioContent) error
	DeleteContents(ctx context.Context, scenarioID uuid.UUID) error
}
