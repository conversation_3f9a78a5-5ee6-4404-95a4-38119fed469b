// internal/common/config/loader.go
package config

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/common/database"
)

// Config holds the application configuration
type Config struct {
	Environment string
	Server      ServerConfig
	Database    database.Config
	Auth        AuthConfig
	Email       EmailConfig
	Logger      LoggerConfig
	Storage     StorageConfig
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Host         string
	Port         string
	Address      string // Combined host:port for convenience
	Domain       string // For cookie domain settings
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
}

// Enhanced AuthConfig with additional security features
type AuthConfig struct {
	// JWT Configuration
	AccessSecret    string
	RefreshSecret   string
	AccessLifetime  time.Duration
	RefreshLifetime time.Duration

	// Cookie Configuration
	CookieDomain   string
	CookieSecure   bool
	CookieHTTPOnly bool
	CookieSameSite string // "Strict", "Lax", "None"

	// CSRF Configuration
	CSRFEnabled bool
	CSRFSecret  string

	// Token refresh settings
	RefreshThreshold time.Duration // When to proactively refresh tokens

	// Rate limiting
	LoginRateLimit   int
	RefreshRateLimit int
}

// EmailConfig holds email configuration
type EmailConfig struct {
	DefaultSender  string
	DefaultName    string
	SMTPHost       string
	SMTPPort       int
	SMTPUsername   string
	SMTPPassword   string
	UseSSL         bool
	ConnectTimeout time.Duration
}

// LoggerConfig holds logger configuration
type LoggerConfig struct {
	Level      string
	TimeFormat string
	Console    bool
	File       string
}

type StorageConfig struct {
	BasePath    string
	BaseURL     string
	MaxFileSize int64
}

// LoadConfig loads configuration from environment variables
func LoadConfig(logger zerolog.Logger) (*Config, error) {
	// Load .env file if it exists
	err := godotenv.Load()
	if err != nil && !os.IsNotExist(err) {
		logger.Warn().Err(err).Msg("Error loading .env file")
	}

	cfg := &Config{
		Environment: getEnv("APP_ENV", "development"),
		Server: ServerConfig{
			Host:         getEnv("SERVER_HOST", "0.0.0.0"),
			Port:         getEnv("SERVER_PORT", "8080"),
			Domain:       getEnv("SERVER_DOMAIN", ""),
			ReadTimeout:  getDurationEnv("SERVER_READ_TIMEOUT", 15*time.Second),
			WriteTimeout: getDurationEnv("SERVER_WRITE_TIMEOUT", 15*time.Second),
			IdleTimeout:  getDurationEnv("SERVER_IDLE_TIMEOUT", 60*time.Second),
		},
		Database: database.Config{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "postgres"),
			DBName:   getEnv("DB_NAME", "admin_back"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		Auth: AuthConfig{
			// JWT Configuration
			AccessSecret:    getEnv("AUTH_ACCESS_SECRET", generateDefaultSecret("access")),
			RefreshSecret:   getEnv("AUTH_REFRESH_SECRET", generateDefaultSecret("refresh")),
			AccessLifetime:  getDurationEnv("AUTH_ACCESS_LIFETIME", 15*time.Minute),
			RefreshLifetime: getDurationEnv("AUTH_REFRESH_LIFETIME", 7*24*time.Hour),

			// Cookie Configuration
			CookieDomain:   getEnv("AUTH_COOKIE_DOMAIN", ""),
			CookieSecure:   getBoolEnv("AUTH_COOKIE_SECURE", getDefaultSecure()),
			CookieHTTPOnly: getBoolEnv("AUTH_COOKIE_HTTP_ONLY", true),
			CookieSameSite: getEnv("AUTH_COOKIE_SAME_SITE", "Lax"),

			// CSRF Configuration
			CSRFEnabled: getBoolEnv("AUTH_CSRF_ENABLED", true),
			CSRFSecret:  getEnv("AUTH_CSRF_SECRET", generateDefaultSecret("csrf")),

			// Token refresh settings
			RefreshThreshold: getDurationEnv("AUTH_REFRESH_THRESHOLD", 5*time.Minute),

			// Rate limiting
			LoginRateLimit:   getIntEnv("AUTH_LOGIN_RATE_LIMIT", 5),
			RefreshRateLimit: getIntEnv("AUTH_REFRESH_RATE_LIMIT", 10),
		},
		Email: EmailConfig{
			DefaultSender:  getEnv("EMAIL_DEFAULT_SENDER", "<EMAIL>"),
			DefaultName:    getEnv("EMAIL_DEFAULT_NAME", "System"),
			SMTPHost:       getEnv("EMAIL_SMTP_HOST", "smtp.example.com"),
			SMTPPort:       getIntEnv("EMAIL_SMTP_PORT", 587),
			SMTPUsername:   getEnv("EMAIL_SMTP_USERNAME", "user"),
			SMTPPassword:   getEnv("EMAIL_SMTP_PASSWORD", "password"),
			UseSSL:         getBoolEnv("EMAIL_USE_SSL", false),
			ConnectTimeout: getDurationEnv("EMAIL_CONNECT_TIMEOUT", 10*time.Second),
		},
		Logger: LoggerConfig{
			Level:      getEnv("LOG_LEVEL", "info"),
			TimeFormat: getEnv("LOG_TIME_FORMAT", time.RFC3339),
			Console:    getBoolEnv("LOG_CONSOLE", true),
			File:       getEnv("LOG_FILE", ""),
		},
		Storage: StorageConfig{
			BasePath:    getEnv("STORAGE_BASE_PATH", "./uploads"),
			BaseURL:     getEnv("STORAGE_BASE_URL", "/api/media/files"),
			MaxFileSize: int64(getIntEnv("STORAGE_MAX_FILE_SIZE", 100*1024*1024)),
		},
	}

	// Set combined server address
	cfg.Server.Address = cfg.Server.Host + ":" + cfg.Server.Port

	// Validate configuration
	if err := cfg.Validate(logger); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	logger.Info().
		Str("environment", cfg.Environment).
		Str("server_address", cfg.Server.Address).
		Bool("csrf_enabled", cfg.Auth.CSRFEnabled).
		Dur("access_lifetime", cfg.Auth.AccessLifetime).
		Dur("refresh_lifetime", cfg.Auth.RefreshLifetime).
		Msg("Configuration loaded successfully")

	return cfg, nil
}

// Validate checks if the configuration is valid
func (c *Config) Validate(logger zerolog.Logger) error {
	// Validate JWT secrets
	if len(c.Auth.AccessSecret) < 32 {
		return fmt.Errorf("AUTH_ACCESS_SECRET must be at least 32 characters long")
	}

	if len(c.Auth.RefreshSecret) < 32 {
		return fmt.Errorf("AUTH_REFRESH_SECRET must be at least 32 characters long")
	}

	if c.Auth.AccessSecret == c.Auth.RefreshSecret {
		return fmt.Errorf("access and refresh secrets must be different")
	}

	// Validate token lifetimes
	if c.Auth.AccessLifetime <= 0 {
		return fmt.Errorf("AUTH_ACCESS_LIFETIME must be positive")
	}

	if c.Auth.RefreshLifetime <= 0 {
		return fmt.Errorf("AUTH_REFRESH_LIFETIME must be positive")
	}

	if c.Auth.RefreshLifetime <= c.Auth.AccessLifetime {
		return fmt.Errorf("refresh token lifetime must be longer than access token lifetime")
	}

	// Validate CSRF configuration
	if c.Auth.CSRFEnabled && len(c.Auth.CSRFSecret) < 16 {
		return fmt.Errorf("AUTH_CSRF_SECRET must be at least 16 characters long when CSRF is enabled")
	}

	// Validate SameSite cookie setting
	validSameSite := map[string]bool{
		"Strict": true,
		"Lax":    true,
		"None":   true,
	}
	if !validSameSite[c.Auth.CookieSameSite] {
		return fmt.Errorf("AUTH_COOKIE_SAME_SITE must be one of: Strict, Lax, None")
	}

	// Warn about insecure development settings
	if c.Environment == "development" {
		if !c.Auth.CookieSecure {
			logger.Warn().Msg("Running in development mode with insecure cookies (this is normal for localhost)")
		}
		if c.Auth.AccessSecret == generateDefaultSecret("access") {
			logger.Warn().Msg("Using default access secret (change this in production)")
		}
		if c.Auth.RefreshSecret == generateDefaultSecret("refresh") {
			logger.Warn().Msg("Using default refresh secret (change this in production)")
		}
	}

	return nil
}

// GetCookieSameSite returns the http.SameSite value for the configured string
func (c *Config) GetCookieSameSite() http.SameSite {
	switch c.Auth.CookieSameSite {
	case "Strict":
		return http.SameSiteStrictMode
	case "Lax":
		return http.SameSiteLaxMode
	case "None":
		return http.SameSiteNoneMode
	default:
		return http.SameSiteLaxMode
	}
}

// GetEffectiveCookieDomain returns the effective cookie domain
func (c *Config) GetEffectiveCookieDomain() string {
	if c.Auth.CookieDomain != "" {
		return c.Auth.CookieDomain
	}

	// In development, leave empty for localhost
	if c.Environment == "development" {
		return ""
	}

	// Use server domain if available
	return c.Server.Domain
}

// IsProduction checks if running in production environment
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// IsDevelopment checks if running in development environment
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// Helper functions to get environment variables with fallbacks
func getEnv(key, fallback string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return fallback
}

func getIntEnv(key string, fallback int) int {
	if value, exists := os.LookupEnv(key); exists {
		intValue, err := strconv.Atoi(value)
		if err == nil {
			return intValue
		}
	}
	return fallback
}

func getBoolEnv(key string, fallback bool) bool {
	if value, exists := os.LookupEnv(key); exists {
		boolValue, err := strconv.ParseBool(value)
		if err == nil {
			return boolValue
		}
	}
	return fallback
}

func getDurationEnv(key string, fallback time.Duration) time.Duration {
	if value, exists := os.LookupEnv(key); exists {
		duration, err := time.ParseDuration(value)
		if err == nil {
			return duration
		}
	}
	return fallback
}

// getDefaultSecure returns true for production, false for development
func getDefaultSecure() bool {
	env := getEnv("APP_ENV", "development")
	return env == "production"
}

// generateDefaultSecret generates a default secret with a prefix
// This is used only when no secret is provided in environment variables
func generateDefaultSecret(prefix string) string {
	// These are default secrets for development only
	// In production, you MUST set proper secrets via environment variables
	secrets := map[string]string{
		"access":  "default-access-secret-change-this-in-production-min-32-chars",
		"refresh": "default-refresh-secret-change-this-in-production-min-32-chars",
		"csrf":    "default-csrf-secret-change-this-in-production",
	}

	if secret, exists := secrets[prefix]; exists {
		return secret
	}

	return "default-secret-change-this-in-production-minimum-32-characters"
}
