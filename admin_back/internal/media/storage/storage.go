package storage

import (
	"context"
	"io"
	"time"
)

type StorageProvider interface {
	Store(ctx context.Context, file io.Reader, filename string) (string, error)
	Get(ctx context.Context, filePath string) (io.ReadCloser, error)
	Delete(ctx context.Context, filePath string) error
	GetURL(filePath string) string
	Exists(ctx context.Context, filePath string) (bool, error)
	GetFileInfo(ctx context.Context, filePath string) (FileInfo, error)
	Copy(ctx context.Context, srcPath, destPath string) error
}

type FileInfo struct {
	Size         int64
	LastModified time.Time
	IsDirectory  bool
}
