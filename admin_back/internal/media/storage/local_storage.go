package storage

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/rs/zerolog"
)

type LocalStorage struct {
	basePath string
	baseURL  string
	logger   zerolog.Logger
}

func NewLocalStorage(basePath, baseURL string, logger zerolog.Logger) (*LocalStorage, error) {
	// Clean and validate base path
	basePath = filepath.Clean(basePath)

	// Ensure base path is absolute
	if !filepath.IsAbs(basePath) {
		var err error
		basePath, err = filepath.Abs(basePath)
		if err != nil {
			return nil, fmt.Errorf("failed to get absolute path: %w", err)
		}
	}

	// Create base directory if it doesn't exist
	if err := os.MkdirAll(basePath, 0755); err != nil {
		return nil, fmt.Errorf("failed to create base directory %s: %w", basePath, err)
	}

	// Create subdirectories for different media types
	mediaTypes := []string{"video", "audio", "image", "document"}
	for _, mediaType := range mediaTypes {
		dir := filepath.Join(basePath, mediaType)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return nil, fmt.Errorf("failed to create media type directory %s: %w", dir, err)
		}
	}

	return &LocalStorage{
		basePath: basePath,
		baseURL:  strings.TrimSuffix(baseURL, "/"),
		logger:   logger.With().Str("component", "local_storage").Logger(),
	}, nil
}

// Store saves a file to local storage with proper directory structure
func (s *LocalStorage) Store(ctx context.Context, file io.Reader, filename string) (string, error) {
	s.logger.Debug().Str("filename", filename).Msg("Storing file")

	// Extract media type from filename (assuming format: {type}_{uuid}.ext or {lang}_{type}_{uuid}.ext)
	mediaType := s.extractMediaTypeFromFilename(filename)

	// Create directory structure: basePath/mediaType/year/month/day
	now := time.Now()
	dateDir := filepath.Join(now.Format("2006"), now.Format("01"), now.Format("02"))
	fullDir := filepath.Join(s.basePath, mediaType, dateDir)

	if err := os.MkdirAll(fullDir, 0755); err != nil {
		s.logger.Error().Err(err).Str("directory", fullDir).Msg("Failed to create directory")
		return "", fmt.Errorf("failed to create directory: %w", err)
	}

	// Full file path
	filePath := filepath.Join(fullDir, filename)

	// Create the file
	dst, err := os.Create(filePath)
	if err != nil {
		s.logger.Error().Err(err).Str("file_path", filePath).Msg("Failed to create file")
		return "", fmt.Errorf("failed to create file: %w", err)
	}
	defer dst.Close()

	// Copy file content
	if _, err := io.Copy(dst, file); err != nil {
		s.logger.Error().Err(err).Str("file_path", filePath).Msg("Failed to copy file content")
		// Clean up the file if copy failed
		os.Remove(filePath)
		return "", fmt.Errorf("failed to copy file content: %w", err)
	}

	// Set appropriate file permissions
	if err := os.Chmod(filePath, 0644); err != nil {
		s.logger.Warn().Err(err).Str("file_path", filePath).Msg("Failed to set file permissions")
	}

	// Return relative path from base (without basePath prefix)
	relativePath := filepath.Join(mediaType, dateDir, filename)

	s.logger.Info().
		Str("filename", filename).
		Str("relative_path", relativePath).
		Str("full_path", filePath).
		Msg("File stored successfully")

	return relativePath, nil
}

// Get retrieves a file from local storage
func (s *LocalStorage) Get(ctx context.Context, filePath string) (io.ReadCloser, error) {
	s.logger.Debug().Str("file_path", filePath).Msg("Retrieving file")

	fullPath := filepath.Join(s.basePath, filePath)

	// Security check: ensure the file is within our base path
	cleanFullPath := filepath.Clean(fullPath)
	if !strings.HasPrefix(cleanFullPath, s.basePath) {
		s.logger.Warn().Str("file_path", filePath).Msg("Attempted path traversal attack")
		return nil, fmt.Errorf("invalid file path")
	}

	file, err := os.Open(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			s.logger.Warn().Str("file_path", filePath).Msg("File not found")
			return nil, fmt.Errorf("file not found")
		}
		s.logger.Error().Err(err).Str("file_path", filePath).Msg("Failed to open file")
		return nil, fmt.Errorf("failed to open file: %w", err)
	}

	return file, nil
}

// Delete removes a file from local storage
func (s *LocalStorage) Delete(ctx context.Context, filePath string) error {
	s.logger.Debug().Str("file_path", filePath).Msg("Deleting file")

	fullPath := filepath.Join(s.basePath, filePath)

	// Security check: ensure the file is within our base path
	cleanFullPath := filepath.Clean(fullPath)
	if !strings.HasPrefix(cleanFullPath, s.basePath) {
		s.logger.Warn().Str("file_path", filePath).Msg("Attempted path traversal attack")
		return fmt.Errorf("invalid file path")
	}

	err := os.Remove(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			s.logger.Warn().Str("file_path", filePath).Msg("File not found for deletion")
			return nil // Don't return error if file doesn't exist
		}
		s.logger.Error().Err(err).Str("file_path", filePath).Msg("Failed to delete file")
		return fmt.Errorf("failed to delete file: %w", err)
	}

	// Try to remove empty directories (cleanup)
	s.cleanupEmptyDirectories(filepath.Dir(fullPath))

	s.logger.Info().Str("file_path", filePath).Msg("File deleted successfully")
	return nil
}

// GetURL returns the public URL for a file
func (s *LocalStorage) GetURL(filePath string) string {
	// Clean the file path and ensure it uses forward slashes for URLs
	cleanPath := filepath.ToSlash(filepath.Clean(filePath))

	// Remove leading slash if present
	cleanPath = strings.TrimPrefix(cleanPath, "/")

	return fmt.Sprintf("%s/%s", s.baseURL, cleanPath)
}

// Exists checks if a file exists
func (s *LocalStorage) Exists(ctx context.Context, filePath string) (bool, error) {
	fullPath := filepath.Join(s.basePath, filePath)

	// Security check
	cleanFullPath := filepath.Clean(fullPath)
	if !strings.HasPrefix(cleanFullPath, s.basePath) {
		return false, fmt.Errorf("invalid file path")
	}

	_, err := os.Stat(fullPath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, fmt.Errorf("failed to check file existence: %w", err)
	}
	return true, nil
}

// GetFileInfo returns file information
func (s *LocalStorage) GetFileInfo(ctx context.Context, filePath string) (FileInfo, error) {
	fullPath := filepath.Join(s.basePath, filePath)

	// Security check
	cleanFullPath := filepath.Clean(fullPath)
	if !strings.HasPrefix(cleanFullPath, s.basePath) {
		return FileInfo{}, fmt.Errorf("invalid file path")
	}

	stat, err := os.Stat(fullPath)
	if err != nil {
		return FileInfo{}, fmt.Errorf("failed to get file info: %w", err)
	}

	return FileInfo{
		Size:         stat.Size(),
		LastModified: stat.ModTime(),
		IsDirectory:  stat.IsDir(),
	}, nil
}

// Copy creates a copy of an existing file
func (s *LocalStorage) Copy(ctx context.Context, srcPath, destPath string) error {
	s.logger.Debug().
		Str("src_path", srcPath).
		Str("dest_path", destPath).
		Msg("Copying file")

	srcFullPath := filepath.Join(s.basePath, srcPath)
	destFullPath := filepath.Join(s.basePath, destPath)

	// Security checks
	if !strings.HasPrefix(filepath.Clean(srcFullPath), s.basePath) ||
		!strings.HasPrefix(filepath.Clean(destFullPath), s.basePath) {
		return fmt.Errorf("invalid file paths")
	}

	// Ensure destination directory exists
	destDir := filepath.Dir(destFullPath)
	if err := os.MkdirAll(destDir, 0755); err != nil {
		return fmt.Errorf("failed to create destination directory: %w", err)
	}

	// Open source file
	src, err := os.Open(srcFullPath)
	if err != nil {
		return fmt.Errorf("failed to open source file: %w", err)
	}
	defer src.Close()

	// Create destination file
	dest, err := os.Create(destFullPath)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %w", err)
	}
	defer dest.Close()

	// Copy content
	if _, err := io.Copy(dest, src); err != nil {
		os.Remove(destFullPath) // Cleanup on failure
		return fmt.Errorf("failed to copy file content: %w", err)
	}

	// Set appropriate permissions
	if err := os.Chmod(destFullPath, 0644); err != nil {
		s.logger.Warn().Err(err).Str("dest_path", destPath).Msg("Failed to set file permissions")
	}

	s.logger.Info().
		Str("src_path", srcPath).
		Str("dest_path", destPath).
		Msg("File copied successfully")

	return nil
}

// extractMediaTypeFromFilename extracts media type from filename
func (s *LocalStorage) extractMediaTypeFromFilename(filename string) string {
	// Remove extension and split by underscores
	nameWithoutExt := strings.TrimSuffix(filename, filepath.Ext(filename))
	parts := strings.Split(nameWithoutExt, "_")

	validTypes := map[string]bool{
		"video":    true,
		"audio":    true,
		"image":    true,
		"document": true,
	}

	// Check if any part is a valid media type
	for _, part := range parts {
		if validTypes[part] {
			return part
		}
	}

	// Default to document if no media type found
	return "document"
}

// cleanupEmptyDirectories removes empty directories up to the base path
func (s *LocalStorage) cleanupEmptyDirectories(dirPath string) {
	// Don't remove the base path or above, or media type directories
	if !strings.HasPrefix(dirPath, s.basePath) || dirPath == s.basePath {
		return
	}

	// Don't remove direct media type subdirectories
	relPath, _ := filepath.Rel(s.basePath, dirPath)
	if strings.Count(relPath, string(filepath.Separator)) <= 0 {
		return
	}

	// Try to remove the directory (will only succeed if empty)
	if err := os.Remove(dirPath); err == nil {
		s.logger.Debug().Str("directory", dirPath).Msg("Removed empty directory")
		// Recursively try to remove parent directories
		s.cleanupEmptyDirectories(filepath.Dir(dirPath))
	}
}
