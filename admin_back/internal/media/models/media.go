package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

type MediaType string

const (
	MediaTypeVideo    MediaType = "video"
	MediaTypeAudio    MediaType = "audio"
	MediaTypeImage    MediaType = "image"
	MediaTypeDocument MediaType = "document"
)

// String returns the string representation of MediaType
func (mt MediaType) String() string {
	return string(mt)
}

// IsValid checks if the media type is valid
func (mt MediaType) IsValid() bool {
	switch mt {
	case MediaTypeVideo, MediaTypeAudio, MediaTypeImage, MediaTypeDocument:
		return true
	default:
		return false
	}
}

type Dimensions struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

func (d Dimensions) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *Dimensions) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	b, ok := value.([]byte)
	if !ok {
		return nil
	}
	return json.Unmarshal(b, d)
}

// MediaFile represents a media file in the system
type MediaFile struct {
	ID               uuid.UUID   `json:"id"`
	OriginalFilename string      `json:"original_filename"`
	StoredFilename   string      `json:"stored_filename"`
	FilePath         string      `json:"file_path"`
	FileSize         int64       `json:"file_size"`
	MimeType         string      `json:"mime_type"`
	MediaType        MediaType   `json:"media_type"`
	LanguageID       *int        `json:"language_id,omitempty"`
	Duration         *int        `json:"duration,omitempty"` // seconds for video/audio
	Dimensions       *Dimensions `json:"dimensions,omitempty"`
	Metadata         interface{} `json:"metadata,omitempty"`
	CreatedBy        *int64      `json:"created_by,omitempty"`
	CreatedAt        time.Time   `json:"created_at"`
	UpdatedAt        time.Time   `json:"updated_at"`
}

// GetPublicURL returns the public URL for accessing this media file
func (mf *MediaFile) GetPublicURL(baseURL string) string {
	return baseURL + "/" + mf.FilePath
}

// IsImage returns true if the media file is an image
func (mf *MediaFile) IsImage() bool {
	return mf.MediaType == MediaTypeImage
}

// IsVideo returns true if the media file is a video
func (mf *MediaFile) IsVideo() bool {
	return mf.MediaType == MediaTypeVideo
}

// IsAudio returns true if the media file is an audio file
func (mf *MediaFile) IsAudio() bool {
	return mf.MediaType == MediaTypeAudio
}

// GetSizeInMB returns the file size in megabytes
func (mf *MediaFile) GetSizeInMB() float64 {
	return float64(mf.FileSize) / 1024 / 1024
}
