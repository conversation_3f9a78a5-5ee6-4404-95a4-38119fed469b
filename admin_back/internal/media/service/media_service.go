// internal/media/service/media_service.go
package service

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"strings"

	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/i18n/service"
	"github.com/psynarios/admin_back/internal/media/models"
	"github.com/psynarios/admin_back/internal/media/repository"
	"github.com/psynarios/admin_back/internal/media/storage"
)

type MediaService struct {
	mediaRepo       repository.Repository
	storageProvider storage.StorageProvider
	i18nService     *service.I18nService
	logger          zerolog.Logger
	maxFileSize     int64
}

func NewMediaService(
	mediaRepo repository.Repository,
	storageProvider storage.StorageProvider,
	i18nService *service.I18nService,
	logger zerolog.Logger,
	maxFileSize int64,
) *MediaService {
	if maxFileSize == 0 {
		maxFileSize = 100 * 1024 * 1024 // 100MB default
	}

	return &MediaService{
		mediaRepo:       mediaRepo,
		storageProvider: storageProvider,
		i18nService:     i18nService,
		logger:          logger.With().Str("component", "media_service").Logger(),
		maxFileSize:     maxFileSize,
	}
}

// Request/Response structures
type UploadRequest struct {
	File       multipart.File         `json:"-"`
	Header     *multipart.FileHeader  `json:"-"`
	LanguageID *int                   `json:"language_id,omitempty"`
	MediaType  models.MediaType       `json:"media_type" validate:"required"`
	CreatedBy  int64                  `json:"created_by"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
}

type UploadResponse struct {
	MediaFile *models.MediaFile `json:"media_file"`
	URL       string            `json:"url"`
}

type MediaListRequest struct {
	MediaType  *models.MediaType `json:"media_type,omitempty"`
	LanguageID *int              `json:"language_id,omitempty"`
	CreatedBy  *int64            `json:"created_by,omitempty"`
	Search     string            `json:"search,omitempty"`
	Limit      int               `json:"limit" validate:"min=1,max=100"`
	Offset     int               `json:"offset" validate:"min=0"`
}

type MediaListResponse struct {
	MediaFiles []models.MediaFile `json:"media_files"`
	Total      int                `json:"total"`
	Limit      int                `json:"limit"`
	Offset     int                `json:"offset"`
}

// UploadMedia uploads a media file with validation and processing
func (s *MediaService) UploadMedia(ctx context.Context, req *UploadRequest) (*UploadResponse, error) {
	s.logger.Info().
		Str("filename", req.Header.Filename).
		Str("media_type", string(req.MediaType)).
		Interface("language_id", req.LanguageID).
		Int64("size", req.Header.Size).
		Msg("Starting media upload")

	// Validate language if provided
	if req.LanguageID != nil {
		if err := s.i18nService.ValidateLanguageID(ctx, *req.LanguageID); err != nil {
			s.logger.Error().Err(err).Int("language_id", *req.LanguageID).Msg("Invalid language ID")
			return nil, fmt.Errorf("invalid language ID: %w", err)
		}
	}

	// Validate file size
	if req.Header.Size > s.maxFileSize {
		s.logger.Warn().
			Int64("file_size", req.Header.Size).
			Int64("max_size", s.maxFileSize).
			Msg("File size exceeds maximum allowed")
		return nil, fmt.Errorf("file size %d bytes exceeds maximum allowed %d bytes", req.Header.Size, s.maxFileSize)
	}

	// Validate file type
	if err := s.validateFileType(req.Header.Filename, req.MediaType); err != nil {
		s.logger.Error().Err(err).Str("filename", req.Header.Filename).Msg("Invalid file type")
		return nil, err
	}

	// Validate and process file content
	dimensions, duration, err := s.analyzeFile(req.File, req.MediaType)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to analyze file")
		return nil, fmt.Errorf("failed to analyze file: %w", err)
	}

	// Generate unique filename with language prefix if specified
	ext := filepath.Ext(req.Header.Filename)
	var storedFilename string

	if req.LanguageID != nil {
		lang, _ := s.i18nService.GetLanguageByID(ctx, *req.LanguageID)
		storedFilename = fmt.Sprintf("%s_%s_%s%s", lang.Code, string(req.MediaType), uuid.New().String(), ext)
	} else {
		storedFilename = fmt.Sprintf("%s_%s%s", string(req.MediaType), uuid.New().String(), ext)
	}

	// Reset file pointer before storing
	if seeker, ok := req.File.(io.Seeker); ok {
		seeker.Seek(0, 0)
	}

	// Store file using the corrected storage provider
	filePath, err := s.storageProvider.Store(ctx, req.File, storedFilename)
	if err != nil {
		s.logger.Error().Err(err).Str("filename", storedFilename).Msg("Failed to store file")
		return nil, fmt.Errorf("failed to store file: %w", err)
	}

	// Create media file record
	mediaFile := models.MediaFile{
		OriginalFilename: req.Header.Filename,
		StoredFilename:   storedFilename,
		FilePath:         filePath,
		FileSize:         req.Header.Size,
		MimeType:         req.Header.Header.Get("Content-Type"),
		MediaType:        req.MediaType,
		LanguageID:       req.LanguageID,
		Duration:         duration,
		Dimensions:       dimensions,
		Metadata:         req.Metadata,
		CreatedBy:        &req.CreatedBy,
	}

	// Save to database
	savedMedia, err := s.mediaRepo.Create(ctx, mediaFile)
	if err != nil {
		// Cleanup stored file on database error
		s.storageProvider.Delete(ctx, filePath)
		s.logger.Error().Err(err).Str("filename", req.Header.Filename).Msg("Failed to save media file to database")
		return nil, fmt.Errorf("failed to save media file: %w", err)
	}

	// Get the public URL for the file
	publicURL := s.storageProvider.GetURL(filePath)

	s.logger.Info().
		Str("id", savedMedia.ID.String()).
		Str("filename", savedMedia.OriginalFilename).
		Str("stored_path", filePath).
		Str("url", publicURL).
		Msg("Media uploaded successfully")

	return &UploadResponse{
		MediaFile: &savedMedia,
		URL:       publicURL,
	}, nil
}

// GetMediaByID retrieves a media file by ID
func (s *MediaService) GetMediaByID(ctx context.Context, id uuid.UUID) (*models.MediaFile, error) {
	s.logger.Debug().Str("media_id", id.String()).Msg("Getting media by ID")

	media, err := s.mediaRepo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("media_id", id.String()).Msg("Failed to get media")
		return nil, fmt.Errorf("failed to get media: %w", err)
	}

	return &media, nil
}

// ListMedia retrieves media files with filtering and pagination
func (s *MediaService) ListMedia(ctx context.Context, req *MediaListRequest) (*MediaListResponse, error) {
	s.logger.Debug().Interface("request", req).Msg("Listing media files")

	var mediaFiles []models.MediaFile
	var total int
	var err error

	// Validate language if specified
	if req.LanguageID != nil {
		if err := s.i18nService.ValidateLanguageID(ctx, *req.LanguageID); err != nil {
			return nil, fmt.Errorf("invalid language ID: %w", err)
		}
	}

	// Handle different query types
	switch {
	case req.Search != "":
		mediaFiles, err = s.mediaRepo.SearchByFilename(ctx, req.Search, req.Limit, req.Offset)
		// For search, we'd need a separate count method
		total = len(mediaFiles) // Simplified
	case req.CreatedBy != nil:
		mediaFiles, err = s.mediaRepo.GetByCreatedBy(ctx, *req.CreatedBy, req.Limit, req.Offset)
		total, _ = s.mediaRepo.Count(ctx, req.MediaType) // Simplified
	case req.LanguageID != nil:
		mediaFiles, err = s.mediaRepo.GetByLanguage(ctx, *req.LanguageID, req.MediaType, req.Limit, req.Offset)
		total, _ = s.mediaRepo.CountByLanguage(ctx, *req.LanguageID, req.MediaType)
	default:
		mediaFiles, err = s.mediaRepo.List(ctx, req.MediaType, req.Limit, req.Offset)
		total, _ = s.mediaRepo.Count(ctx, req.MediaType)
	}

	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to list media files")
		return nil, fmt.Errorf("failed to list media files: %w", err)
	}

	s.logger.Debug().
		Int("count", len(mediaFiles)).
		Int("total", total).
		Msg("Media files listed successfully")

	return &MediaListResponse{
		MediaFiles: mediaFiles,
		Total:      total,
		Limit:      req.Limit,
		Offset:     req.Offset,
	}, nil
}

// UpdateMedia updates media file metadata
func (s *MediaService) UpdateMedia(ctx context.Context, id uuid.UUID, updates map[string]interface{}) (*models.MediaFile, error) {
	s.logger.Info().Str("media_id", id.String()).Interface("updates", updates).Msg("Updating media")

	// Get existing media
	media, err := s.mediaRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("media not found: %w", err)
	}

	// Apply updates
	if filename, ok := updates["original_filename"].(string); ok {
		media.OriginalFilename = filename
	}
	if metadata, ok := updates["metadata"]; ok {
		media.Metadata = metadata
	}
	if languageID, ok := updates["language_id"].(int); ok {
		if err := s.i18nService.ValidateLanguageID(ctx, languageID); err != nil {
			return nil, fmt.Errorf("invalid language ID: %w", err)
		}
		media.LanguageID = &languageID
	}

	// Update in database
	updatedMedia, err := s.mediaRepo.Update(ctx, media)
	if err != nil {
		s.logger.Error().Err(err).Str("media_id", id.String()).Msg("Failed to update media")
		return nil, fmt.Errorf("failed to update media: %w", err)
	}

	s.logger.Info().Str("media_id", id.String()).Msg("Media updated successfully")
	return &updatedMedia, nil
}

// DeleteMedia deletes a media file
func (s *MediaService) DeleteMedia(ctx context.Context, id uuid.UUID) error {
	s.logger.Info().Str("media_id", id.String()).Msg("Deleting media")

	// Get media info for cleanup
	media, err := s.mediaRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("media not found: %w", err)
	}

	// Delete from database first
	err = s.mediaRepo.Delete(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("media_id", id.String()).Msg("Failed to delete media from database")
		return fmt.Errorf("failed to delete media: %w", err)
	}

	// Delete file from storage
	if err := s.storageProvider.Delete(ctx, media.FilePath); err != nil {
		s.logger.Warn().Err(err).Str("file_path", media.FilePath).Msg("Failed to delete file from storage")
		// Don't return error here as database deletion succeeded
	}

	s.logger.Info().Str("media_id", id.String()).Msg("Media deleted successfully")
	return nil
}

// GetMediaStats returns statistics about media files
func (s *MediaService) GetMediaStats(ctx context.Context) (map[string]interface{}, error) {
	s.logger.Debug().Msg("Getting media statistics")

	stats, err := s.mediaRepo.GetStats(ctx)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to get media stats")
		return nil, fmt.Errorf("failed to get media stats: %w", err)
	}

	s.logger.Debug().Interface("stats", stats).Msg("Media statistics retrieved")
	return stats, nil
}

// GetPublicURL returns the public URL for a file path
func (s *MediaService) GetPublicURL(filePath string) string {
	return s.storageProvider.GetURL(filePath)
}

// validateFileType validates the file extension against the expected media type
func (s *MediaService) validateFileType(filename string, expectedType models.MediaType) error {
	ext := strings.ToLower(filepath.Ext(filename))

	validExtensions := map[models.MediaType][]string{
		models.MediaTypeVideo:    {".mp4", ".avi", ".mov", ".mkv", ".webm", ".flv", ".wmv"},
		models.MediaTypeAudio:    {".mp3", ".wav", ".aac", ".ogg", ".m4a", ".flac", ".wma"},
		models.MediaTypeImage:    {".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".svg"},
		models.MediaTypeDocument: {".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt"},
	}

	validExts, exists := validExtensions[expectedType]
	if !exists {
		return fmt.Errorf("unsupported media type: %s", expectedType)
	}

	for _, validExt := range validExts {
		if ext == validExt {
			return nil
		}
	}

	return fmt.Errorf("invalid file extension %s for media type %s. Allowed: %v", ext, expectedType, validExts)
}

// analyzeFile analyzes file content to extract metadata like dimensions and duration
func (s *MediaService) analyzeFile(file multipart.File, mediaType models.MediaType) (*models.Dimensions, *int, error) {
	// Reset file pointer
	file.Seek(0, 0)
	defer file.Seek(0, 0) // Reset for subsequent reads

	switch mediaType {
	case models.MediaTypeImage:
		return s.analyzeImage(file)
	case models.MediaTypeVideo:
		return s.analyzeVideo(file)
	case models.MediaTypeAudio:
		return s.analyzeAudio(file)
	default:
		return nil, nil, nil // No analysis needed for documents
	}
}

// analyzeImage extracts dimensions from image files
func (s *MediaService) analyzeImage(file multipart.File) (*models.Dimensions, *int, error) {
	// For a complete implementation, you'd use image libraries like:
	// - image package for basic formats
	// - github.com/disintegration/imaging for advanced features
	// This is a simplified version

	// Read a small portion to detect format and dimensions
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return nil, nil, fmt.Errorf("failed to read image header: %w", err)
	}

	// Basic dimension detection would go here
	// For now, return nil to indicate no dimensions extracted
	_ = n // Use the bytes read for actual analysis

	return nil, nil, nil
}

// analyzeVideo extracts duration and dimensions from video files
func (s *MediaService) analyzeVideo(file multipart.File) (*models.Dimensions, *int, error) {
	// For production, you'd use libraries like:
	// - github.com/3d0c/gmf (Go FFmpeg bindings)
	// - github.com/giorgisio/goav (Another FFmpeg wrapper)

	// This would require FFmpeg or similar tool to extract:
	// - Video duration
	// - Resolution (width x height)
	// - Codec information

	return nil, nil, nil
}

// analyzeAudio extracts duration from audio files
func (s *MediaService) analyzeAudio(file multipart.File) (*models.Dimensions, *int, error) {
	// For production, you'd use libraries like:
	// - github.com/tcolgate/mp3 for MP3 files
	// - github.com/go-audio/wav for WAV files

	return nil, nil, nil
}

// CopyMedia creates a copy of an existing media file
func (s *MediaService) CopyMedia(ctx context.Context, id uuid.UUID, newLanguageID *int, createdBy int64) (*models.MediaFile, error) {
	s.logger.Info().Str("source_id", id.String()).Interface("new_language_id", newLanguageID).Msg("Copying media")

	// Get source media
	sourceMedia, err := s.mediaRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("source media not found: %w", err)
	}

	// Validate new language if provided
	if newLanguageID != nil {
		if err := s.i18nService.ValidateLanguageID(ctx, *newLanguageID); err != nil {
			return nil, fmt.Errorf("invalid language ID: %w", err)
		}
	}

	// Generate new filename
	ext := filepath.Ext(sourceMedia.StoredFilename)
	var newFilename string

	if newLanguageID != nil {
		lang, _ := s.i18nService.GetLanguageByID(ctx, *newLanguageID)
		newFilename = fmt.Sprintf("%s_%s_%s%s", lang.Code, string(sourceMedia.MediaType), uuid.New().String(), ext)
	} else {
		newFilename = fmt.Sprintf("%s_%s%s", string(sourceMedia.MediaType), uuid.New().String(), ext)
	}

	// Copy file in storage
	newFilePath := strings.Replace(sourceMedia.FilePath, sourceMedia.StoredFilename, newFilename, 1)
	err = s.storageProvider.Copy(ctx, sourceMedia.FilePath, newFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to copy file: %w", err)
	}

	// Create new media record
	newMedia := models.MediaFile{
		OriginalFilename: sourceMedia.OriginalFilename,
		StoredFilename:   newFilename,
		FilePath:         newFilePath,
		FileSize:         sourceMedia.FileSize,
		MimeType:         sourceMedia.MimeType,
		MediaType:        sourceMedia.MediaType,
		LanguageID:       newLanguageID,
		Duration:         sourceMedia.Duration,
		Dimensions:       sourceMedia.Dimensions,
		Metadata:         sourceMedia.Metadata,
		CreatedBy:        &createdBy,
	}

	savedMedia, err := s.mediaRepo.Create(ctx, newMedia)
	if err != nil {
		// Cleanup copied file on database error
		s.storageProvider.Delete(ctx, newFilePath)
		return nil, fmt.Errorf("failed to save copied media: %w", err)
	}

	s.logger.Info().
		Str("source_id", id.String()).
		Str("new_id", savedMedia.ID.String()).
		Msg("Media copied successfully")

	return &savedMedia, nil
}
