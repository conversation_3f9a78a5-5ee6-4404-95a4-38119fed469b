package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/media/models"
	"github.com/psynarios/admin_back/internal/media/repository"
)

// mediaRepository implements the Repository interface
type mediaRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

// NewMediaRepository creates a new media repository
func NewMediaRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.Repository {
	return &mediaRepository{
		db:     db,
		logger: logger.With().Str("component", "media_repository").Logger(),
	}
}

func (r *mediaRepository) Create(ctx context.Context, media models.MediaFile) (models.MediaFile, error) {
	query := `
        INSERT INTO media_files (id, original_filename, stored_filename, file_path, file_size, 
                               mime_type, media_type, language_id, duration, dimensions, metadata, 
                               created_by, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
        RETURNING id, created_at, updated_at`

	now := time.Now()
	media.ID = uuid.New()
	media.CreatedAt = now
	media.UpdatedAt = now

	err := r.db.QueryRow(
		ctx,
		query,
		media.ID,
		media.OriginalFilename,
		media.StoredFilename,
		media.FilePath,
		media.FileSize,
		media.MimeType,
		media.MediaType,
		media.LanguageID,
		media.Duration,
		media.Dimensions,
		media.Metadata,
		media.CreatedBy,
		media.CreatedAt,
		media.UpdatedAt,
	).Scan(&media.ID, &media.CreatedAt, &media.UpdatedAt)

	if err != nil {
		return models.MediaFile{}, fmt.Errorf("failed to create media file: %w", err)
	}

	r.logger.Info().
		Str("id", media.ID.String()).
		Str("filename", media.OriginalFilename).
		Str("media_type", string(media.MediaType)).
		Int64("file_size", media.FileSize).
		Msg("Media file created successfully")

	return media, nil
}

func (r *mediaRepository) GetByID(ctx context.Context, id uuid.UUID) (models.MediaFile, error) {
	query := `
        SELECT id, original_filename, stored_filename, file_path, file_size, mime_type, 
               media_type, language_id, duration, dimensions, metadata, created_by, created_at, updated_at
        FROM media_files
        WHERE id = $1`

	var media models.MediaFile
	row := r.db.QueryRow(ctx, query, id)

	err := row.Scan(
		&media.ID,
		&media.OriginalFilename,
		&media.StoredFilename,
		&media.FilePath,
		&media.FileSize,
		&media.MimeType,
		&media.MediaType,
		&media.LanguageID,
		&media.Duration,
		&media.Dimensions,
		&media.Metadata,
		&media.CreatedBy,
		&media.CreatedAt,
		&media.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.MediaFile{}, fmt.Errorf("media file with id %s not found", id)
		}
		return models.MediaFile{}, fmt.Errorf("failed to get media file: %w", err)
	}

	return media, nil
}

func (r *mediaRepository) Update(ctx context.Context, media models.MediaFile) (models.MediaFile, error) {
	query := `
        UPDATE media_files
        SET original_filename = $1, stored_filename = $2, file_path = $3, file_size = $4,
            mime_type = $5, media_type = $6, language_id = $7, duration = $8, 
            dimensions = $9, metadata = $10, updated_at = $11
        WHERE id = $12
        RETURNING id, original_filename, stored_filename, file_path, file_size, mime_type, 
                 media_type, language_id, duration, dimensions, metadata, created_by, created_at, updated_at`

	media.UpdatedAt = time.Now()

	var updatedMedia models.MediaFile
	row := r.db.QueryRow(
		ctx,
		query,
		media.OriginalFilename,
		media.StoredFilename,
		media.FilePath,
		media.FileSize,
		media.MimeType,
		media.MediaType,
		media.LanguageID,
		media.Duration,
		media.Dimensions,
		media.Metadata,
		media.UpdatedAt,
		media.ID,
	)

	err := row.Scan(
		&updatedMedia.ID,
		&updatedMedia.OriginalFilename,
		&updatedMedia.StoredFilename,
		&updatedMedia.FilePath,
		&updatedMedia.FileSize,
		&updatedMedia.MimeType,
		&updatedMedia.MediaType,
		&updatedMedia.LanguageID,
		&updatedMedia.Duration,
		&updatedMedia.Dimensions,
		&updatedMedia.Metadata,
		&updatedMedia.CreatedBy,
		&updatedMedia.CreatedAt,
		&updatedMedia.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.MediaFile{}, fmt.Errorf("media file with id %s not found", media.ID)
		}
		return models.MediaFile{}, fmt.Errorf("failed to update media file: %w", err)
	}

	r.logger.Info().
		Str("id", media.ID.String()).
		Str("filename", media.OriginalFilename).
		Msg("Media file updated successfully")

	return updatedMedia, nil
}

func (r *mediaRepository) Delete(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM media_files WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete media file: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("media file with id %s not found", id)
	}

	r.logger.Info().Str("id", id.String()).Msg("Media file deleted successfully")
	return nil
}

func (r *mediaRepository) List(ctx context.Context, mediaType *models.MediaType, limit, offset int) ([]models.MediaFile, error) {
	var query string
	var args []interface{}

	baseQuery := `
        SELECT id, original_filename, stored_filename, file_path, file_size, mime_type, 
               media_type, language_id, duration, dimensions, metadata, created_by, created_at, updated_at
        FROM media_files`

	if mediaType != nil {
		query = baseQuery + ` WHERE media_type = $1 ORDER BY created_at DESC LIMIT $2 OFFSET $3`
		args = []interface{}{*mediaType, limit, offset}
	} else {
		query = baseQuery + ` ORDER BY created_at DESC LIMIT $1 OFFSET $2`
		args = []interface{}{limit, offset}
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to list media files: %w", err)
	}
	defer rows.Close()

	var mediaFiles []models.MediaFile
	for rows.Next() {
		var media models.MediaFile
		err := rows.Scan(
			&media.ID,
			&media.OriginalFilename,
			&media.StoredFilename,
			&media.FilePath,
			&media.FileSize,
			&media.MimeType,
			&media.MediaType,
			&media.LanguageID,
			&media.Duration,
			&media.Dimensions,
			&media.Metadata,
			&media.CreatedBy,
			&media.CreatedAt,
			&media.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan media file: %w", err)
		}
		mediaFiles = append(mediaFiles, media)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating media files: %w", err)
	}

	return mediaFiles, nil
}

func (r *mediaRepository) GetByLanguage(ctx context.Context, languageID int, mediaType *models.MediaType, limit, offset int) ([]models.MediaFile, error) {
	var query string
	var args []interface{}

	baseQuery := `
        SELECT id, original_filename, stored_filename, file_path, file_size, mime_type, 
               media_type, language_id, duration, dimensions, metadata, created_by, created_at, updated_at
        FROM media_files
        WHERE language_id = $1`

	args = []interface{}{languageID}

	if mediaType != nil {
		query = baseQuery + ` AND media_type = $2 ORDER BY created_at DESC LIMIT $3 OFFSET $4`
		args = append(args, *mediaType, limit, offset)
	} else {
		query = baseQuery + ` ORDER BY created_at DESC LIMIT $2 OFFSET $3`
		args = append(args, limit, offset)
	}

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to get media files by language: %w", err)
	}
	defer rows.Close()

	var mediaFiles []models.MediaFile
	for rows.Next() {
		var media models.MediaFile
		err := rows.Scan(
			&media.ID,
			&media.OriginalFilename,
			&media.StoredFilename,
			&media.FilePath,
			&media.FileSize,
			&media.MimeType,
			&media.MediaType,
			&media.LanguageID,
			&media.Duration,
			&media.Dimensions,
			&media.Metadata,
			&media.CreatedBy,
			&media.CreatedAt,
			&media.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan media file: %w", err)
		}
		mediaFiles = append(mediaFiles, media)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating media files: %w", err)
	}

	return mediaFiles, nil
}

func (r *mediaRepository) GetByCreatedBy(ctx context.Context, createdBy int64, limit, offset int) ([]models.MediaFile, error) {
	query := `
        SELECT id, original_filename, stored_filename, file_path, file_size, mime_type, 
               media_type, language_id, duration, dimensions, metadata, created_by, created_at, updated_at
        FROM media_files
        WHERE created_by = $1
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3`

	rows, err := r.db.Query(ctx, query, createdBy, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get media files by creator: %w", err)
	}
	defer rows.Close()

	var mediaFiles []models.MediaFile
	for rows.Next() {
		var media models.MediaFile
		err := rows.Scan(
			&media.ID,
			&media.OriginalFilename,
			&media.StoredFilename,
			&media.FilePath,
			&media.FileSize,
			&media.MimeType,
			&media.MediaType,
			&media.LanguageID,
			&media.Duration,
			&media.Dimensions,
			&media.Metadata,
			&media.CreatedBy,
			&media.CreatedAt,
			&media.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan media file: %w", err)
		}
		mediaFiles = append(mediaFiles, media)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating media files: %w", err)
	}

	return mediaFiles, nil
}

func (r *mediaRepository) Count(ctx context.Context, mediaType *models.MediaType) (int, error) {
	var query string
	var args []interface{}

	if mediaType != nil {
		query = `SELECT COUNT(*) FROM media_files WHERE media_type = $1`
		args = []interface{}{*mediaType}
	} else {
		query = `SELECT COUNT(*) FROM media_files`
	}

	var count int
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count media files: %w", err)
	}

	return count, nil
}

func (r *mediaRepository) CountByLanguage(ctx context.Context, languageID int, mediaType *models.MediaType) (int, error) {
	var query string
	var args []interface{}

	if mediaType != nil {
		query = `SELECT COUNT(*) FROM media_files WHERE language_id = $1 AND media_type = $2`
		args = []interface{}{languageID, *mediaType}
	} else {
		query = `SELECT COUNT(*) FROM media_files WHERE language_id = $1`
		args = []interface{}{languageID}
	}

	var count int
	err := r.db.QueryRow(ctx, query, args...).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to count media files by language: %w", err)
	}

	return count, nil
}

func (r *mediaRepository) SearchByFilename(ctx context.Context, filename string, limit, offset int) ([]models.MediaFile, error) {
	query := `
        SELECT id, original_filename, stored_filename, file_path, file_size, mime_type, 
               media_type, language_id, duration, dimensions, metadata, created_by, created_at, updated_at
        FROM media_files
        WHERE original_filename ILIKE '%' || $1 || '%' OR stored_filename ILIKE '%' || $1 || '%'
        ORDER BY created_at DESC
        LIMIT $2 OFFSET $3`

	rows, err := r.db.Query(ctx, query, filename, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to search media files: %w", err)
	}
	defer rows.Close()

	var mediaFiles []models.MediaFile
	for rows.Next() {
		var media models.MediaFile
		err := rows.Scan(
			&media.ID,
			&media.OriginalFilename,
			&media.StoredFilename,
			&media.FilePath,
			&media.FileSize,
			&media.MimeType,
			&media.MediaType,
			&media.LanguageID,
			&media.Duration,
			&media.Dimensions,
			&media.Metadata,
			&media.CreatedBy,
			&media.CreatedAt,
			&media.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan media file: %w", err)
		}
		mediaFiles = append(mediaFiles, media)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating media files: %w", err)
	}

	return mediaFiles, nil
}

func (r *mediaRepository) GetStats(ctx context.Context) (map[string]interface{}, error) {
	query := `
        SELECT 
            media_type,
            COUNT(*) as count,
            SUM(file_size) as total_size,
            AVG(file_size) as avg_size
        FROM media_files 
        GROUP BY media_type`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get media stats: %w", err)
	}
	defer rows.Close()

	stats := make(map[string]interface{})
	typeStats := make(map[string]map[string]interface{})

	var totalCount int
	var totalSize int64

	for rows.Next() {
		var mediaType string
		var count int
		var totalTypeSize int64
		var avgSize float64

		err := rows.Scan(&mediaType, &count, &totalTypeSize, &avgSize)
		if err != nil {
			return nil, fmt.Errorf("failed to scan stats: %w", err)
		}

		typeStats[mediaType] = map[string]interface{}{
			"count":      count,
			"total_size": totalTypeSize,
			"avg_size":   avgSize,
		}

		totalCount += count
		totalSize += totalTypeSize
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating stats: %w", err)
	}

	stats["total_count"] = totalCount
	stats["total_size"] = totalSize
	stats["by_type"] = typeStats
	stats["generated_at"] = time.Now()

	return stats, nil
}
