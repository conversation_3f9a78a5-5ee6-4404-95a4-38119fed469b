package repository

import (
	"context"

	"github.com/google/uuid"
	"github.com/psynarios/admin_back/internal/media/models"
)

type Repository interface {
	Create(ctx context.Context, media models.MediaFile) (models.MediaFile, error)
	GetByID(ctx context.Context, id uuid.UUID) (models.MediaFile, error)
	Update(ctx context.Context, media models.MediaFile) (models.MediaFile, error)
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, mediaType *models.MediaType, limit, offset int) ([]models.MediaFile, error)
	GetByLanguage(ctx context.Context, languageID int, mediaType *models.MediaType, limit, offset int) ([]models.MediaFile, error)
	GetByCreatedBy(ctx context.Context, createdBy int64, limit, offset int) ([]models.MediaFile, error)
	Count(ctx context.Context, mediaType *models.MediaType) (int, error)
	CountByLanguage(ctx context.Context, languageID int, mediaType *models.MediaType) (int, error)
	SearchByFilename(ctx context.Context, filename string, limit, offset int) ([]models.MediaFile, error)
	GetStats(ctx context.Context) (map[string]interface{}, error)
}
