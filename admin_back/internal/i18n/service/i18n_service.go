package service

import (
	"context"
	"fmt"

	"github.com/psynarios/admin_back/internal/i18n/models"
	"github.com/psynarios/admin_back/internal/i18n/repository"
)

type I18nService struct {
	languageRepo    repository.Repository
	defaultLanguage *models.Language
}

func NewI18nService(languageRepo repository.Repository) *I18nService {
	return &I18nService{
		languageRepo: languageRepo,
	}
}

func (s *I18nService) GetDefaultLanguage(ctx context.Context) (*models.Language, error) {
	if s.defaultLanguage == nil {
		lang, err := s.languageRepo.GetDefault(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get default language: %w", err)
		}
		s.defaultLanguage = lang
	}
	return s.defaultLanguage, nil
}

func (s *I18nService) GetLanguageByCode(ctx context.Context, code string) (*models.Language, error) {
	return s.languageRepo.GetByCode(ctx, code)
}

func (s *I18nService) GetLanguageByID(ctx context.Context, id int) (*models.Language, error) {
	return s.languageRepo.GetByID(ctx, id)
}

func (s *I18nService) GetActiveLanguages(ctx context.Context) ([]*models.Language, error) {
	return s.languageRepo.GetActive(ctx)
}

func (s *I18nService) ValidateLanguageID(ctx context.Context, languageID int) error {
	_, err := s.languageRepo.GetByID(ctx, languageID)
	return err
}
