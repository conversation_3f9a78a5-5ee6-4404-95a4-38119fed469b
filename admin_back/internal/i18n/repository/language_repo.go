package repository

import (
	"context"

	"github.com/psynarios/admin_back/internal/i18n/models"
)

type Repository interface {
	GetByID(ctx context.Context, id int) (*models.Language, error)
	GetByCode(ctx context.Context, code string) (*models.Language, error)
	GetDefault(ctx context.Context) (*models.Language, error)
	GetActive(ctx context.Context) ([]*models.Language, error)
	GetAll(ctx context.Context) ([]*models.Language, error)
	Create(ctx context.Context, language *models.Language) error
	Update(ctx context.Context, language *models.Language) error
	Delete(ctx context.Context, id int) error
}
