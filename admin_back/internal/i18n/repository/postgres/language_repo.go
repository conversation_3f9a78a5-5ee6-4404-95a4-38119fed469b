package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/i18n/models"
	"github.com/psynarios/admin_back/internal/i18n/repository"
)

// languageRepository implements the Repository interface
type languageRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

// NewLanguageRepository creates a new language repository
func NewLanguageRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.Repository {
	return &languageRepository{
		db:     db,
		logger: logger.With().Str("component", "language_repository").Logger(),
	}
}

func (r *languageRepository) GetByID(ctx context.Context, id int) (*models.Language, error) {
	query := `
		SELECT id, code, name, native_name, is_active, is_default, created_at, updated_at
		FROM languages
		WHERE id = $1`

	var language models.Language
	row := r.db.QueryRow(ctx, query, id)

	err := row.Scan(
		&language.ID,
		&language.Code,
		&language.Name,
		&language.NativeName,
		&language.IsActive,
		&language.IsDefault,
		&language.CreatedAt,
		&language.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("language with id %d not found", id)
		}
		return nil, fmt.Errorf("failed to get language: %w", err)
	}

	return &language, nil
}

func (r *languageRepository) GetByCode(ctx context.Context, code string) (*models.Language, error) {
	query := `
		SELECT id, code, name, native_name, is_active, is_default, created_at, updated_at
		FROM languages
		WHERE code = $1 AND is_active = true`

	var language models.Language
	row := r.db.QueryRow(ctx, query, code)

	err := row.Scan(
		&language.ID,
		&language.Code,
		&language.Name,
		&language.NativeName,
		&language.IsActive,
		&language.IsDefault,
		&language.CreatedAt,
		&language.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("language with code %s not found", code)
		}
		return nil, fmt.Errorf("failed to get language: %w", err)
	}

	return &language, nil
}

func (r *languageRepository) GetDefault(ctx context.Context) (*models.Language, error) {
	query := `
		SELECT id, code, name, native_name, is_active, is_default, created_at, updated_at
		FROM languages
		WHERE is_default = true AND is_active = true
		LIMIT 1`

	var language models.Language
	row := r.db.QueryRow(ctx, query)

	err := row.Scan(
		&language.ID,
		&language.Code,
		&language.Name,
		&language.NativeName,
		&language.IsActive,
		&language.IsDefault,
		&language.CreatedAt,
		&language.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("no default language found")
		}
		return nil, fmt.Errorf("failed to get default language: %w", err)
	}

	return &language, nil
}

func (r *languageRepository) GetActive(ctx context.Context) ([]*models.Language, error) {
	query := `
		SELECT id, code, name, native_name, is_active, is_default, created_at, updated_at
		FROM languages
		WHERE is_active = true
		ORDER BY is_default DESC, name ASC`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get active languages: %w", err)
	}
	defer rows.Close()

	var languages []*models.Language
	for rows.Next() {
		language := new(models.Language)
		err := rows.Scan(
			&language.ID,
			&language.Code,
			&language.Name,
			&language.NativeName,
			&language.IsActive,
			&language.IsDefault,
			&language.CreatedAt,
			&language.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan language: %w", err)
		}
		languages = append(languages, language)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating languages: %w", err)
	}

	return languages, nil
}

func (r *languageRepository) GetAll(ctx context.Context) ([]*models.Language, error) {
	query := `
		SELECT id, code, name, native_name, is_active, is_default, created_at, updated_at
		FROM languages
		ORDER BY name ASC`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get all languages: %w", err)
	}
	defer rows.Close()

	var languages []*models.Language
	for rows.Next() {
		language := new(models.Language)
		err := rows.Scan(
			&language.ID,
			&language.Code,
			&language.Name,
			&language.NativeName,
			&language.IsActive,
			&language.IsDefault,
			&language.CreatedAt,
			&language.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan language: %w", err)
		}
		languages = append(languages, language)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating languages: %w", err)
	}

	return languages, nil
}

func (r *languageRepository) Create(ctx context.Context, language *models.Language) error {
	query := `
		INSERT INTO languages (code, name, native_name, is_active, is_default, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
		RETURNING id, created_at, updated_at`

	now := time.Now()
	language.CreatedAt = now
	language.UpdatedAt = now

	err := r.db.QueryRow(
		ctx,
		query,
		language.Code,
		language.Name,
		language.NativeName,
		language.IsActive,
		language.IsDefault,
		language.CreatedAt,
		language.UpdatedAt,
	).Scan(&language.ID, &language.CreatedAt, &language.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create language: %w", err)
	}

	// If this is set as default, deactivate other defaults
	if language.IsDefault {
		deactivateQuery := `
			UPDATE languages
			SET is_default = FALSE, updated_at = $1
			WHERE id != $2`

		_, err = r.db.Exec(ctx, deactivateQuery, now, language.ID)
		if err != nil {
			r.logger.Error().Err(err).Int("language_id", language.ID).
				Msg("Failed to deactivate other default languages")
			// Continue anyway since the main operation succeeded
		}
	}

	r.logger.Info().Int("id", language.ID).Str("code", language.Code).
		Msg("Language created successfully")

	return nil
}

func (r *languageRepository) Update(ctx context.Context, language *models.Language) error {
	query := `
		UPDATE languages
		SET code = $1, name = $2, native_name = $3, is_active = $4, is_default = $5, updated_at = $6
		WHERE id = $7
		RETURNING id, code, name, native_name, is_active, is_default, created_at, updated_at`

	language.UpdatedAt = time.Now()

	row := r.db.QueryRow(
		ctx,
		query,
		language.Code,
		language.Name,
		language.NativeName,
		language.IsActive,
		language.IsDefault,
		language.UpdatedAt,
		language.ID,
	)

	err := row.Scan(
		&language.ID,
		&language.Code,
		&language.Name,
		&language.NativeName,
		&language.IsActive,
		&language.IsDefault,
		&language.CreatedAt,
		&language.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return fmt.Errorf("language with id %d not found", language.ID)
		}
		return fmt.Errorf("failed to update language: %w", err)
	}

	// If this is set as default, deactivate other defaults
	if language.IsDefault {
		deactivateQuery := `
			UPDATE languages
			SET is_default = FALSE, updated_at = $1
			WHERE id != $2`

		_, err = r.db.Exec(ctx, deactivateQuery, language.UpdatedAt, language.ID)
		if err != nil {
			r.logger.Error().Err(err).Int("language_id", language.ID).
				Msg("Failed to deactivate other default languages")
			// Continue anyway since the main operation succeeded
		}
	}

	r.logger.Info().Int("id", language.ID).Str("code", language.Code).
		Msg("Language updated successfully")

	return nil
}

func (r *languageRepository) Delete(ctx context.Context, id int) error {
	query := `DELETE FROM languages WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete language: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("language with id %d not found", id)
	}

	r.logger.Info().Int("id", id).Msg("Language deleted successfully")
	return nil
}
