package models

import "time"

type Language struct {
	ID         int       `json:"id" db:"id"`
	Code       string    `json:"code" db:"code" validate:"required,min=2,max=5"`
	Name       string    `json:"name" db:"name" validate:"required,max=100"`
	NativeName string    `json:"native_name" db:"native_name" validate:"required,max=100"`
	IsActive   bool      `json:"is_active" db:"is_active"`
	IsDefault  bool      `json:"is_default" db:"is_default"`
	CreatedAt  time.Time `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time `json:"updated_at" db:"updated_at"`
}
