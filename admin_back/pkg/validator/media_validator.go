// pkg/validator/media_validator.go
package validator

import (
	"fmt"
	"mime/multipart"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"github.com/psynarios/admin_back/internal/media/models"
)

const (
	MaxFileSize = 100 * 1024 * 1024 // 100MB
	MinFileSize = 1                 // 1 byte
	MaxLimit    = 100
	MaxOffset   = 10000
)

// ValidateMediaType validates if the provided media type is valid
func (v *CustomValidator) ValidateMediaType(mediaType string) error {
	if mediaType == "" {
		return fmt.Errorf("media_type is required")
	}

	mt := models.MediaType(mediaType)
	if !mt.IsValid() {
		return fmt.Errorf("invalid media_type. Must be one of: video, audio, image, document")
	}

	return nil
}

// ValidateFileUpload validates file upload parameters
func (v *CustomValidator) ValidateFileUpload(header *multipart.FileHeader, mediaType models.MediaType) error {
	// Validate file size
	if header.Size < MinFileSize {
		return fmt.Errorf("file size must be at least %d bytes", MinFileSize)
	}

	if header.Size > MaxFileSize {
		return fmt.Errorf("file size %d bytes exceeds maximum allowed %d bytes (%.1f MB)",
			header.Size, MaxFileSize, float64(MaxFileSize)/(1024*1024))
	}

	// Validate filename
	if header.Filename == "" {
		return fmt.Errorf("filename cannot be empty")
	}

	// Validate file extension against media type
	if err := v.validateFileExtension(header.Filename, mediaType); err != nil {
		return err
	}

	// Validate MIME type if available
	if err := v.validateMimeType(header.Header.Get("Content-Type"), mediaType); err != nil {
		return err
	}

	return nil
}

// validateFileExtension validates file extension against media type
func (v *CustomValidator) validateFileExtension(filename string, mediaType models.MediaType) error {
	ext := strings.ToLower(filepath.Ext(filename))
	if ext == "" {
		return fmt.Errorf("file must have an extension")
	}

	validExtensions := map[models.MediaType][]string{
		models.MediaTypeVideo: {
			".mp4", ".avi", ".mov", ".mkv", ".webm", ".flv", ".wmv", ".m4v", ".3gp",
		},
		models.MediaTypeAudio: {
			".mp3", ".wav", ".aac", ".ogg", ".m4a", ".flac", ".wma", ".opus",
		},
		models.MediaTypeImage: {
			".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp", ".svg", ".tiff", ".ico",
		},
		models.MediaTypeDocument: {
			".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt", ".xls", ".xlsx", ".ppt", ".pptx",
		},
	}

	allowedExts, exists := validExtensions[mediaType]
	if !exists {
		return fmt.Errorf("unsupported media type: %s", mediaType)
	}

	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			return nil
		}
	}

	return fmt.Errorf("invalid file extension %s for media type %s. Allowed extensions: %v",
		ext, mediaType, allowedExts)
}

// validateMimeType validates MIME type against media type
func (v *CustomValidator) validateMimeType(mimeType string, mediaType models.MediaType) error {
	if mimeType == "" {
		// MIME type validation is optional if not provided
		return nil
	}

	mimeType = strings.ToLower(mimeType)

	validMimeTypes := map[models.MediaType][]string{
		models.MediaTypeVideo: {
			"video/mp4", "video/avi", "video/quicktime", "video/x-msvideo",
			"video/webm", "video/x-flv", "video/x-ms-wmv",
		},
		models.MediaTypeAudio: {
			"audio/mpeg", "audio/wav", "audio/aac", "audio/ogg",
			"audio/mp4", "audio/flac", "audio/x-ms-wma",
		},
		models.MediaTypeImage: {
			"image/jpeg", "image/png", "image/gif", "image/webp",
			"image/bmp", "image/svg+xml", "image/tiff",
		},
		models.MediaTypeDocument: {
			"application/pdf", "application/msword",
			"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
			"text/plain", "application/rtf",
		},
	}

	allowedMimes, exists := validMimeTypes[mediaType]
	if !exists {
		return nil // Skip validation for unknown media types
	}

	for _, allowedMime := range allowedMimes {
		if strings.HasPrefix(mimeType, allowedMime) {
			return nil
		}
	}

	return fmt.Errorf("invalid MIME type %s for media type %s. Expected one of: %v",
		mimeType, mediaType, allowedMimes)
}

// ValidatePagination validates pagination parameters
func (v *CustomValidator) ValidatePagination(limit, offset int) error {
	if limit <= 0 {
		return fmt.Errorf("limit must be greater than 0")
	}

	if limit > MaxLimit {
		return fmt.Errorf("limit %d exceeds maximum allowed %d", limit, MaxLimit)
	}

	if offset < 0 {
		return fmt.Errorf("offset must be non-negative")
	}

	if offset > MaxOffset {
		return fmt.Errorf("offset %d exceeds maximum allowed %d", offset, MaxOffset)
	}

	return nil
}

// ValidateMediaUpdates validates media update parameters
func (v *CustomValidator) ValidateMediaUpdates(updates map[string]interface{}) error {
	allowedFields := map[string]bool{
		"original_filename": true,
		"metadata":          true,
		"language_id":       true,
	}

	for field := range updates {
		if !allowedFields[field] {
			return fmt.Errorf("field '%s' is not allowed for updates", field)
		}
	}

	// Validate specific fields
	if filename, ok := updates["original_filename"]; ok {
		if str, ok := filename.(string); ok {
			if strings.TrimSpace(str) == "" {
				return fmt.Errorf("original_filename cannot be empty")
			}
			if len(str) > 255 {
				return fmt.Errorf("original_filename too long (max 255 characters)")
			}
		} else {
			return fmt.Errorf("original_filename must be a string")
		}
	}

	if languageID, ok := updates["language_id"]; ok {
		if _, ok := languageID.(int); !ok {
			if floatVal, ok := languageID.(float64); ok {
				updates["language_id"] = int(floatVal) // Convert float64 to int from JSON
			} else {
				return fmt.Errorf("language_id must be an integer")
			}
		}
	}

	return nil
}

// ValidateAdminMediaUpdates validates admin media update parameters (more permissive)
func (v *CustomValidator) ValidateAdminMediaUpdates(updates map[string]interface{}) error {
	allowedFields := map[string]bool{
		"original_filename": true,
		"metadata":          true,
		"language_id":       true,
		"created_by":        true, // Admin can change ownership
		"file_size":         true, // Admin can correct file size
		"mime_type":         true, // Admin can correct MIME type
		"media_type":        true, // Admin can correct media type
	}

	for field := range updates {
		if !allowedFields[field] {
			return fmt.Errorf("field '%s' is not allowed for admin updates", field)
		}
	}

	// Validate specific fields
	if filename, ok := updates["original_filename"]; ok {
		if str, ok := filename.(string); ok {
			if strings.TrimSpace(str) == "" {
				return fmt.Errorf("original_filename cannot be empty")
			}
			if len(str) > 255 {
				return fmt.Errorf("original_filename too long (max 255 characters)")
			}
		} else {
			return fmt.Errorf("original_filename must be a string")
		}
	}

	if mediaType, ok := updates["media_type"]; ok {
		if str, ok := mediaType.(string); ok {
			if err := v.ValidateMediaType(str); err != nil {
				return err
			}
		} else {
			return fmt.Errorf("media_type must be a string")
		}
	}

	if fileSize, ok := updates["file_size"]; ok {
		var size int64
		switch v := fileSize.(type) {
		case int:
			size = int64(v)
		case int64:
			size = v
		case float64:
			size = int64(v)
		default:
			return fmt.Errorf("file_size must be a number")
		}

		if size < MinFileSize || size > MaxFileSize {
			return fmt.Errorf("file_size must be between %d and %d bytes", MinFileSize, MaxFileSize)
		}
	}

	return nil
}

// ValidateSearchQuery validates search query parameters
func (v *CustomValidator) ValidateSearchQuery(query string) error {
	if strings.TrimSpace(query) == "" {
		return fmt.Errorf("search query cannot be empty")
	}

	if len(query) < 2 {
		return fmt.Errorf("search query must be at least 2 characters long")
	}

	if len(query) > 100 {
		return fmt.Errorf("search query too long (max 100 characters)")
	}

	// Check for potentially dangerous characters
	dangerous := []string{"<", ">", "\"", "'", "&", ";", "(", ")", "|", "`"}
	for _, char := range dangerous {
		if strings.Contains(query, char) {
			return fmt.Errorf("search query contains invalid character: %s", char)
		}
	}

	return nil
}

// ValidateBulkOperation validates bulk operation parameters
func (v *CustomValidator) ValidateBulkOperation(operation map[string]interface{}) error {
	// Validate IDs array
	idsInterface, ok := operation["ids"]
	if !ok {
		return fmt.Errorf("'ids' field is required for bulk operations")
	}

	ids, ok := idsInterface.([]interface{})
	if !ok {
		return fmt.Errorf("'ids' must be an array")
	}

	if len(ids) == 0 {
		return fmt.Errorf("at least one ID is required for bulk operations")
	}

	if len(ids) > 50 {
		return fmt.Errorf("too many IDs for bulk operation (max 50)")
	}

	// Validate each ID
	for i, idInterface := range ids {
		idStr, ok := idInterface.(string)
		if !ok {
			return fmt.Errorf("ID at index %d must be a string", i)
		}

		if _, err := uuid.Parse(idStr); err != nil {
			return fmt.Errorf("invalid UUID at index %d: %s", i, idStr)
		}
	}

	return nil
}

// ValidateLanguageID validates language ID parameter
func (v *CustomValidator) ValidateLanguageID(languageID interface{}) error {
	var id int

	switch v := languageID.(type) {
	case int:
		id = v
	case float64:
		id = int(v)
	case string:
		parsed, err := strconv.Atoi(v)
		if err != nil {
			return fmt.Errorf("language_id must be a valid integer")
		}
		id = parsed
	default:
		return fmt.Errorf("language_id must be an integer")
	}

	if id <= 0 {
		return fmt.Errorf("language_id must be greater than 0")
	}

	// Additional validation could check if language exists in database
	// This would require injecting the i18n service into the validator

	return nil
}
