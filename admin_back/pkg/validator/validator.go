package validator

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
)

// CustomValidator implements a custom validator
type CustomValidator struct {
	validator *validator.Validate
}

// NewCustomValidator creates a new validator
func NewCustomValidator() *CustomValidator {
	v := validator.New()

	// Register validation for struct fields using JSON tags
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	// Register custom validations here
	// Example: v.RegisterValidation("my_custom_rule", myCustomRule)

	return &CustomValidator{validator: v}
}

// Validate validates a struct and returns validation errors
func (cv *CustomValidator) Validate(i interface{}) (map[string]string, error) {
	err := cv.validator.Struct(i)
	if err == nil {
		return nil, nil
	}

	// Convert validation errors to a map
	validationErrors := make(map[string]string)
	for _, err := range err.(validator.ValidationErrors) {
		fieldName := err.Field()
		validationErrors[fieldName] = formatErrorMsg(err)
	}

	return validationErrors, err
}

// formatErrorMsg formats a validation error message
func formatErrorMsg(err validator.FieldError) string {
	switch err.Tag() {
	case "required":
		return "This field is required"
	case "email":
		return "Must be a valid email address"
	case "min":
		if err.Type().Kind() == reflect.String {
			return fmt.Sprintf("Must be at least %s characters long", err.Param())
		}
		return fmt.Sprintf("Must be at least %s", err.Param())
	case "max":
		if err.Type().Kind() == reflect.String {
			return fmt.Sprintf("Must be at most %s characters long", err.Param())
		}
		return fmt.Sprintf("Must be at most %s", err.Param())
	case "oneof":
		return fmt.Sprintf("Must be one of: %s", err.Param())
	default:
		return fmt.Sprintf("Failed validation for '%s'", err.Tag())
	}
}

// ValidateVar validates a single variable
func (cv *CustomValidator) ValidateVar(field interface{}, tag string) error {
	return cv.validator.Var(field, tag)
}

func (v *CustomValidator) IsValidUUID(str string) bool {
	_, err := uuid.Parse(str)
	return err == nil
}

// ValidateFilename validates a filename for security issues
func (v *CustomValidator) ValidateFilename(filename string) error {
	if filename == "" {
		return fmt.Errorf("filename cannot be empty")
	}

	// Check for path traversal attacks
	if strings.Contains(filename, "..") {
		return fmt.Errorf("filename cannot contain '..'")
	}

	if strings.Contains(filename, "/") || strings.Contains(filename, "\\") {
		return fmt.Errorf("filename cannot contain path separators")
	}

	// Check for null bytes
	if strings.Contains(filename, "\x00") {
		return fmt.Errorf("filename cannot contain null bytes")
	}

	// Check length
	if len(filename) > 255 {
		return fmt.Errorf("filename too long (max 255 characters)")
	}

	return nil
}

// ValidateMetadata validates metadata object
func (v *CustomValidator) ValidateMetadata(metadata interface{}) error {
	if metadata == nil {
		return nil // Metadata is optional
	}

	// Ensure it's a map
	metaMap, ok := metadata.(map[string]interface{})
	if !ok {
		return fmt.Errorf("metadata must be a JSON object")
	}

	// Check for reasonable size (prevent huge metadata objects)
	if len(metaMap) > 50 {
		return fmt.Errorf("metadata object too large (max 50 keys)")
	}

	// Validate keys and values
	for key, value := range metaMap {
		if len(key) > 100 {
			return fmt.Errorf("metadata key '%s' too long (max 100 characters)", key)
		}

		// Convert value to string to check length
		var valueStr string
		switch v := value.(type) {
		case string:
			valueStr = v
		case nil:
			continue // nil values are OK
		default:
			// For non-string values, we'll allow them but not validate length
			continue
		}

		if len(valueStr) > 1000 {
			return fmt.Errorf("metadata value for key '%s' too long (max 1000 characters)", key)
		}
	}

	return nil
}
