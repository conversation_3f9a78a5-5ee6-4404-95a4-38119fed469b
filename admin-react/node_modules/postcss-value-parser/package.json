{"name": "postcss-value-parser", "version": "4.2.0", "description": "Transforms css values and at-rule params into the tree", "main": "lib/index.js", "files": ["lib"], "devDependencies": {"eslint": "^5.16.0", "husky": "^2.3.0", "lint-staged": "^8.1.7", "prettier": "^1.17.1", "tap-spec": "^5.0.0", "tape": "^4.10.2"}, "scripts": {"lint:prettier": "prettier \"**/*.js\" \"**/*.ts\" --list-different", "lint:js": "eslint . --cache", "lint": "yarn lint:js && yarn lint:prettier", "pretest": "yarn lint", "test": "tape test/*.js | tap-spec"}, "eslintConfig": {"env": {"es6": true, "node": true}, "extends": "eslint:recommended"}, "lint-staged": {"*.js": ["eslint", "prettier --write", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/TrySound/postcss-value-parser", "repository": {"type": "git", "url": "https://github.com/TrySound/postcss-value-parser.git"}, "keywords": ["postcss", "value", "parser"], "bugs": {"url": "https://github.com/TrySound/postcss-value-parser/issues"}}