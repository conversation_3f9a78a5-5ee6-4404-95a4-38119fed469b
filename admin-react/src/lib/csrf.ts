// CSRF token management for React app

class CSRFManager {
  private token: string | null = null;

  // Get CSRF token from cookie
  getFromCookie(): string | null {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'csrf_token') {
        return decodeURIComponent(value);
      }
    }
    return null;
  }

  // Get fresh CSRF token from server
  async refreshFromServer(): Promise<string | null> {
    try {
      if (process.env.NODE_ENV !== 'production') {
        console.log('🔄 [CSRF] Requesting fresh CSRF token from server');
      }

      const response = await fetch('/api/auth/csrf', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.csrf_token) {
          this.token = data.csrf_token;
          if (process.env.NODE_ENV !== 'production') {
            console.log('✅ [CSRF] Fresh CSRF token obtained from server');
          }
          return data.csrf_token;
        }
      } else {
        if (process.env.NODE_ENV !== 'production') {
          console.warn('⚠️ [CSRF] Failed to get CSRF token from server:', response.status);
        }
      }
    } catch (error) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('❌ [CSRF] Error getting CSRF token from server:', error);
      }
    }

    return null;
  }

  // Get current token (from memory, cookie, or server)
  async getToken(): Promise<string | null> {
    // First try memory cache
    if (this.token) {
      return this.token;
    }

    // Then try cookie
    this.token = this.getFromCookie();
    if (this.token) {
      return this.token;
    }

    // Finally, get fresh from server
    return await this.refreshFromServer();
  }

  // Initialize CSRF token on app startup
  async initialize(): Promise<void> {
    try {
      // Check if we already have a token
      const existingToken = this.getFromCookie();
      if (!existingToken) {
        // If no token, get a fresh one
        await this.refreshFromServer();
      } else {
        this.token = existingToken;
      }
    } catch (error) {
      console.error('Failed to initialize CSRF token:', error);
    }
  }

  // Clear token (for logout)
  clear(): void {
    this.token = null;
  }
}

export const csrfManager = new CSRFManager();
