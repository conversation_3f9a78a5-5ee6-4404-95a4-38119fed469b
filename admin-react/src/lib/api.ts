// API client for React app
import { csrfManager } from './csrf';
import { isDevelopment } from './env';

// Check if endpoint requires CSRF token
function requiresCSRF(endpoint: string, method: string): boolean {
  const csrfMethods = ['POST', 'PUT', 'DELETE', 'PATCH'];
  const csrfExemptEndpoints = ['/auth/login', '/auth/register', '/auth/csrf'];

  return csrfMethods.includes(method.toUpperCase()) &&
         !csrfExemptEndpoints.some(exempt => endpoint.includes(exempt));
}

// Custom error class for API errors
export class ApiError extends Error {
  public status: number;
  public statusText: string;
  public data?: any;

  constructor(status: number, statusText: string, data?: any) {
    super(`API Error: ${status} ${statusText}`);
    this.name = 'ApiError';
    this.status = status;
    this.statusText = statusText;
    this.data = data;
  }
}

// Main API function
export async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = endpoint.startsWith('/') ? `/api${endpoint}` : `/api/${endpoint}`;
  const method = options.method || 'GET';

  if (isDevelopment()) {
    console.log(`📡 [API] ${method} ${url}`);
  }

  // Setup headers
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };

  // Add CSRF token if required
  if (requiresCSRF(endpoint, method)) {
    const token = await csrfManager.getToken();
    if (token) {
      (headers as any)['X-CSRF-Token'] = token;
      if (isDevelopment()) {
        console.log(`🔒 [API] Adding CSRF token to ${method} ${endpoint}`);
      }
    } else {
      if (isDevelopment()) {
        console.warn(`⚠️ [API] No CSRF token available for ${method} ${endpoint}`);
      }
    }
  }

  try {
    const response = await fetch(url, {
      ...options,
      method,
      headers,
      credentials: 'include', // Important for cookies
    });

    // Handle CSRF token refresh on 403
    if (response.status === 403 && requiresCSRF(endpoint, method)) {
      if (isDevelopment()) {
        console.log('🔄 [API] CSRF token might be invalid, refreshing...');
      }
      const newToken = await csrfManager.refreshFromServer();

      if (newToken) {
        // Retry with new token
        (headers as any)['X-CSRF-Token'] = newToken;
        const retryResponse = await fetch(url, {
          ...options,
          method,
          headers,
          credentials: 'include',
        });

        if (retryResponse.ok) {
          return await retryResponse.json();
        }

        const errorData = await retryResponse.json().catch(() => ({}));
        throw new ApiError(retryResponse.status, retryResponse.statusText, errorData);
      }
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new ApiError(response.status, response.statusText, errorData);
    }

    return await response.json();
  } catch (error) {
    if (isDevelopment()) {
      console.error(`❌ [API] Error in ${method} ${endpoint}:`, error);
    }
    throw error;
  }
}

// Convenience methods
export const api = {
  get: <T>(endpoint: string): Promise<T> =>
    apiRequest<T>(endpoint, { method: 'GET' }),

  post: <T>(endpoint: string, data?: any): Promise<T> =>
    apiRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    }),

  put: <T>(endpoint: string, data?: any): Promise<T> =>
    apiRequest<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    }),

  delete: <T>(endpoint: string): Promise<T> =>
    apiRequest<T>(endpoint, { method: 'DELETE' }),
};
