import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { api, ApiError } from '../lib/api';
import { csrfManager } from '../lib/csrf';
import type { User } from '../types';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  console.log('🔐 [AUTH] AuthProvider render - user:', user?.email || 'none', 'loading:', loading, 'path:', location.pathname);

  // Handle authentication errors
  const handleAuthError = () => {
    setUser(null);
    localStorage.removeItem('user');
    csrfManager.clear();

    // Only redirect if we're not already on the login page
    if (location.pathname !== '/login') {
      console.log('🔄 [AUTH] Redirecting to login from:', location.pathname);
      navigate('/login');
    }
  };

  const checkAuth = async (): Promise<boolean> => {
    try {
      if (process.env.NODE_ENV !== 'production') {
        console.log('🔍 [AUTH] Checking authentication with server...');
      }
      
      const data = await api.get<{ authenticated: boolean; user?: User }>('/auth/status');
      
      if (data.authenticated && data.user) {
        setUser(data.user);
        localStorage.setItem('user', JSON.stringify(data.user));
        return true;
      } else {
        setUser(null);
        localStorage.removeItem('user');
        return false;
      }
    } catch (error) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('❌ [AUTH] Auth check error:', error);
      }
      return false;
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      if (process.env.NODE_ENV !== 'production') {
        console.log('🔐 [AUTH] Attempting login...');
      }
      
      const data = await api.post<{ user: User }>('/auth/login', { email, password });
      
      if (process.env.NODE_ENV !== 'production') {
        console.log('✅ [AUTH] Login successful');
      }
      
      // Store user data
      setUser(data.user);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      return true;
    } catch (error) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('❌ [AUTH] Login error:', error);
      }
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await api.post('/auth/logout');
      if (process.env.NODE_ENV !== 'production') {
        console.log('✅ [AUTH] Logout successful');
      }
    } catch (error) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('❌ [AUTH] Logout error:', error);
      }
    } finally {
      // Always clear local state regardless of API response
      setUser(null);
      localStorage.removeItem('user');
      csrfManager.clear();
      navigate('/login');
    }
  };

  // Check authentication status on mount
  useEffect(() => {
    const initAuth = async () => {
      try {
        // First check localStorage for a user
        const storedUser = localStorage.getItem('user');

        if (storedUser) {
          try {
            const userData = JSON.parse(storedUser);
            if (process.env.NODE_ENV !== 'production') {
              console.log('👤 [AUTH] Found user in localStorage:', userData.email);
            }

            // Set user immediately from localStorage
            setUser(userData);

            // Then verify with server in background
            try {
              const isValid = await checkAuth();

              if (isValid) {
                // If we're on the login page but have a valid user session, redirect to dashboard
                if (location.pathname === '/login') {
                  if (process.env.NODE_ENV !== 'production') {
                    console.log('🔄 [AUTH] Redirecting from login to dashboard');
                  }
                  navigate('/dashboard');
                }
              } else {
                handleAuthError();
              }
            } catch (serverError) {
              // If server check fails, still allow user to proceed with cached data
              if (process.env.NODE_ENV !== 'production') {
                console.warn('⚠️ [AUTH] Server check failed, using cached user data');
              }
            }
          } catch (e) {
            if (process.env.NODE_ENV !== 'production') {
              console.error('❌ [AUTH] Error parsing stored user', e);
            }
            localStorage.removeItem('user');
            handleAuthError();
          }
        } else {
          // No stored user, redirect to login if not already there
          if (process.env.NODE_ENV !== 'production') {
            console.log('👤 [AUTH] No stored user found');
          }
          if (location.pathname !== '/login') {
            console.log('🔄 [AUTH] No user, redirecting to login');
            navigate('/login');
          }
        }
      } catch (error) {
        if (process.env.NODE_ENV !== 'production') {
          console.error('❌ [AUTH] Error during initial auth check:', error);
        }
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, [navigate, location.pathname]);

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        checkAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
