import { useState, useEffect } from 'react'
import { api } from '../lib/api'
import { Template } from '../types'

export default function Templates() {
  const [templates, setTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    try {
      setLoading(true)
      const data = await api.get<Template[]>('/templates')
      setTemplates(data)
    } catch (err) {
      setError('Failed to load templates')
      console.error('Error loading templates:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div>
        <h1>Email Templates</h1>
        <p>Loading templates...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div>
        <h1>Email Templates</h1>
        <div style={{ 
          color: '#dc3545', 
          backgroundColor: '#f8d7da',
          padding: '1rem',
          borderRadius: '0.375rem'
        }}>
          {error}
        </div>
        <button 
          onClick={loadTemplates}
          style={{
            marginTop: '1rem',
            padding: '0.5rem 1rem',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '0.375rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <h1 style={{ margin: 0, color: '#495057' }}>Email Templates</h1>
        <button style={{
          padding: '0.75rem 1.5rem',
          backgroundColor: '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '0.375rem',
          cursor: 'pointer'
        }}>
          Add Template
        </button>
      </div>

      {templates.length === 0 ? (
        <div style={{
          textAlign: 'center',
          padding: '3rem',
          backgroundColor: '#f8f9fa',
          borderRadius: '0.5rem',
          border: '1px solid #dee2e6'
        }}>
          <p style={{ color: '#6c757d', fontSize: '1.1rem' }}>
            No templates found. Create your first email template to get started.
          </p>
        </div>
      ) : (
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
          gap: '1.5rem'
        }}>
          {templates.map((template) => (
            <div 
              key={template.id}
              style={{
                backgroundColor: 'white',
                border: '1px solid #dee2e6',
                borderRadius: '0.5rem',
                padding: '1.5rem'
              }}
            >
              <h3 style={{ 
                margin: '0 0 0.5rem 0', 
                color: '#495057',
                fontSize: '1.25rem'
              }}>
                {template.name}
              </h3>
              
              <p style={{ 
                color: '#6c757d', 
                margin: '0 0 1rem 0',
                fontSize: '0.9rem'
              }}>
                Subject: {template.subject}
              </p>
              
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '1rem'
              }}>
                <span style={{
                  backgroundColor: '#e3f2fd',
                  color: '#1976d2',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '1rem',
                  fontSize: '0.8rem'
                }}>
                  Week {template.week_number}
                </span>
                <span style={{
                  color: template.is_active ? '#28a745' : '#6c757d',
                  fontSize: '0.9rem'
                }}>
                  {template.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>

              <div style={{ 
                display: 'flex', 
                gap: '0.5rem'
              }}>
                <button style={{
                  flex: 1,
                  padding: '0.5rem',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0.25rem',
                  cursor: 'pointer'
                }}>
                  Edit
                </button>
                <button style={{
                  flex: 1,
                  padding: '0.5rem',
                  backgroundColor: '#6c757d',
                  color: 'white',
                  border: 'none',
                  borderRadius: '0.25rem',
                  cursor: 'pointer'
                }}>
                  Preview
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
