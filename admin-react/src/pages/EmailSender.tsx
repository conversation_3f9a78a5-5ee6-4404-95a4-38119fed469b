import { useState, useEffect } from 'react'
import { api } from '../lib/api'
import type { Template } from '../types'
import type { Company } from '../types'

export default function EmailSender() {
  const [templates, setTemplates] = useState<Template[]>([])
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [sending, setSending] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState('')
  const [selectedCompany, setSelectedCompany] = useState('')
  const [recipients, setRecipients] = useState('')
  const [ccRecipients, setCcRecipients] = useState('')
  const [bccRecipients, setBccRecipients] = useState('')
  const [templateData, setTemplateData] = useState<Record<string, string>>({})
  const [previewMode, setPreviewMode] = useState(false)
  const [previewContent, setPreviewContent] = useState({ subject: '', body: '' })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      setError('')
      const [templatesData, companiesData] = await Promise.all([
        api.get<Template[]>('/templates'),
        api.get<Company[]>('/companies')
      ])
      setTemplates(templatesData)
      setCompanies(companiesData)
    } catch (err) {
      console.error('Error loading data:', err)
      setError('Failed to load templates and companies. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleTemplateChange = (templateId: string) => {
    setSelectedTemplate(templateId)
    const template = templates.find(t => t.id === templateId)
    if (template) {
      // Initialize template data with default values
      const defaultData: Record<string, string> = {}
      if (template.default_data) {
        Object.keys(template.default_data).forEach(key => {
          defaultData[key] = template.default_data?.[key]?.toString() || ''
        })
      }
      setTemplateData(defaultData)
    }
  }

  const handlePreview = async () => {
    if (!selectedTemplate) {
      alert('Please select a template first')
      return
    }

    try {
      const template = templates.find(t => t.id === selectedTemplate)
      if (!template) return

      // Simple client-side template rendering for preview
      let subject = template.subject
      let body = template.body || ''

      // Replace template variables with data
      Object.entries(templateData).forEach(([key, value]) => {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g')
        subject = subject.replace(regex, value)
        body = body.replace(regex, value)
      })

      setPreviewContent({ subject, body })
      setPreviewMode(true)
    } catch (err) {
      console.error('Error generating preview:', err)
      setError('Failed to generate preview')
    }
  }

  const handleSend = async () => {
    if (!selectedTemplate || !recipients.trim()) {
      alert('Please select a template and enter recipients')
      return
    }

    if (!selectedCompany) {
      alert('Please select a company')
      return
    }

    try {
      setSending(true)
      setError('')

      const recipientList = recipients.split(',').map(email => email.trim()).filter(email => email)
      const ccList = ccRecipients ? ccRecipients.split(',').map(email => email.trim()).filter(email => email) : []
      const bccList = bccRecipients ? bccRecipients.split(',').map(email => email.trim()).filter(email => email) : []

      await api.post('/mail/send-template', {
        to: recipientList,
        cc: ccList,
        bcc: bccList,
        template_id: selectedTemplate,
        data: templateData,
        company_id: selectedCompany
      })

      alert(`Email sent successfully to ${recipientList.length} recipient(s)!`)

      // Reset form
      setRecipients('')
      setCcRecipients('')
      setBccRecipients('')
      setTemplateData({})
      setSelectedTemplate('')
    } catch (err) {
      console.error('Error sending email:', err)
      setError('Failed to send email. Please check your mail configuration and try again.')
    } finally {
      setSending(false)
    }
  }

  const selectedTemplateObj = templates.find(t => t.id === selectedTemplate)

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div>
        <h1 style={{
          fontSize: '1.875rem',
          fontWeight: 'bold',
          color: '#111827',
          margin: '0 0 0.25rem 0'
        }}>
          Email Sender
        </h1>
        <p style={{
          fontSize: '0.875rem',
          color: '#6b7280',
          margin: 0
        }}>
          Send emails to your customers using templates and automation.
        </p>
      </div>

      {error && (
        <div style={{
          padding: '1rem',
          fontSize: '0.875rem',
          color: '#b91c1c',
          backgroundColor: '#fef2f2',
          borderRadius: '0.375rem'
        }}>
          {error}
        </div>
      )}

      {/* Preview Modal */}
      {previewMode && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 50
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '0.5rem',
            padding: '1.5rem',
            width: '90%',
            maxWidth: '48rem',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '1.5rem'
            }}>
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#111827',
                margin: 0
              }}>
                Email Preview
              </h2>
              <button
                onClick={() => setPreviewMode(false)}
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  color: '#6b7280'
                }}
              >
                ×
              </button>
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <strong style={{ color: '#374151' }}>Subject: </strong>
              <span style={{ color: '#111827' }}>{previewContent.subject}</span>
            </div>

            <div style={{
              border: '1px solid #e5e7eb',
              borderRadius: '0.375rem',
              padding: '1rem',
              backgroundColor: '#f9fafb'
            }}>
              <div dangerouslySetInnerHTML={{ __html: previewContent.body }} />
            </div>

            <div style={{
              display: 'flex',
              gap: '0.75rem',
              justifyContent: 'flex-end',
              marginTop: '1.5rem'
            }}>
              <button
                onClick={() => setPreviewMode(false)}
                style={{
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  backgroundColor: 'white',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.375rem',
                  cursor: 'pointer'
                }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Email Sending Form */}
      {loading ? (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '3rem 0'
        }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '4px solid #4f46e5',
            borderRadius: '50%',
            borderTopColor: 'transparent',
            animation: 'spin 1s linear infinite'
          }}></div>
        </div>
      ) : (
        <div style={{
          backgroundColor: 'white',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
          borderRadius: '0.5rem',
          padding: '1.5rem'
        }}>
          <h2 style={{
            fontSize: '1.125rem',
            fontWeight: '600',
            color: '#111827',
            marginBottom: '1.5rem',
            margin: '0 0 1.5rem 0'
          }}>
            Send Email
          </h2>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            {/* Template and Company Selection */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.25rem'
                }}>
                  Email Template
                </label>
                <select
                  value={selectedTemplate}
                  onChange={(e) => handleTemplateChange(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.5rem 0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="">Select a template</option>
                  {templates.map(template => (
                    <option key={template.id} value={template.id}>
                      {template.name} (Week {template.week_number})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.25rem'
                }}>
                  Company
                </label>
                <select
                  value={selectedCompany}
                  onChange={(e) => setSelectedCompany(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.5rem 0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="">Select a company</option>
                  {companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Recipients */}
            <div>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: '#374151',
                marginBottom: '0.25rem'
              }}>
                Recipients (To) *
              </label>
              <textarea
                value={recipients}
                onChange={(e) => setRecipients(e.target.value)}
                placeholder="Enter email addresses separated by commas"
                rows={3}
                style={{
                  width: '100%',
                  padding: '0.5rem 0.75rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.375rem',
                  fontSize: '0.875rem',
                  boxSizing: 'border-box',
                  resize: 'vertical'
                }}
              />
            </div>

            {/* CC and BCC */}
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.25rem'
                }}>
                  CC (Optional)
                </label>
                <textarea
                  value={ccRecipients}
                  onChange={(e) => setCcRecipients(e.target.value)}
                  placeholder="CC email addresses"
                  rows={2}
                  style={{
                    width: '100%',
                    padding: '0.5rem 0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.25rem'
                }}>
                  BCC (Optional)
                </label>
                <textarea
                  value={bccRecipients}
                  onChange={(e) => setBccRecipients(e.target.value)}
                  placeholder="BCC email addresses"
                  rows={2}
                  style={{
                    width: '100%',
                    padding: '0.5rem 0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    boxSizing: 'border-box',
                    resize: 'vertical'
                  }}
                />
              </div>
            </div>

            {/* Template Data Fields */}
            {selectedTemplateObj && selectedTemplateObj.default_data && Object.keys(selectedTemplateObj.default_data).length > 0 && (
              <div>
                <h3 style={{
                  fontSize: '1rem',
                  fontWeight: '500',
                  color: '#111827',
                  marginBottom: '1rem',
                  margin: '0 0 1rem 0'
                }}>
                  Template Variables
                </h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
                  {Object.keys(selectedTemplateObj.default_data).map(key => (
                    <div key={key}>
                      <label style={{
                        display: 'block',
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '0.25rem'
                      }}>
                        {key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, ' ')}
                      </label>
                      <input
                        type="text"
                        value={templateData[key] || ''}
                        onChange={(e) => setTemplateData({ ...templateData, [key]: e.target.value })}
                        placeholder={`Enter ${key}`}
                        style={{
                          width: '100%',
                          padding: '0.5rem 0.75rem',
                          border: '1px solid #d1d5db',
                          borderRadius: '0.375rem',
                          fontSize: '0.875rem',
                          boxSizing: 'border-box'
                        }}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Template Preview */}
            {selectedTemplateObj && (
              <div style={{
                border: '1px solid #e5e7eb',
                borderRadius: '0.375rem',
                padding: '1rem',
                backgroundColor: '#f9fafb'
              }}>
                <h3 style={{
                  fontSize: '1rem',
                  fontWeight: '500',
                  color: '#111827',
                  marginBottom: '0.5rem',
                  margin: '0 0 0.5rem 0'
                }}>
                  Selected Template: {selectedTemplateObj.name}
                </h3>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  margin: '0 0 0.5rem 0'
                }}>
                  <strong>Subject:</strong> {selectedTemplateObj.subject}
                </p>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  margin: 0
                }}>
                  Week {selectedTemplateObj.week_number} • {selectedTemplateObj.is_active ? 'Active' : 'Inactive'}
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div style={{
              display: 'flex',
              gap: '0.75rem',
              justifyContent: 'flex-end',
              paddingTop: '1rem',
              borderTop: '1px solid #e5e7eb'
            }}>
              <button
                onClick={handlePreview}
                disabled={!selectedTemplate}
                style={{
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  backgroundColor: 'white',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.375rem',
                  cursor: !selectedTemplate ? 'not-allowed' : 'pointer',
                  opacity: !selectedTemplate ? 0.5 : 1
                }}
              >
                Preview Email
              </button>
              <button
                onClick={handleSend}
                disabled={!selectedTemplate || !recipients.trim() || !selectedCompany || sending}
                style={{
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'white',
                  backgroundColor: (!selectedTemplate || !recipients.trim() || !selectedCompany || sending) ? '#6b7280' : '#4f46e5',
                  border: 'none',
                  borderRadius: '0.375rem',
                  cursor: (!selectedTemplate || !recipients.trim() || !selectedCompany || sending) ? 'not-allowed' : 'pointer'
                }}
                onMouseEnter={(e) => {
                  if (!(!selectedTemplate || !recipients.trim() || !selectedCompany || sending)) {
                    e.currentTarget.style.backgroundColor = '#4338ca'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!(!selectedTemplate || !recipients.trim() || !selectedCompany || sending)) {
                    e.currentTarget.style.backgroundColor = '#4f46e5'
                  }
                }}
              >
                {sending ? 'Sending...' : 'Send Email'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
