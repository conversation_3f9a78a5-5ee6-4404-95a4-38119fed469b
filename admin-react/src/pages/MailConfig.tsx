import { useState, useEffect } from 'react'
import { api } from '../lib/api'
import type { MailConfig, CreateMailConfigRequest } from '../types/mail-config'
import type { Company } from '../types'

export default function MailConfig() {
  const [mailConfigs, setMailConfigs] = useState<MailConfig[]>([])
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showForm, setShowForm] = useState(false)
  const [editingConfig, setEditingConfig] = useState<MailConfig | null>(null)
  const [testingConfig, setTestingConfig] = useState<string | null>(null)
  const [testEmail, setTestEmail] = useState('')
  const [formData, setFormData] = useState<CreateMailConfigRequest>({
    company_id: '',
    sender_email: '',
    sender_name: '',
    smtp_host: '',
    smtp_port: 587,
    smtp_username: '',
    smtp_password: '',
    use_ssl: true,
    active: true
  })

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      setError('')
      const [configsData, companiesData] = await Promise.all([
        api.get<{ mail_configs: MailConfig[] }>('/mail/configs'),
        api.get<Company[]>('/companies')
      ])
      setMailConfigs(configsData.mail_configs || [])
      setCompanies(companiesData)
    } catch (err) {
      console.error('Error loading data:', err)
      setError('Failed to load mail configurations. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (editingConfig) {
        await api.put(`/mail/configs/${editingConfig.id}`, formData)
      } else {
        await api.post('/mail/configs', formData)
      }
      await loadData()
      resetForm()
    } catch (err) {
      console.error('Error saving mail config:', err)
      setError('Failed to save mail configuration. Please try again.')
    }
  }

  const handleDelete = async (config: MailConfig) => {
    if (!confirm(`Are you sure you want to delete the mail configuration for "${config.sender_email}"?`)) {
      return
    }
    try {
      await api.delete(`/mail/configs/${config.id}`)
      await loadData()
    } catch (err) {
      console.error('Error deleting mail config:', err)
      setError('Failed to delete mail configuration. Please try again.')
    }
  }

  const handleTest = async (config: MailConfig) => {
    if (!testEmail) {
      alert('Please enter a test email address')
      return
    }
    try {
      setTestingConfig(config.id)
      await api.post('/mail/configs/test', {
        config: config,
        test_email: testEmail
      })
      alert('Test email sent successfully!')
      setTestEmail('')
    } catch (err) {
      console.error('Error testing mail config:', err)
      alert('Failed to send test email. Please check your configuration.')
    } finally {
      setTestingConfig(null)
    }
  }

  const resetForm = () => {
    setFormData({
      company_id: '',
      sender_email: '',
      sender_name: '',
      smtp_host: '',
      smtp_port: 587,
      smtp_username: '',
      smtp_password: '',
      use_ssl: true,
      active: true
    })
    setEditingConfig(null)
    setShowForm(false)
  }

  const startEdit = (config: MailConfig) => {
    setFormData({
      company_id: config.company_id,
      sender_email: config.sender_email,
      sender_name: config.sender_name,
      smtp_host: config.smtp_host,
      smtp_port: config.smtp_port,
      smtp_username: config.smtp_username,
      smtp_password: '', // Don't populate password for security
      use_ssl: config.use_ssl,
      active: config.active
    })
    setEditingConfig(config)
    setShowForm(true)
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div>
          <h1 style={{
            fontSize: '1.875rem',
            fontWeight: 'bold',
            color: '#111827',
            margin: '0 0 0.25rem 0'
          }}>
            Mail Configuration
          </h1>
          <p style={{
            fontSize: '0.875rem',
            color: '#6b7280',
            margin: 0
          }}>
            Configure SMTP settings and email delivery options.
          </p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            padding: '0.5rem 1rem',
            fontSize: '0.875rem',
            fontWeight: '500',
            color: 'white',
            backgroundColor: '#4f46e5',
            border: 'none',
            borderRadius: '0.375rem',
            textDecoration: 'none',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#4338ca'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#4f46e5'}
        >
          Add Mail Config
        </button>
      </div>

      {error && (
        <div style={{
          padding: '1rem',
          fontSize: '0.875rem',
          color: '#b91c1c',
          backgroundColor: '#fef2f2',
          borderRadius: '0.375rem'
        }}>
          {error}
        </div>
      )}

      {/* Form Modal */}
      {showForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 50
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '0.5rem',
            padding: '1.5rem',
            width: '100%',
            maxWidth: '32rem',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '1.5rem'
            }}>
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#111827',
                margin: 0
              }}>
                {editingConfig ? 'Edit Mail Configuration' : 'Add Mail Configuration'}
              </h2>
              <button
                onClick={resetForm}
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  fontSize: '1.5rem',
                  cursor: 'pointer',
                  color: '#6b7280'
                }}
              >
                ×
              </button>
            </div>

            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.25rem'
                }}>
                  Company
                </label>
                <select
                  value={formData.company_id}
                  onChange={(e) => setFormData({ ...formData, company_id: e.target.value })}
                  required
                  style={{
                    width: '100%',
                    padding: '0.5rem 0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    boxSizing: 'border-box'
                  }}
                >
                  <option value="">Select a company</option>
                  {companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </select>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '0.25rem'
                  }}>
                    Sender Email
                  </label>
                  <input
                    type="email"
                    value={formData.sender_email}
                    onChange={(e) => setFormData({ ...formData, sender_email: e.target.value })}
                    required
                    style={{
                      width: '100%',
                      padding: '0.5rem 0.75rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '0.375rem',
                      fontSize: '0.875rem',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '0.25rem'
                  }}>
                    Sender Name
                  </label>
                  <input
                    type="text"
                    value={formData.sender_name}
                    onChange={(e) => setFormData({ ...formData, sender_name: e.target.value })}
                    required
                    style={{
                      width: '100%',
                      padding: '0.5rem 0.75rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '0.375rem',
                      fontSize: '0.875rem',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '2fr 1fr', gap: '1rem' }}>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '0.25rem'
                  }}>
                    SMTP Host
                  </label>
                  <input
                    type="text"
                    value={formData.smtp_host}
                    onChange={(e) => setFormData({ ...formData, smtp_host: e.target.value })}
                    required
                    placeholder="smtp.gmail.com"
                    style={{
                      width: '100%',
                      padding: '0.5rem 0.75rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '0.375rem',
                      fontSize: '0.875rem',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151',
                    marginBottom: '0.25rem'
                  }}>
                    SMTP Port
                  </label>
                  <input
                    type="number"
                    value={formData.smtp_port}
                    onChange={(e) => setFormData({ ...formData, smtp_port: parseInt(e.target.value) })}
                    required
                    style={{
                      width: '100%',
                      padding: '0.5rem 0.75rem',
                      border: '1px solid #d1d5db',
                      borderRadius: '0.375rem',
                      fontSize: '0.875rem',
                      boxSizing: 'border-box'
                    }}
                  />
                </div>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.25rem'
                }}>
                  SMTP Username
                </label>
                <input
                  type="text"
                  value={formData.smtp_username}
                  onChange={(e) => setFormData({ ...formData, smtp_username: e.target.value })}
                  required
                  style={{
                    width: '100%',
                    padding: '0.5rem 0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: '#374151',
                  marginBottom: '0.25rem'
                }}>
                  SMTP Password
                </label>
                <input
                  type="password"
                  value={formData.smtp_password}
                  onChange={(e) => setFormData({ ...formData, smtp_password: e.target.value })}
                  placeholder={editingConfig ? 'Leave blank to keep current password' : ''}
                  required={!editingConfig}
                  style={{
                    width: '100%',
                    padding: '0.5rem 0.75rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '0.375rem',
                    fontSize: '0.875rem',
                    boxSizing: 'border-box'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '0.875rem',
                  color: '#374151',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={formData.use_ssl}
                    onChange={(e) => setFormData({ ...formData, use_ssl: e.target.checked })}
                  />
                  Use SSL/TLS
                </label>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  fontSize: '0.875rem',
                  color: '#374151',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={formData.active}
                    onChange={(e) => setFormData({ ...formData, active: e.target.checked })}
                  />
                  Active
                </label>
              </div>

              <div style={{
                display: 'flex',
                gap: '0.75rem',
                justifyContent: 'flex-end',
                marginTop: '1rem'
              }}>
                <button
                  type="button"
                  onClick={resetForm}
                  style={{
                    padding: '0.5rem 1rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#374151',
                    backgroundColor: 'white',
                    border: '1px solid #d1d5db',
                    borderRadius: '0.375rem',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  style={{
                    padding: '0.5rem 1rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: 'white',
                    backgroundColor: '#4f46e5',
                    border: 'none',
                    borderRadius: '0.375rem',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#4338ca'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#4f46e5'}
                >
                  {editingConfig ? 'Update' : 'Create'} Configuration
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Mail Configurations List */}
      {loading ? (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '3rem 0'
        }}>
          <div style={{
            width: '3rem',
            height: '3rem',
            border: '4px solid #4f46e5',
            borderRadius: '50%',
            borderTopColor: 'transparent',
            animation: 'spin 1s linear infinite'
          }}></div>
        </div>
      ) : mailConfigs.length === 0 ? (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '3rem',
          backgroundColor: 'white',
          borderRadius: '0.5rem',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            padding: '1rem',
            color: '#4f46e5',
            backgroundColor: '#e0e7ff',
            borderRadius: '50%'
          }}>
            ⚙️
          </div>
          <h3 style={{
            marginTop: '1rem',
            fontSize: '1.125rem',
            fontWeight: '500',
            color: '#111827',
            margin: '1rem 0 0.25rem 0'
          }}>
            No mail configurations found
          </h3>
          <p style={{
            color: '#6b7280',
            margin: 0
          }}>
            Create your first mail configuration to start sending emails.
          </p>
          <button
            onClick={() => setShowForm(true)}
            style={{
              marginTop: '1rem',
              display: 'inline-flex',
              alignItems: 'center',
              padding: '0.5rem 1rem',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: 'white',
              backgroundColor: '#4f46e5',
              border: 'none',
              borderRadius: '0.375rem',
              textDecoration: 'none',
              boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#4338ca'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#4f46e5'}
          >
            Add Mail Configuration
          </button>
        </div>
      ) : (
        <div style={{
          overflow: 'hidden',
          backgroundColor: 'white',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
          borderRadius: '0.375rem'
        }}>
          <table style={{
            minWidth: '100%',
            borderCollapse: 'collapse'
          }}>
            <thead style={{ backgroundColor: '#f9fafb' }}>
              <tr>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  letterSpacing: '0.05em',
                  textAlign: 'left',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  borderBottom: '1px solid #e5e7eb'
                }}>
                  Sender
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  letterSpacing: '0.05em',
                  textAlign: 'left',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  borderBottom: '1px solid #e5e7eb'
                }}>
                  SMTP Host
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  letterSpacing: '0.05em',
                  textAlign: 'left',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  borderBottom: '1px solid #e5e7eb'
                }}>
                  Status
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  letterSpacing: '0.05em',
                  textAlign: 'left',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  borderBottom: '1px solid #e5e7eb'
                }}>
                  Test
                </th>
                <th style={{
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  letterSpacing: '0.05em',
                  textAlign: 'left',
                  color: '#6b7280',
                  textTransform: 'uppercase',
                  borderBottom: '1px solid #e5e7eb'
                }}>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody style={{ backgroundColor: 'white' }}>
              {mailConfigs.map((config, index) => (
                <tr key={config.id} style={{
                  borderBottom: index < mailConfigs.length - 1 ? '1px solid #e5e7eb' : 'none'
                }}>
                  <td style={{
                    padding: '1rem 1.5rem',
                    whiteSpace: 'nowrap'
                  }}>
                    <div>
                      <div style={{
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: '#111827'
                      }}>
                        {config.sender_name}
                      </div>
                      <div style={{
                        fontSize: '0.875rem',
                        color: '#6b7280'
                      }}>
                        {config.sender_email}
                      </div>
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    whiteSpace: 'nowrap'
                  }}>
                    <div style={{
                      fontSize: '0.875rem',
                      color: '#111827'
                    }}>
                      {config.smtp_host}:{config.smtp_port}
                    </div>
                    <div style={{
                      fontSize: '0.75rem',
                      color: '#6b7280'
                    }}>
                      {config.use_ssl ? 'SSL/TLS' : 'No SSL'}
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    whiteSpace: 'nowrap'
                  }}>
                    <div style={{
                      padding: '0.25rem 0.5rem',
                      fontSize: '0.75rem',
                      color: config.active ? '#166534' : '#6b7280',
                      backgroundColor: config.active ? '#dcfce7' : '#f3f4f6',
                      borderRadius: '9999px',
                      display: 'inline-block'
                    }}>
                      {config.active ? 'Active' : 'Inactive'}
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    whiteSpace: 'nowrap'
                  }}>
                    <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        value={testEmail}
                        onChange={(e) => setTestEmail(e.target.value)}
                        style={{
                          padding: '0.25rem 0.5rem',
                          border: '1px solid #d1d5db',
                          borderRadius: '0.25rem',
                          fontSize: '0.75rem',
                          width: '120px'
                        }}
                      />
                      <button
                        onClick={() => handleTest(config)}
                        disabled={testingConfig === config.id || !testEmail}
                        style={{
                          padding: '0.25rem 0.5rem',
                          fontSize: '0.75rem',
                          color: 'white',
                          backgroundColor: testingConfig === config.id ? '#6b7280' : '#059669',
                          border: 'none',
                          borderRadius: '0.25rem',
                          cursor: testingConfig === config.id || !testEmail ? 'not-allowed' : 'pointer',
                          opacity: testingConfig === config.id || !testEmail ? 0.5 : 1
                        }}
                      >
                        {testingConfig === config.id ? 'Testing...' : 'Test'}
                      </button>
                    </div>
                  </td>
                  <td style={{
                    padding: '1rem 1.5rem',
                    whiteSpace: 'nowrap',
                    fontSize: '0.875rem',
                    fontWeight: '500'
                  }}>
                    <div style={{
                      display: 'flex',
                      gap: '0.75rem'
                    }}>
                      <button
                        onClick={() => startEdit(config)}
                        style={{
                          color: '#2563eb',
                          backgroundColor: 'transparent',
                          border: 'none',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.color = '#1d4ed8'}
                        onMouseLeave={(e) => e.currentTarget.style.color = '#2563eb'}
                      >
                        ✏️
                      </button>
                      <button
                        onClick={() => handleDelete(config)}
                        style={{
                          color: '#dc2626',
                          backgroundColor: 'transparent',
                          border: 'none',
                          cursor: 'pointer'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.color = '#b91c1c'}
                        onMouseLeave={(e) => e.currentTarget.style.color = '#dc2626'}
                      >
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
