import { useState, useEffect } from 'react'
import { api } from '../lib/api'
import { Company } from '../types'

export default function Companies() {
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    loadCompanies()
  }, [])

  const loadCompanies = async () => {
    try {
      setLoading(true)
      const data = await api.get<Company[]>('/companies')
      setCompanies(data)
    } catch (err) {
      setError('Failed to load companies')
      console.error('Error loading companies:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div>
        <h1>Companies</h1>
        <p>Loading companies...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div>
        <h1>Companies</h1>
        <div style={{ 
          color: '#dc3545', 
          backgroundColor: '#f8d7da',
          padding: '1rem',
          borderRadius: '0.375rem'
        }}>
          {error}
        </div>
        <button 
          onClick={loadCompanies}
          style={{
            marginTop: '1rem',
            padding: '0.5rem 1rem',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '0.375rem',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <h1 style={{ margin: 0, color: '#495057' }}>Companies</h1>
        <button style={{
          padding: '0.75rem 1.5rem',
          backgroundColor: '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '0.375rem',
          cursor: 'pointer'
        }}>
          Add Company
        </button>
      </div>

      {companies.length === 0 ? (
        <div style={{
          textAlign: 'center',
          padding: '3rem',
          backgroundColor: '#f8f9fa',
          borderRadius: '0.5rem',
          border: '1px solid #dee2e6'
        }}>
          <p style={{ color: '#6c757d', fontSize: '1.1rem' }}>
            No companies found. Add your first company to get started.
          </p>
        </div>
      ) : (
        <div style={{
          backgroundColor: 'white',
          borderRadius: '0.5rem',
          border: '1px solid #dee2e6',
          overflow: 'hidden'
        }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f8f9fa' }}>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>
                  Name
                </th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>
                  Database Type
                </th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>
                  Started At
                </th>
                <th style={{ padding: '1rem', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {companies.map((company) => (
                <tr key={company.id}>
                  <td style={{ padding: '1rem', borderBottom: '1px solid #dee2e6' }}>
                    {company.name}
                  </td>
                  <td style={{ padding: '1rem', borderBottom: '1px solid #dee2e6' }}>
                    {company.database_type}
                  </td>
                  <td style={{ padding: '1rem', borderBottom: '1px solid #dee2e6' }}>
                    {new Date(company.started_at).toLocaleDateString()}
                  </td>
                  <td style={{ padding: '1rem', borderBottom: '1px solid #dee2e6' }}>
                    <button style={{
                      padding: '0.25rem 0.75rem',
                      backgroundColor: '#007bff',
                      color: 'white',
                      border: 'none',
                      borderRadius: '0.25rem',
                      cursor: 'pointer',
                      marginRight: '0.5rem'
                    }}>
                      Edit
                    </button>
                    <button style={{
                      padding: '0.25rem 0.75rem',
                      backgroundColor: '#dc3545',
                      color: 'white',
                      border: 'none',
                      borderRadius: '0.25rem',
                      cursor: 'pointer'
                    }}>
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}
