import { Link } from 'react-router-dom';
import { Building2, FileText, Mail, Settings } from 'lucide-react';

export default function Dashboard() {
  const stats = [
    { name: 'Total Companies', value: '12', icon: Building2, color: 'bg-blue-500' },
    { name: '<PERSON>ail Templates', value: '24', icon: FileText, color: 'bg-green-500' },
    { name: 'Emails Sent', value: '1,234', icon: Mail, color: 'bg-purple-500' },
    { name: 'Active Configs', value: '8', icon: Settings, color: 'bg-yellow-500' },
  ];

  const quickActions = [
    {
      title: 'Companies',
      description: 'Manage your client companies and their database connections.',
      href: '/companies',
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      title: 'Email Templates',
      description: 'Create and manage email templates for your automation campaigns.',
      href: '/templates',
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      title: 'Email Sender',
      description: 'Send emails to your customers using templates and automation.',
      href: '/email-sender',
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      title: 'Mail Configuration',
      description: 'Configure SMTP settings and email delivery options.',
      href: '/mail-config',
      color: 'bg-yellow-600 hover:bg-yellow-700',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-1 text-sm text-gray-600">
          Welcome to the Email Automation Admin Dashboard
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`${stat.color} p-3 rounded-md`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          {quickActions.map((action) => (
            <Link
              key={action.title}
              to={action.href}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {action.title}
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {action.description}
                </p>
                <div className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white ${action.color} transition-colors`}>
                  Go to {action.title}
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Welcome Message */}
      <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <div className="h-8 w-8 bg-indigo-400 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-medium">!</span>
            </div>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-indigo-800">
              Welcome to Psynarios Email Automation
            </h3>
            <div className="mt-2 text-sm text-indigo-700">
              <p>
                Use the navigation menu to manage companies, templates, and email configurations.
                Get started by adding your first company or creating an email template.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
