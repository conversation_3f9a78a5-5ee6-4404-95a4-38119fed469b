import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ToastProvider } from './contexts/ToastContext';
import { ToastContainer } from './components/ui/Toast';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Layout from './components/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Companies from './pages/Companies';
import Templates from './pages/Templates';
import EmailSender from './pages/EmailSender';
import MailConfig from './pages/MailConfig';

function App() {
  return (
    <AuthProvider>
      <ToastProvider>
        <div className="min-h-screen bg-gray-100">
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="companies" element={<Companies />} />
              <Route path="templates" element={<Templates />} />
              <Route path="email-sender" element={<EmailSender />} />
              <Route path="mail-config" element={<MailConfig />} />
            </Route>
          </Routes>
          <ToastContainer />
        </div>
      </ToastProvider>
    </AuthProvider>
  );
}

export default App;
