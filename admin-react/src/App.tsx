import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ToastProvider } from './contexts/ToastContext';
import { ToastContainer } from './components/ui/Toast';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Layout from './components/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Companies from './pages/Companies';
import Templates from './pages/Templates';

function App() {
  console.log('🚀 [APP] App component rendering');

  // Simple test to see if the app is loading
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">React App Loading Test</h1>
      <p className="text-gray-600">If you can see this, React is working!</p>

      <AuthProvider>
        <ToastProvider>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="companies" element={<Companies />} />
              <Route path="templates" element={<Templates />} />
              <Route path="email-sender" element={<div className="p-8">Email Sender - Coming Soon</div>} />
              <Route path="mail-config" element={<div className="p-8">Mail Config - Coming Soon</div>} />
            </Route>
            {/* Catch all route for debugging */}
            <Route path="*" element={<div className="p-8">Route not found. Current path: {window.location.pathname}</div>} />
          </Routes>
          <ToastContainer />
        </ToastProvider>
      </AuthProvider>
    </div>
  );
}

export default App;
