import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON>rowserRouter } from 'react-router-dom'
import './index.css'
import App from './App.tsx'
import { csrfManager } from './lib/csrf'

// Simple error boundary
function ErrorFallback({ error }: { error?: Error }) {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>Something went wrong</h1>
      <p>Check the browser console for errors</p>
      {error && <pre style={{ color: 'red', textAlign: 'left' }}>{error.message}</pre>}
      <button onClick={() => window.location.reload()}>Reload Page</button>
    </div>
  )
}

// Initialize CSRF token on app startup
csrfManager.initialize().catch(console.error);

try {
  createRoot(document.getElementById('root')!).render(
    <StrictMode>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </StrictMode>,
  )
} catch (error) {
  console.error('Error rendering app:', error)
  createRoot(document.getElementById('root')!).render(<ErrorFallback error={error as Error} />)
}
