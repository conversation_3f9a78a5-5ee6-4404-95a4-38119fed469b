import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { BrowserRouter } from 'react-router-dom'
import './index.css'

// Simple error boundary
function ErrorFallback() {
  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <h1>Something went wrong</h1>
      <p>Check the browser console for errors</p>
      <button onClick={() => window.location.reload()}>Reload Page</button>
    </div>
  )
}

// Simple test component first
function SimpleApp() {
  return (
    <div style={{ padding: '20px' }}>
      <h1>React App is Working!</h1>
      <p>Current URL: {window.location.href}</p>
      <div style={{ marginTop: '20px' }}>
        <a href="/login" style={{ marginRight: '10px', color: 'blue' }}>Login</a>
        <a href="/dashboard" style={{ color: 'blue' }}>Dashboard</a>
      </div>
    </div>
  )
}

try {
  createRoot(document.getElementById('root')!).render(
    <StrictMode>
      <BrowserRouter>
        <SimpleApp />
      </BrowserRouter>
    </StrictMode>,
  )
} catch (error) {
  console.error('Error rendering app:', error)
  createRoot(document.getElementById('root')!).render(<ErrorFallback />)
}
