import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'

// Ultra simple test component
function TestApp() {
  console.log('TestApp rendering...')
  return (
    <div style={{ padding: '20px', backgroundColor: 'lightblue' }}>
      <h1>TEST APP WORKING</h1>
      <p>If you can see this, <PERSON><PERSON> is working</p>
      <p>Current time: {new Date().toLocaleTimeString()}</p>
    </div>
  )
}

console.log('main.tsx loading...')

try {
  const rootElement = document.getElementById('root')
  console.log('Root element:', rootElement)

  if (!rootElement) {
    throw new Error('Root element not found')
  }

  const root = createRoot(rootElement)
  console.log('Root created:', root)

  root.render(
    <StrictMode>
      <TestApp />
    </StrictMode>
  )

  console.log('App rendered successfully')
} catch (error) {
  console.error('Error in main.tsx:', error)
  document.body.innerHTML = `
    <div style="padding: 20px; color: red;">
      <h1>Error in main.tsx</h1>
      <pre>${error}</pre>
    </div>
  `
}
