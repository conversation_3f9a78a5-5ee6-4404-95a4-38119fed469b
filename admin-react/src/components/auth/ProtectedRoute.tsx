import { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

interface ProtectedRouteProps {
  children: ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { user, loading } = useAuth();
  const location = useLocation();

  console.log('🛡️ [PROTECTED] ProtectedRoute - user:', user?.email || 'none', 'loading:', loading, 'path:', location.pathname);

  if (loading) {
    console.log('🛡️ [PROTECTED] Showing loading spinner');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!user) {
    console.log('🛡️ [PROTECTED] No user, redirecting to login');
    // Redirect to login page with return url
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  console.log('🛡️ [PROTECTED] User authenticated, rendering children');
  return <>{children}</>;
}
