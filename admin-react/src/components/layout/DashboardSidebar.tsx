import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  Building2, 
  FileText, 
  Mail, 
  Settings, 
  Menu 
} from 'lucide-react';
import { cn } from '../../lib/utils';

export default function DashboardSidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const isActive = (path: string) => {
    return location.pathname.startsWith(path);
  };

  const navItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: <Home size={20} />,
    },
    {
      name: 'Companies',
      href: '/companies',
      icon: <Building2 size={20} />,
    },
    {
      name: 'Templates',
      href: '/templates',
      icon: <FileText size={20} />,
    },
    {
      name: 'Email Sender',
      href: '/email-sender',
      icon: <Mail size={20} />,
    },
    {
      name: 'Mail Config',
      href: '/mail-config',
      icon: <Settings size={20} />,
    }
  ];

  return (
    <div className={cn(
      'bg-indigo-800 text-white transition-all duration-300',
      collapsed ? 'w-16' : 'w-80'
    )}>
      <div className="p-4 flex items-center justify-between">
        {!collapsed && (
          <img
            src="/images/psynarios-logo.png"
            alt="Psynarios Logo"
            className="h-10 w-auto"
          />
        )}
        <button 
          onClick={toggleSidebar}
          className="p-2 rounded-md hover:bg-indigo-700 transition-colors"
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <Menu size={24} />
        </button>
      </div>

      <nav className="mt-8">
        <ul className="space-y-2 px-2">
          {navItems.map((item) => (
            <li key={item.href}>
              <Link
                to={item.href}
                className={cn(
                  'flex items-center p-3 rounded-md transition-colors',
                  isActive(item.href)
                    ? 'bg-indigo-700'
                    : 'hover:bg-indigo-700'
                )}
              >
                <span className="mr-3">{item.icon}</span>
                {!collapsed && <span>{item.name}</span>}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}
