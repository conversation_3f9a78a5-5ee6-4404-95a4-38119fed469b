import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  Building2,
  FileText,
  Mail,
  Settings,
  Menu
} from 'lucide-react';

export default function DashboardSidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const isActive = (path: string) => {
    return location.pathname.startsWith(path);
  };

  const navItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: <Home size={20} />,
    },
    {
      name: 'Companies',
      href: '/companies',
      icon: <Building2 size={20} />,
    },
    {
      name: 'Templates',
      href: '/templates',
      icon: <FileText size={20} />,
    },
    {
      name: 'Email Sender',
      href: '/email-sender',
      icon: <Mail size={20} />,
    },
    {
      name: 'Mail Config',
      href: '/mail-config',
      icon: <Settings size={20} />,
    }
  ];

  return (
    <div style={{
      backgroundColor: '#3730a3',
      color: 'white',
      width: collapsed ? '4rem' : '20rem',
      transition: 'width 0.3s ease',
      height: '100vh',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{
        padding: '1rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        {!collapsed && (
          <img
            src="/images/psynarios-logo.png"
            alt="Psynarios Logo"
            style={{
              height: '2.5rem',
              width: 'auto'
            }}
            onError={(e) => {
              // Fallback if logo doesn't exist
              e.currentTarget.style.display = 'none';
              const fallback = e.currentTarget.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = 'block';
            }}
          />
        )}
        {!collapsed && (
          <div
            style={{
              display: 'none',
              fontSize: '1.25rem',
              fontWeight: 'bold'
            }}
          >
            Psynarios
          </div>
        )}
        <button
          onClick={toggleSidebar}
          style={{
            padding: '0.5rem',
            borderRadius: '0.375rem',
            backgroundColor: 'transparent',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            transition: 'background-color 0.2s'
          }}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#4338ca'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <Menu size={24} />
        </button>
      </div>

      <nav style={{ marginTop: '2rem', flex: 1 }}>
        <ul style={{
          listStyle: 'none',
          padding: '0 0.5rem',
          margin: 0,
          display: 'flex',
          flexDirection: 'column',
          gap: '0.5rem'
        }}>
          {navItems.map((item) => (
            <li key={item.href}>
              <Link
                to={item.href}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '0.75rem',
                  borderRadius: '0.375rem',
                  textDecoration: 'none',
                  color: 'white',
                  backgroundColor: isActive(item.href) ? '#4338ca' : 'transparent',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!isActive(item.href)) {
                    e.currentTarget.style.backgroundColor = '#4338ca';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive(item.href)) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <span style={{ marginRight: '0.75rem' }}>{item.icon}</span>
                {!collapsed && <span>{item.name}</span>}
              </Link>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
}
