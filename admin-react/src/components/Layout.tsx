import { Outlet } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import DashboardSidebar from './layout/DashboardSidebar';
import DashboardHeader from './layout/DashboardHeader';

export default function Layout() {
  const { user } = useAuth();

  if (!user) {
    return null; // This shouldn't happen due to route protection, but just in case
  }

  return (
    <div style={{
      display: 'flex',
      height: '100vh',
      backgroundColor: '#f3f4f6'
    }}>
      {/* Sidebar */}
      <DashboardSidebar />

      {/* Main content area */}
      <div style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* Header */}
        <DashboardHeader user={user} />

        {/* Main content */}
        <main style={{
          flex: 1,
          overflowY: 'auto',
          padding: '1rem'
        }}>
          <Outlet />
        </main>
      </div>
    </div>
  );
}
