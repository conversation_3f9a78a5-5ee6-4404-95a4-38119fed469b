import { Outlet } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import DashboardSidebar from './layout/DashboardSidebar';
import DashboardHeader from './layout/DashboardHeader';

export default function Layout() {
  const { user } = useAuth();

  if (!user) {
    return null; // This shouldn't happen due to route protection, but just in case
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <DashboardSidebar />

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <DashboardHeader user={user} />

        {/* Main content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
}
