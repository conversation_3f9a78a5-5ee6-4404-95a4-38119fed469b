export interface MailConfig {
    id: string;
    company_id: string;
    sender_email: string;
    sender_name: string;
    smtp_host: string;
    smtp_port: number;
    smtp_username: string;
    use_ssl: boolean;
    active: boolean;
    created_at: string;
    updated_at: string;
  }
  
  export interface MailConfigResponse {
    mail_config: MailConfig;
    message?: string;
  }
  
  export interface MailConfigsListResponse {
    mail_configs: MailConfig[];
    total?: number;
    message?: string;
  }
  
  export interface CreateMailConfigRequest {
    company_id: string;
    sender_email: string;
    sender_name?: string;
    smtp_host: string;
    smtp_port: number;
    smtp_username: string;
    smtp_password?: string;
    use_ssl: boolean;
    active: boolean;
  }
  
  export interface UpdateMailConfigRequest {
    company_id?: string;
    sender_email?: string;
    sender_name?: string;
    smtp_host?: string;
    smtp_port?: number;
    smtp_username?: string;
    smtp_password?: string;
    use_ssl?: boolean;
    active?: boolean;
  }
