// Template model
export interface Template {
  id: string;
  name: string;
  subject: string;
  body?: string;
  content?: string;
  group_id: string;
  week_number: number;
  type?: string;
  default_data?: Record<string, any>;
  required_fields?: string[];
  is_active?: boolean;
  created_at: string;
  updated_at: string;
}

// Template Group model
export interface TemplateGroup {
  id: string;
  name: string;
  description?: string;
  company_ids: string[];
  day_number: number;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Form data for creating/updating templates
export interface TemplateFormData {
  name: string;
  subject: string;
  content: string;
  group_id: string;
  week_number: number;
  type?: string;
  default_data?: Record<string, any>;
  required_fields?: string[];
  is_active?: boolean;
}

// Form data for creating/updating template groups
export interface TemplateGroupFormData {
  name: string;
  description: string;
  company_ids: string[];
  day_number: number;
  is_active?: boolean;
}

// Template preview data
export interface TemplatePreviewData {
  name?: string;
  email?: string;
  company?: string;
  score?: string;
  [key: string]: string | undefined;
}
