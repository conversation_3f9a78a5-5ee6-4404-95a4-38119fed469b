'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  getMailConfigs, 
  toggleMailConfigStatus, 
  deleteMailConfig, 
  testMailConfig 
} from '@/lib/api/mail-config';
import { getCompanies } from '@/lib/api/companies';
import { MailConfig } from '@/types/mail-config';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useToast } from '@/context/toast-context';

interface Company {
  id: string;
  name: string;
  connection_string: string;
  database_type: string;
  started_at: string;
  created_at: string;
  updated_at: string;
}

export default function MailConfigPage() {
  const [configs, setConfigs] = useState<MailConfig[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [testingId, setTestingId] = useState<string | null>(null);
  const { showToast } = useToast();
  
  // Modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [configToDelete, setConfigToDelete] = useState<MailConfig | null>(null);
  
  // Test email state
  const [showTestEmailInput, setShowTestEmailInput] = useState(false);
  const [configToTest, setConfigToTest] = useState<MailConfig | null>(null);
  const [testEmail, setTestEmail] = useState('');
  const [testPassword, setTestPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  
  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        console.log('Starting to load data...');
        
        // First load companies to ensure we have them for mapping
        console.log('Fetching companies...');
        const companiesResponse = await getCompanies();
        console.log('Companies response:', companiesResponse);
        
        if (Array.isArray(companiesResponse)) {
          setCompanies(companiesResponse);
        } else {
          console.error('Invalid companies response format:', companiesResponse);
          // Create empty companies array to prevent errors
          setCompanies([]);
        }
        
        // Then load mail configs
        console.log('Fetching mail configs...');
        const configsData = await getMailConfigs();
        console.log('Mail configs response:', configsData);
        
        if (configsData && configsData.mail_configs) {
          setConfigs(configsData.mail_configs);
          console.log('Mail configs set:', configsData.mail_configs);
        } else {
          console.error('Invalid mail configs response format:', configsData);
          // Create empty configs array to prevent errors
          setConfigs([]);
        }
      } catch (err: any) {
        console.error('Error loading data:', err);
        showToast('Failed to load mail configurations');
        setError(`Failed to load mail configurations: ${err.message || 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, [showToast]);
  
  function getCompanyName(companyId: string): string {
    if (!companyId) return 'No Company';
    
    const company = companies.find(c => c.id === companyId);
    if (!company) {
      console.warn(`Company with ID ${companyId} not found`);
      return 'Unknown Company';
    }
    
    return company.name;
  }
  
  async function handleToggleActive(config: MailConfig) {
    try {
      // Update the configuration status using our direct export function
      await toggleMailConfigStatus(config.id, !config.active);
      
      // Update local state
      setConfigs(configs.map(c => 
        c.id === config.id ? { ...c, active: !c.active } : c
      ));
      
      // Show success toast
      showToast(`Configuration "${config.sender_name || 'Unnamed'}" ${!config.active ? 'activated' : 'deactivated'} successfully`);
    } catch (err: any) {
      console.error('Error updating mail configuration:', err);
      showToast('Failed to update configuration status. Please try again.');
    }
  }
  
  function openDeleteModal(config: MailConfig) {
    setConfigToDelete(config);
    setIsDeleteModalOpen(true);
  }
  
  async function confirmDelete() {
    if (!configToDelete) return;
    
    try {
      // Delete the configuration using our direct export function
      await deleteMailConfig(configToDelete.id);
      
      // Update local state
      setConfigs(configs.filter(c => c.id !== configToDelete.id));
      
      // Show success toast
      showToast(`Configuration "${configToDelete.sender_name || 'Unnamed'}" deleted successfully`);
      
      // Close modal
      setIsDeleteModalOpen(false);
      setConfigToDelete(null);
    } catch (err: any) {
      console.error('Error deleting mail configuration:', err);
      showToast('Failed to delete configuration. Please try again.');
    }
  }
  
  function openTestEmailInput(config: MailConfig) {
    setConfigToTest(config);
    setTestEmail(''); // Reset test email input
    setTestPassword(''); // Reset test password input
    setShowPassword(false); // Hide password by default
    setShowTestEmailInput(true);
  }

  async function handleTestConnection() {
    if (!configToTest) return;
    
    // Use either the provided test email or fall back to the sender email
    const emailToTest = testEmail.trim() || configToTest.sender_email;
    
    if (!emailToTest) {
      showToast('A test email address is required. Please provide one.');
      return;
    }
    
    setTestingId(configToTest.id);
    
    try {
      // Create a copy of the config to test
      const testConfig = {
        ...configToTest,
        // Override the password if one was provided
        smtp_password: testPassword.trim() || configToTest.smtp_password || "[UNCHANGED]"
      };
      
      // Test the configuration using our direct export function
      const result = await testMailConfig(testConfig, emailToTest);
      
      // Show success/error message
      if (result.success) {
        showToast(`SMTP connection test successful! Test email sent to ${emailToTest}`);
        setShowTestEmailInput(false);
        setConfigToTest(null);
      } else {
        showToast(`SMTP connection test failed: ${result.message || result.error}`);
      }
    } catch (err: any) {
      console.error('Error testing SMTP connection:', err);
      showToast('SMTP connection test failed. Please check your settings and try again.');
    } finally {
      setTestingId(null);
    }
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">SMTP Configurations</h1>
        <Link 
          href="/mail-config/new" 
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
        >
          <i className="mr-2 fas fa-plus"></i>
          New Configuration
        </Link>
      </div>
      
      {error && (
        <div className="p-4 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
        </div>
      ) : !configs || configs.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-12 bg-white rounded-lg shadow">
          <div className="p-4 text-indigo-500 bg-indigo-100 rounded-full">
            <i className="text-2xl fas fa-server"></i>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">No SMTP configurations found</h3>
          <p className="mt-1 text-gray-500">
            Create a new SMTP configuration to send emails.
          </p>
          <Link 
            href="/mail-config/new" 
            className="mt-4 inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
          >
            <i className="mr-2 fas fa-plus"></i>
            Add SMTP Configuration
          </Link>
        </div>
      ) : (
        <div className="overflow-hidden bg-white rounded-lg shadow">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sender Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Company
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  SMTP Settings
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  From Email
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {configs.map(config => (
                <tr key={config.id} className={!config.active ? "bg-gray-50" : ""}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-gray-900">{config.sender_name || 'Unnamed'}</div>
                      {!config.active && (
                        <span className="ml-2 px-2 py-0.5 text-xs text-gray-500 bg-gray-100 rounded-full">Inactive</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{getCompanyName(config.company_id)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{`${config.smtp_host}:${config.smtp_port}`}</div>
                    <div className="text-xs text-gray-500">{config.use_ssl ? 'SSL/TLS' : 'No SSL'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{config.sender_email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      config.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {config.active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-4">
                      <button 
                        onClick={() => openTestEmailInput(config)}
                        disabled={testingId === config.id}
                        className="text-indigo-600 hover:text-indigo-900 text-lg"
                        title="Test Connection"
                      >
                        {testingId === config.id ? (
                          <i className="fas fa-spinner fa-spin"></i>
                        ) : (
                          <span>🔌</span>
                        )}
                      </button>
                      <button
                        onClick={() => handleToggleActive(config)}
                        className={`${
                          config.active 
                            ? 'text-yellow-600 hover:text-yellow-900' 
                            : 'text-green-600 hover:text-green-900'
                        } text-lg`}
                        title={config.active ? 'Deactivate' : 'Activate'}
                      >
                        <span>{config.active ? '⏸️' : '▶️'}</span>
                      </button>
                      <Link 
                        href={`/mail-config/${config.id}`} 
                        className="text-indigo-600 hover:text-indigo-900 text-lg"
                        title="View Details"
                      >
                        <span>👁️</span>
                      </Link>
                      <Link 
                        href={`/mail-config/${config.id}/edit`} 
                        className="text-blue-600 hover:text-blue-900 text-lg"
                        title="Edit"
                      >
                        <span>✏️</span>
                      </Link>
                      <button 
                        onClick={() => openDeleteModal(config)}
                        className="text-red-600 hover:text-red-900 text-lg"
                        title="Delete"
                      >
                        <span>🗑️</span>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        title="Delete SMTP Configuration"
        message={`Are you sure you want to delete "${configToDelete?.sender_name || 'this configuration'}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
        showSuccessToast={false} 
        onConfirm={confirmDelete}
        onCancel={() => {
          setIsDeleteModalOpen(false);
          setConfigToDelete(null);
        }}
      />
      
      {/* Test Email Input Modal */}
      <div className={`fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full ${showTestEmailInput ? 'flex' : 'hidden'} items-center justify-center`}>
        <div className="relative mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div className="mt-3">
            <h3 className="text-lg leading-6 font-medium text-gray-900 text-center">Test SMTP Connection</h3>
            <div className="mt-4 space-y-4">
              <div>
                <label htmlFor="test_email" className="block text-sm font-medium text-gray-700">
                  Test Email Address
                </label>
                <input
                  type="email"
                  id="test_email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder={configToTest?.sender_email || "Enter email address"}
                  className="mt-1 px-4 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Enter an email address to receive the test message.
                  {configToTest?.sender_email ? ` Leave empty to use ${configToTest.sender_email}.` : ''}
                </p>
              </div>
              
              <div>
                <label htmlFor="test_password" className="block text-sm font-medium text-gray-700">
                  SMTP Password (for test only)
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="test_password"
                    value={testPassword}
                    onChange={(e) => setTestPassword(e.target.value)}
                    placeholder="Override password for testing"
                    className="px-4 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  >
                    <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                  </button>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  Optional. Enter a password to override the stored one for this test.
                </p>
              </div>
            </div>
            <div className="mt-5 flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowTestEmailInput(false);
                  setConfigToTest(null);
                }}
                className="px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                onClick={handleTestConnection}
                disabled={testingId !== null}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                {testingId !== null ? (
                  <div className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Testing...
                  </div>
                ) : (
                  'Send Test'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}