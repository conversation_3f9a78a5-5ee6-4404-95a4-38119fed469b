// lib/api/auth.ts - Simplified fix with direct exports
import { useFetchAP<PERSON>, useApiMethods } from './fetchAPI';

interface LoginCredentials {
  email: string;
  password: string;
}

interface AuthResponse {
  success: boolean;
  token?: string;
  user?: any;
  message?: string;
  csrf_token?: string;
  expires_in?: number;
}

// Helper function to get API methods
function getApiMethods() {
  if (typeof window !== 'undefined') {
    return useApiMethods();
  }
  throw new Error('API methods not available in server-side environment');
}

/**
 * Hook for authentication API calls (use in React components)
 */
export function useAuthAPI() {
  const api = useApiMethods();
  
  return {
    // Login and get token with CSRF support
    async login(credentials: LoginCredentials): Promise<AuthResponse> {
      try {
        console.log('🔐 Login API call with credentials:', credentials.email);
        
        const data = await api.post<any>('/auth/login', credentials);
        console.log('✅ Login API response:', data);

        if (data.user) {
          // Store user info in localStorage for easy access
          localStorage.setItem('user', JSON.stringify(data.user));
          
          // Store session expiry if provided
          if (data.expires_in) {
            const expiresAt = Math.floor(Date.now() / 1000) + data.expires_in;
            localStorage.setItem('session_expires_at', expiresAt.toString());
          }
        }

        return {
          success: true,
          token: data.token,
          user: data.user,
          csrf_token: data.csrf_token,
          expires_in: data.expires_in,
        };
      } catch (error: any) {
        console.error('❌ Login error:', error);
        return {
          success: false,
          message: error.message || 'Network error occurred',
        };
      }
    },

    // Logout with CSRF support
    async logout(): Promise<boolean> {
      try {
        await api.post<any>('/auth/logout', {});

        // Clear all auth data
        localStorage.removeItem('user');
        localStorage.removeItem('session_expires_at');
        
        return true;
      } catch (error: any) {
        console.error('❌ Logout error:', error);
        
        // Even if logout fails, clear local data
        localStorage.removeItem('user');
        localStorage.removeItem('session_expires_at');
        
        return false;
      }
    },

    // Check authentication status
    async checkAuthStatus(): Promise<{ authenticated: boolean; user?: any }> {
      try {
        const response = await api.get<any>('/auth/status');

        if (response.authenticated && response.user) {
          localStorage.setItem('user', JSON.stringify(response.user));
          return {
            authenticated: true,
            user: response.user,
          };
        }

        return { authenticated: false };
      } catch (error) {
        console.error('❌ Auth status check error:', error);
        return { authenticated: false };
      }
    },

    // Change password with CSRF support
    async changePassword(currentPassword: string, newPassword: string): Promise<{ success: boolean; message: string }> {
      try {
        const response = await api.post<any>('/auth/change-password', {
          current_password: currentPassword,
          new_password: newPassword,
        });

        return {
          success: true,
          message: response.message || 'Password changed successfully',
        };
      } catch (error: any) {
        console.error('❌ Change password error:', error);
        return {
          success: false,
          message: error.message || 'Failed to change password',
        };
      }
    },

    // Get current user from API
    async getCurrentUserFromAPI(): Promise<any | null> {
      try {
        const response = await api.get<any>('/auth/me');

        if (response.user) {
          localStorage.setItem('user', JSON.stringify(response.user));
          return response.user;
        }

        return null;
      } catch (error) {
        console.error('❌ Error getting current user from API:', error);
        return null;
      }
    },

    // Refresh token/session
    async refreshSession(): Promise<AuthResponse> {
      try {
        const response = await api.post<any>('/auth/refresh', {});
        
        if (response.user) {
          localStorage.setItem('user', JSON.stringify(response.user));
          
          if (response.expires_in) {
            const expiresAt = Math.floor(Date.now() / 1000) + response.expires_in;
            localStorage.setItem('session_expires_at', expiresAt.toString());
          }
        }

        return {
          success: true,
          token: response.token,
          user: response.user,
          csrf_token: response.csrf_token,
          expires_in: response.expires_in,
        };
      } catch (error: any) {
        console.error('❌ Session refresh error:', error);
        return {
          success: false,
          message: error.message || 'Failed to refresh session',
        };
      }
    }
  };
}

// Direct export functions
export async function login(credentials: LoginCredentials): Promise<AuthResponse> {
  const api = getApiMethods();
  try {
    console.log('🔐 Login API call with credentials:', credentials.email);
    
    const data = await api.post<any>('/auth/login', credentials);
    console.log('✅ Login API response:', data);

    if (data.user) {
      // Store user info in localStorage for easy access
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Store session expiry if provided
      if (data.expires_in) {
        const expiresAt = Math.floor(Date.now() / 1000) + data.expires_in;
        localStorage.setItem('session_expires_at', expiresAt.toString());
      }
    }

    return {
      success: true,
      token: data.token,
      user: data.user,
      csrf_token: data.csrf_token,
      expires_in: data.expires_in,
    };
  } catch (error: any) {
    console.error('❌ Login error:', error);
    return {
      success: false,
      message: error.message || 'Network error occurred',
    };
  }
}

export async function logout(): Promise<boolean> {
  const api = getApiMethods();
  try {
    await api.post<any>('/auth/logout', {});

    // Clear all auth data
    localStorage.removeItem('user');
    localStorage.removeItem('session_expires_at');
    
    return true;
  } catch (error: any) {
    console.error('❌ Logout error:', error);
    
    // Even if logout fails, clear local data
    localStorage.removeItem('user');
    localStorage.removeItem('session_expires_at');
    
    return false;
  }
}

export async function checkAuthStatus(): Promise<{ authenticated: boolean; user?: any }> {
  const api = getApiMethods();
  try {
    const response = await api.get<any>('/auth/status');

    if (response.authenticated && response.user) {
      localStorage.setItem('user', JSON.stringify(response.user));
      return {
        authenticated: true,
        user: response.user,
      };
    }

    return { authenticated: false };
  } catch (error) {
    console.error('❌ Auth status check error:', error);
    return { authenticated: false };
  }
}

export async function changePassword(currentPassword: string, newPassword: string): Promise<{ success: boolean; message: string }> {
  const api = getApiMethods();
  try {
    const response = await api.post<any>('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    });

    return {
      success: true,
      message: response.message || 'Password changed successfully',
    };
  } catch (error: any) {
    console.error('❌ Change password error:', error);
    return {
      success: false,
      message: error.message || 'Failed to change password',
    };
  }
}

export async function getCurrentUserFromAPI(): Promise<any | null> {
  const api = getApiMethods();
  try {
    const response = await api.get<any>('/auth/me');

    if (response.user) {
      localStorage.setItem('user', JSON.stringify(response.user));
      return response.user;
    }

    return null;
  } catch (error) {
    console.error('❌ Error getting current user from API:', error);
    return null;
  }
}

export async function refreshSession(): Promise<AuthResponse> {
  const api = getApiMethods();
  try {
    const response = await api.post<any>('/auth/refresh', {});
    
    if (response.user) {
      localStorage.setItem('user', JSON.stringify(response.user));
      
      if (response.expires_in) {
        const expiresAt = Math.floor(Date.now() / 1000) + response.expires_in;
        localStorage.setItem('session_expires_at', expiresAt.toString());
      }
    }

    return {
      success: true,
      token: response.token,
      user: response.user,
      csrf_token: response.csrf_token,
      expires_in: response.expires_in,
    };
  } catch (error: any) {
    console.error('❌ Session refresh error:', error);
    return {
      success: false,
      message: error.message || 'Failed to refresh session',
    };
  }
}

export async function requestPasswordReset(email: string): Promise<{ success: boolean; message: string }> {
  const api = getApiMethods();
  try {
    const response = await api.post<any>('/auth/forgot-password', { email });

    return {
      success: true,
      message: response.message || 'Password reset email sent successfully',
    };
  } catch (error: any) {
    console.error('❌ Password reset request error:', error);
    return {
      success: false,
      message: error.message || 'Failed to send password reset email',
    };
  }
}

export async function resetPassword(token: string, newPassword: string): Promise<{ success: boolean; message: string }> {
  const api = getApiMethods();
  try {
    const response = await api.post<any>('/auth/reset-password', {
      token,
      new_password: newPassword,
    });

    return {
      success: true,
      message: response.message || 'Password reset successfully',
    };
  } catch (error: any) {
    console.error('❌ Password reset error:', error);
    return {
      success: false,
      message: error.message || 'Failed to reset password',
    };
  }
}

// Client-side helper functions (non-API) - these don't require API calls
export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') return false; // SSR safety
  const user = localStorage.getItem('user');
  return !!user;
}

export function getCurrentUser(): any | null {
  if (typeof window === 'undefined') return null; // SSR safety
  try {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

// Check if session is expired based on localStorage
export function isSessionExpired(): boolean {
  if (typeof window === 'undefined') return true; // SSR safety
  try {
    const expiresAt = localStorage.getItem('session_expires_at');
    if (!expiresAt) return true;

    const expiry = parseInt(expiresAt, 10);
    const now = Math.floor(Date.now() / 1000);
    
    return now >= expiry;
  } catch (error) {
    console.error('Error checking session expiry:', error);
    return true;
  }
}

// Get time until session expires (in seconds)
export function getTimeUntilExpiry(): number {
  if (typeof window === 'undefined') return 0; // SSR safety
  try {
    const expiresAt = localStorage.getItem('session_expires_at');
    if (!expiresAt) return 0;

    const expiry = parseInt(expiresAt, 10);
    const now = Math.floor(Date.now() / 1000);
    
    return Math.max(0, expiry - now);
  } catch (error) {
    console.error('Error getting time until expiry:', error);
    return 0;
  }
}

// Clear all authentication data
export function clearAuthData(): void {
  if (typeof window === 'undefined') return; // SSR safety
  localStorage.removeItem('user');
  localStorage.removeItem('session_expires_at');
}

// Auto-logout when session expires
export function setupAutoLogout(onExpiry?: () => void): () => void {
  if (typeof window === 'undefined') return () => {}; // SSR safety
  
  const checkExpiry = () => {
    if (isSessionExpired() && isAuthenticated()) {
      console.log('🔒 Session expired, auto-logging out');
      clearAuthData();
      if (onExpiry) {
        onExpiry();
      }
    }
  };

  // Check every minute
  const interval = setInterval(checkExpiry, 60000);
  
  // Return cleanup function
  return () => clearInterval(interval);
}

// Export types for external use
export type { LoginCredentials, AuthResponse };