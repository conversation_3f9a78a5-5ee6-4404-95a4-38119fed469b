// lib/api/companies.ts - Simplified fix with direct exports
import { useApiMethods, useFetchAPI } from './fetchAPI';
import type { Company, CompanyFormData } from '@/types/companies';

// Helper function to get API methods
function getApiMethods() {
  if (typeof window !== 'undefined') {
    return useApiMethods();
  }
  throw new Error('API methods not available in server-side environment');
}

function getFetchAPI() {
  if (typeof window !== 'undefined') {
    return useFetchAPI();
  }
  throw new Error('Fetch API not available in server-side environment');
}

/**
 * Hook for companies API calls (use in React components)
 */
export function useCompaniesAPI() {
  const api = useApiMethods();
  
  const fetchWithContext = useFetchAPI();

  return {
    async getCompanies(): Promise<Company[]> {
      try {
        const response = await api.get<Company[] | { companies: Company[] } | null>('/companies');
        
        if (Array.isArray(response)) {
          return response;
        } else if (response && 'companies' in response) {
          return response.companies;
        }
        
        return [];
      } catch (error) {
        console.error('❌ Failed to fetch companies:', error);
        return [];
      }
    },

    async getCompanyById(id: string): Promise<Company | null> {
      try {
        const response = await api.get<Company | { company: Company } | null>(`/companies/${id}`);
        
        if (response && 'company' in response) {
          return response.company;
        }
        
        return response as Company;
      } catch (error) {
        console.error(`❌ Failed to fetch company ${id}:`, error);
        return null;
      }
    },

    async createCompany(data: CompanyFormData): Promise<Company> {
      try {
        const formattedData = {
          ...data,
          started_at: data.started_at ? new Date(data.started_at).toISOString() : ''
        };

        const response = await api.post<Company | { company: Company }>('/companies', formattedData);

        if (response && 'company' in response) {
          return response.company;
        }

        return response as Company;
      } catch (error: any) {
        console.error('❌ Error creating company:', error);
        throw error;
      }
    },

    async updateCompany(id: string, data: CompanyFormData): Promise<Company> {
      try {
        const formattedData = {
          ...data,
          started_at: data.started_at ? new Date(data.started_at).toISOString() : ''
        };

        const response = await api.put<Company | { company: Company }>(`/companies/${id}`, formattedData);

        if (response && 'company' in response) {
          return response.company;
        }

        return response as Company;
      } catch (error: any) {
        console.error('❌ Error updating company:', error);
        throw error;
      }
    },

    async deleteCompany(id: string): Promise<void> {
      try {
        await api.del<void>(`/companies/${id}`);
      } catch (error: any) {
        console.error('❌ Error deleting company:', error);
        throw error;
      }
    },

    async getCompanyMetrics(id: string): Promise<any> {
      try {
        return await api.get<any>(`/companies/${id}/metrics`);
      } catch (error) {
        console.error(`❌ Failed to fetch metrics for company ${id}:`, error);
        return null;
      }
    },

    async testDatabaseConnection(
      connectionString: string, 
      databaseType: string
    ): Promise<{ success: boolean; message: string }> {
      try {
        return await api.post<{ success: boolean; message: string }>('/companies/test-connection', {
          connection_string: connectionString,
          database_type: databaseType,
        });
      } catch (error: any) {
        console.error('❌ Error testing database connection:', error);
        throw error;
      }
    },

    async uploadCertificate(
      companyId: string, 
      certificateFile: File
    ): Promise<{ success: boolean; message: string }> {
      try {
        const formData = new FormData();
        formData.append('certificate', certificateFile);

        return await fetchWithContext<{ success: boolean; message: string }>(`/companies/${companyId}/certificate`, {
          method: 'POST',
          body: formData,
          headers: {} // Empty headers object - browser will set Content-Type with boundary
        });
      } catch (error: any) {
        console.error('❌ Error uploading certificate:', error);
        throw error;
      }
    },

    async checkCertificate(companyId: string): Promise<{ 
      company_id: string; 
      use_ssl: boolean; 
      certificate_exists: boolean 
    }> {
      try {
        return await api.get<{ 
          company_id: string; 
          use_ssl: boolean; 
          certificate_exists: boolean 
        }>(`/companies/${companyId}/certificate`);
      } catch (error) {
        console.error(`❌ Failed to check certificate for company ${companyId}:`, error);
        return {
          company_id: companyId,
          use_ssl: false,
          certificate_exists: false
        };
      }
    },

    async deleteCertificate(companyId: string): Promise<{ 
      success: boolean; 
      message: string 
    }> {
      try {
        return await api.del<{ success: boolean; message: string }>(`/companies/${companyId}/certificate`);
      } catch (error: any) {
        console.error('❌ Error deleting certificate:', error);
        throw error;
      }
    }
  };
}

// Direct export functions
export async function getCompanies(): Promise<Company[]> {
  const api = getApiMethods();
  try {
    const response = await api.get<Company[] | { companies: Company[] } | null>('/companies');
    
    if (Array.isArray(response)) {
      return response;
    } else if (response && 'companies' in response) {
      return response.companies;
    }
    
    return [];
  } catch (error) {
    console.error('❌ Failed to fetch companies:', error);
    return [];
  }
}

export async function getCompanyById(id: string): Promise<Company | null> {
  const api = getApiMethods();
  try {
    const response = await api.get<Company | { company: Company } | null>(`/companies/${id}`);
    
    if (response && 'company' in response) {
      return response.company;
    }
    
    return response as Company;
  } catch (error) {
    console.error(`❌ Failed to fetch company ${id}:`, error);
    return null;
  }
}

export async function createCompany(data: CompanyFormData): Promise<Company> {
  const api = getApiMethods();
  try {
    const formattedData = {
      ...data,
      started_at: data.started_at ? new Date(data.started_at).toISOString() : ''
    };

    const response = await api.post<Company | { company: Company }>('/companies', formattedData);

    if (response && 'company' in response) {
      return response.company;
    }

    return response as Company;
  } catch (error: any) {
    console.error('❌ Error creating company:', error);
    throw error;
  }
}

export async function updateCompany(id: string, data: CompanyFormData): Promise<Company> {
  const api = getApiMethods();
  try {
    const formattedData = {
      ...data,
      started_at: data.started_at ? new Date(data.started_at).toISOString() : ''
    };

    const response = await api.put<Company | { company: Company }>(`/companies/${id}`, formattedData);

    if (response && 'company' in response) {
      return response.company;
    }

    return response as Company;
  } catch (error: any) {
    console.error('❌ Error updating company:', error);
    throw error;
  }
}

export async function deleteCompany(id: string): Promise<void> {
  const api = getApiMethods();
  try {
    await api.del<void>(`/companies/${id}`);
  } catch (error: any) {
    console.error('❌ Error deleting company:', error);
    throw error;
  }
}

export async function getCompanyMetrics(id: string): Promise<any> {
  const api = getApiMethods();
  try {
    return await api.get<any>(`/companies/${id}/metrics`);
  } catch (error) {
    console.error(`❌ Failed to fetch metrics for company ${id}:`, error);
    return null;
  }
}

export async function testDatabaseConnection(
  connectionString: string, 
  databaseType: string
): Promise<{ success: boolean; message: string }> {
  const api = getApiMethods();
  try {
    return await api.post<{ success: boolean; message: string }>('/companies/test-connection', {
      connection_string: connectionString,
      database_type: databaseType,
    });
  } catch (error: any) {
    console.error('❌ Error testing database connection:', error);
    throw error;
  }
}

export async function uploadCertificate(
  companyId: string, 
  certificateFile: File
): Promise<{ success: boolean; message: string }> {
  const fetchAPI = getFetchAPI();
  try {
    const formData = new FormData();
    formData.append('certificate', certificateFile);

    return await fetchAPI<{ success: boolean; message: string }>(`/companies/${companyId}/certificate`, {
      method: 'POST',
      body: formData,
      headers: {} // Empty headers object - browser will set Content-Type with boundary
    });
  } catch (error: any) {
    console.error('❌ Error uploading certificate:', error);
    throw error;
  }
}

export async function checkCertificate(companyId: string): Promise<{ 
  company_id: string; 
  use_ssl: boolean; 
  certificate_exists: boolean 
}> {
  const api = getApiMethods();
  try {
    return await api.get<{ 
      company_id: string; 
      use_ssl: boolean; 
      certificate_exists: boolean 
    }>(`/companies/${companyId}/certificate`);
  } catch (error) {
    console.error(`❌ Failed to check certificate for company ${companyId}:`, error);
    return {
      company_id: companyId,
      use_ssl: false,
      certificate_exists: false
    };
  }
}

export async function deleteCertificate(companyId: string): Promise<{ 
  success: boolean; 
  message: string 
}> {
  const api = getApiMethods();
  try {
    return await api.del<{ success: boolean; message: string }>(`/companies/${companyId}/certificate`);
  } catch (error: any) {
    console.error('❌ Error deleting certificate:', error);
    throw error;
  }
}