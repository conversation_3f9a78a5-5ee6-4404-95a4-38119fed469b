// api-url-context.ts
'use client';

import { createContext, useContext, ReactNode } from 'react';

const ApiUrlContext = createContext<string>(
  process.env.NEXT_PUBLIC_API_URL || '/api'
);

export function ApiUrlProvider({ children }: { children: ReactNode }) {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
  
  return (
    <ApiUrlContext.Provider value={apiUrl}>
      {children}
    </ApiUrlContext.Provider>
  );
}

export function useApiUrl() {
  return useContext(ApiUrlContext);
}
