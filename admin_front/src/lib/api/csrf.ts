// lib/api/csrf.ts - CSRF token management without React hooks

const isBrowser = typeof window !== 'undefined';

// CSRF token management - only reads from cookies (no localStorage)
export const csrfTokenManager = {
  // Get CSRF token from cookie (primary and only source)
  getFromCookie: (): string | null => {
    if (!isBrowser) return null;
    
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'csrf_token') {
        return decodeURIComponent(value);
      }
    }
    return null;
  },
  
  // Get fresh CSRF token from server
  refreshFromServer: async (): Promise<string | null> => {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';
    
    try {
      if (process.env.NODE_ENV !== 'production') {
        console.log('🔄 [CSRF] Requesting fresh CSRF token from server');
      }
      
      const response = await fetch(`${API_BASE_URL}/auth/csrf`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Origin': isBrowser ? window.location.origin : 'http://localhost:3000'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.csrf_token) {
          if (process.env.NODE_ENV !== 'production') {
            console.log('✅ [CSRF] Fresh CSRF token obtained from server');
          }
          return data.csrf_token;
        }
      } else {
        if (process.env.NODE_ENV !== 'production') {
          console.warn('⚠️ [CSRF] Failed to get CSRF token from server:', response.status);
        }
      }
    } catch (error) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('❌ [CSRF] Error getting CSRF token from server:', error);
      }
    }
    
    return null;
  },
  
  // Ensure we have a valid CSRF token
  ensureToken: async (): Promise<string | null> => {
    // First try to get from cookie
    let token = csrfTokenManager.getFromCookie();
    
    // If no token in cookie, get fresh one from server
    if (!token) {
      token = await csrfTokenManager.refreshFromServer();
    }
    
    return token;
  }
};

/**
 * Initialize CSRF token - call this on app startup
 */
export async function initializeCSRF(): Promise<void> {
  if (!isBrowser) return;
  
  try {
    // Check if we already have a token
    const existingToken = csrfTokenManager.getFromCookie();
    if (!existingToken) {
      // If no token, get a fresh one
      await csrfTokenManager.refreshFromServer();
    }
  } catch (error) {
    console.error('Failed to initialize CSRF token:', error);
  }
}
