// lib/api/templates.ts - Simplified fix with direct exports
import { useApiMethods, csrfTokenManager } from './fetchAPI';
import type { Template, TemplateGroup, TemplateFormData, TemplateGroupFormData } from '@/types/templates';

// Helper function to get API methods
function getApiMethods() {
  // In browser environment, we can safely call useApiMethods
  if (typeof window !== 'undefined') {
    return useApiMethods();
  }
  // For SSR, return null - these functions shouldn't be called server-side anyway
  throw new Error('API methods not available in server-side environment');
}

/**
 * Hook for templates API calls (use in React components)
 */
export function useTemplatesAPI() {
  const api = useApiMethods();

  return {
    // Template Groups
    async getTemplateGroups(companyId?: string): Promise<TemplateGroup[]> {
      try {
        const endpoint = companyId ? `/templates/groups?company_id=${companyId}` : '/templates/groups';
        const response = await api.get<TemplateGroup[] | { template_groups: TemplateGroup[] } | null>(endpoint);
        
        // Handle different response formats
        if (Array.isArray(response)) {
          return response;
        } else if (response && 'template_groups' in response) {
          return response.template_groups;
        }
        
        return [];
      } catch (error) {
        console.error('❌ Failed to fetch template groups:', error);
        return [];
      }
    },

    async getTemplateGroupById(id: string): Promise<TemplateGroup | null> {
      try {
        const response = await api.get<TemplateGroup | { template_group: TemplateGroup } | null>(`/templates/groups/${id}`);
        
        // Handle wrapped response
        if (response && 'template_group' in response) {
          return response.template_group;
        }
        
        return response as TemplateGroup;
      } catch (error) {
        console.error(`❌ Failed to fetch template group ${id}:`, error);
        return null;
      }
    },

    async createTemplateGroup(data: TemplateGroupFormData): Promise<TemplateGroup> {
      try {
        const response = await api.post<TemplateGroup | { template_group: TemplateGroup }>('/templates/groups', data);

        // Handle wrapped response
        if (response && 'template_group' in response) {
          return response.template_group;
        }

        return response as TemplateGroup;
      } catch (error: any) {
        console.error('❌ Error creating template group:', error);
        throw error;
      }
    },

    async updateTemplateGroup(id: string, data: TemplateGroupFormData): Promise<TemplateGroup> {
      try {
        const response = await api.put<TemplateGroup | { template_group: TemplateGroup }>(`/templates/groups/${id}`, data);

        // Handle wrapped response
        if (response && 'template_group' in response) {
          return response.template_group;
        }

        return response as TemplateGroup;
      } catch (error: any) {
        console.error('❌ Error updating template group:', error);
        throw error;
      }
    },

    async deleteTemplateGroup(id: string): Promise<void> {
      try {
        await api.del<void>(`/templates/groups/${id}`);
      } catch (error: any) {
        console.error('❌ Error deleting template group:', error);
        throw error;
      }
    },

    // Templates
    async getTemplates(groupId: string): Promise<Template[]> {
      try {
        const response = await api.get<Template[] | { templates: Template[] } | null>(`/templates/group/${groupId}`);
        
        let templates: Template[] = [];
        
        // Handle different response formats
        if (Array.isArray(response)) {
          templates = response;
        } else if (response && 'templates' in response) {
          templates = response.templates;
        }
        
        // Map responses if needed
        if (templates.length > 0) {
          return templates.map(template => ({
            ...template,
            content: template.body || template.content // Map body to content if needed
          }));
        }
        
        return [];
      } catch (error) {
        console.error(`❌ Failed to fetch templates for group ${groupId}:`, error);
        return [];
      }
    },

    async getTemplateById(id: string): Promise<Template | null> {
      try {
        const response = await api.get<Template | { template: Template } | null>(`/templates/${id}`);
        
        let template: Template | null = null;
        
        // Handle wrapped response
        if (response && 'template' in response) {
          template = response.template;
        } else {
          template = response as Template;
        }
        
        // Map body to content if needed
        if (template) {
          return {
            ...template,
            content: template.body || template.content // Use body or content, whichever exists
          };
        }
        
        return null;
      } catch (error) {
        console.error(`❌ Failed to fetch template ${id}:`, error);
        return null;
      }
    },

    async createTemplate(data: TemplateFormData): Promise<Template> {
      try {
        // Ensure week_number is a number, not a string
        const weekNumber = typeof data.week_number === 'string' 
          ? parseInt(data.week_number, 10) 
          : data.week_number;
        
        // Map frontend field names to what the backend/database expects
        const requestData = {
          name: data.name,
          subject: data.subject,
          body: data.content, // Map content to body
          group_id: data.group_id,
          week_number: weekNumber || 0,
          type: data.type || 'default',
          default_data: data.default_data || {},
          required_fields: data.required_fields || [],
          is_active: data.is_active !== undefined ? data.is_active : true
        };
        
        const response = await api.post<Template | { template: Template }>('/templates', requestData);

        // Handle wrapped response
        if (response && 'template' in response) {
          return response.template;
        }

        return response as Template;
      } catch (error: any) {
        console.error('❌ Error creating template:', error);
        throw error;
      }
    },

    async getAllTemplates(): Promise<Template[]> {
      try {
        // First, get all template groups
        const groups = await this.getTemplateGroups();
        
        // Return empty array if no groups
        if (groups.length === 0) {
          return [];
        }
        
        // Then fetch templates for each group
        const templatesPromises = groups.map(group => this.getTemplates(group.id));
        
        // Wait for all requests to complete
        const templatesArrays = await Promise.all(templatesPromises);
        
        // Flatten the arrays of templates
        return templatesArrays.flat();
      } catch (error) {
        console.error('❌ Failed to fetch all templates:', error);
        return [];
      }
    },

    async updateTemplate(id: string, data: Partial<Template>): Promise<Template> {
      try {
        // Ensure week_number is a number
        const processedData = {
          ...data,
          week_number: Number(data.week_number)
        };
        
        const response = await api.put<Template | { template: Template }>(`/templates/${id}`, processedData);

        // Handle wrapped response
        if (response && 'template' in response) {
          return response.template;
        }

        return response as Template;
      } catch (error: any) {
        console.error('❌ Error updating template:', error);
        throw error;
      }
    },

    async deleteTemplate(id: string): Promise<void> {
      try {
        await api.del<void>(`/templates/${id}`);
      } catch (error: any) {
        console.error('❌ Error deleting template:', error);
        throw error;
      }
    },

    async renderTemplate(id: string, templateData?: Record<string, string>): Promise<string> {
      try {
        const response = await api.post<{body: string} | {content: string} | string>(`/templates/${id}/render`, templateData || {});
        
        // Handle different response types
        if (typeof response === 'string') {
          return response;
        } else if (response && 'body' in response) {
          return response.body;
        } else if (response && 'content' in response) {
          return response.content;
        }
        
        return '';
      } catch (error: any) {
        console.error('❌ Error rendering template:', error);
        throw error;
      }
    }
  };
}

// Direct export functions that use the API methods
export async function getTemplateGroups(companyId?: string): Promise<TemplateGroup[]> {
  const api = getApiMethods();
  try {
    const endpoint = companyId ? `/templates/groups?company_id=${companyId}` : '/templates/groups';
    const response = await api.get<TemplateGroup[] | { template_groups: TemplateGroup[] } | null>(endpoint);
    
    // Handle different response formats
    if (Array.isArray(response)) {
      return response;
    } else if (response && 'template_groups' in response) {
      return response.template_groups;
    }
    
    return [];
  } catch (error) {
    console.error('❌ Failed to fetch template groups:', error);
    return [];
  }
}

export async function getTemplateGroupById(id: string): Promise<TemplateGroup | null> {
  const api = getApiMethods();
  try {
    const response = await api.get<TemplateGroup | { template_group: TemplateGroup } | null>(`/templates/groups/${id}`);
    
    // Handle wrapped response
    if (response && 'template_group' in response) {
      return response.template_group;
    }
    
    return response as TemplateGroup;
  } catch (error) {
    console.error(`❌ Failed to fetch template group ${id}:`, error);
    return null;
  }
}

export async function createTemplateGroup(data: TemplateGroupFormData): Promise<TemplateGroup> {
  const api = getApiMethods();
  try {
    const response = await api.post<TemplateGroup | { template_group: TemplateGroup }>('/templates/groups', data);

    // Handle wrapped response
    if (response && 'template_group' in response) {
      return response.template_group;
    }

    return response as TemplateGroup;
  } catch (error: any) {
    console.error('❌ Error creating template group:', error);
    throw error;
  }
}

export async function updateTemplateGroup(id: string, data: TemplateGroupFormData): Promise<TemplateGroup> {
  const api = getApiMethods();
  try {
    const response = await api.put<TemplateGroup | { template_group: TemplateGroup }>(`/templates/groups/${id}`, data);

    // Handle wrapped response
    if (response && 'template_group' in response) {
      return response.template_group;
    }

    return response as TemplateGroup;
  } catch (error: any) {
    console.error('❌ Error updating template group:', error);
    throw error;
  }
}

export async function deleteTemplateGroup(id: string): Promise<void> {
  const api = getApiMethods();
  try {
    await api.del<void>(`/templates/groups/${id}`);
  } catch (error: any) {
    console.error('❌ Error deleting template group:', error);
    throw error;
  }
}

export async function getTemplates(groupId: string): Promise<Template[]> {
  const api = getApiMethods();
  try {
    const response = await api.get<Template[] | { templates: Template[] } | null>(`/templates/group/${groupId}`);
    
    let templates: Template[] = [];
    
    // Handle different response formats
    if (Array.isArray(response)) {
      templates = response;
    } else if (response && 'templates' in response) {
      templates = response.templates;
    }
    
    // Map responses if needed
    if (templates.length > 0) {
      return templates.map(template => ({
        ...template,
        content: template.body || template.content // Map body to content if needed
      }));
    }
    
    return [];
  } catch (error) {
    console.error(`❌ Failed to fetch templates for group ${groupId}:`, error);
    return [];
  }
}

export async function getTemplateById(id: string): Promise<Template | null> {
  const api = getApiMethods();
  try {
    const response = await api.get<Template | { template: Template } | null>(`/templates/${id}`);
    
    let template: Template | null = null;
    
    // Handle wrapped response
    if (response && 'template' in response) {
      template = response.template;
    } else {
      template = response as Template;
    }
    
    // Map body to content if needed
    if (template) {
      return {
        ...template,
        content: template.body || template.content // Use body or content, whichever exists
      };
    }
    
    return null;
  } catch (error) {
    console.error(`❌ Failed to fetch template ${id}:`, error);
    return null;
  }
}

export async function createTemplate(data: TemplateFormData): Promise<Template> {
  const api = getApiMethods();
  try {
    // Ensure week_number is a number, not a string
    const weekNumber = typeof data.week_number === 'string' 
      ? parseInt(data.week_number, 10) 
      : data.week_number;
    
    // Map frontend field names to what the backend/database expects
    const requestData = {
      name: data.name,
      subject: data.subject,
      body: data.content, // Map content to body
      group_id: data.group_id,
      week_number: weekNumber || 0,
      type: data.type || 'default',
      default_data: data.default_data || {},
      required_fields: data.required_fields || [],
      is_active: data.is_active !== undefined ? data.is_active : true
    };
    
    const response = await api.post<Template | { template: Template }>('/templates', requestData);

    // Handle wrapped response
    if (response && 'template' in response) {
      return response.template;
    }

    return response as Template;
  } catch (error: any) {
    console.error('❌ Error creating template:', error);
    throw error;
  }
}

export async function getAllTemplates(): Promise<Template[]> {
  const api = getApiMethods();
  try {
    // First, get all template groups
    const groups = await getTemplateGroups();
    
    // Return empty array if no groups
    if (groups.length === 0) {
      return [];
    }
    
    // Then fetch templates for each group
    const templatesPromises = groups.map(group => getTemplates(group.id));
    
    // Wait for all requests to complete
    const templatesArrays = await Promise.all(templatesPromises);
    
    // Flatten the arrays of templates
    return templatesArrays.flat();
  } catch (error) {
    console.error('❌ Failed to fetch all templates:', error);
    return [];
  }
}

export async function updateTemplate(id: string, data: Partial<Template>): Promise<Template> {
  const api = getApiMethods();
  try {
    // Ensure week_number is a number
    const processedData = {
      ...data,
      week_number: Number(data.week_number)
    };
    
    const response = await api.put<Template | { template: Template }>(`/templates/${id}`, processedData);

    // Handle wrapped response
    if (response && 'template' in response) {
      return response.template;
    }

    return response as Template;
  } catch (error: any) {
    console.error('❌ Error updating template:', error);
    throw error;
  }
}

export async function deleteTemplate(id: string): Promise<void> {
  const api = getApiMethods();
  try {
    await api.del<void>(`/templates/${id}`);
  } catch (error: any) {
    console.error('❌ Error deleting template:', error);
    throw error;
  }
}

export async function renderTemplate(id: string, templateData?: Record<string, string>): Promise<string> {
  const api = getApiMethods();
  try {
    const response = await api.post<{body: string} | {content: string} | string>(`/templates/${id}/render`, templateData || {});
    
    // Handle different response types
    if (typeof response === 'string') {
      return response;
    } else if (response && 'body' in response) {
      return response.body;
    } else if (response && 'content' in response) {
      return response.content;
    }
    
    return '';
  } catch (error: any) {
    console.error('❌ Error rendering template:', error);
    throw error;
  }
}