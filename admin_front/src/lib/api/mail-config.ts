// lib/api/mail-config.ts - Simplified fix with direct exports
import { useApiMethods } from './fetchAPI';
import type {
  MailConfig,
  MailConfigResponse,
  MailConfigsListResponse,
  CreateMailConfigRequest,
  UpdateMailConfigRequest
} from '@/types/mail-config';

// Helper function to get API methods
function getApiMethods() {
  if (typeof window !== 'undefined') {
    return useApiMethods();
  }
  throw new Error('API methods not available in server-side environment');
}

/**
 * Hook for mail config API calls (use in React components)
 */
export function useMailConfigAPI() {
  const api = useApiMethods();

  return {
    async getMailConfigs(page?: number, limit?: number): Promise<MailConfigsListResponse> {
      try {
        let endpoint = '/mail/configs';
        if (page && limit) endpoint += `?page=${page}&limit=${limit}`;
        
        const response = await api.get<MailConfig[] | MailConfigsListResponse>(endpoint);
        
        // Check if response is an array (direct API response)
        if (Array.isArray(response)) {
          return {
            mail_configs: response,
            total: response.length
          };
        }
        
        // Otherwise return the response as is
        return response;
      } catch (error) {
        console.error('❌ Error fetching mail configs:', error);
        throw error;
      }
    },

    async getMailConfig(id: string): Promise<MailConfigResponse> {
      try {
        // Handle both direct object response and wrapped response
        const response = await api.get<MailConfig | MailConfigResponse>(`/mail/configs/${id}`);
        
        // Check if response is direct mail config object
        if (!('mail_config' in response)) {
          return {
            mail_config: response as MailConfig
          };
        }
        
        return response as MailConfigResponse;
      } catch (error) {
        console.error('❌ Error in getMailConfig:', error);
        return {
          mail_config: {
            id,
            company_id: '',
            sender_email: '',
            sender_name: '',
            smtp_host: '',
            smtp_port: 587,
            smtp_username: '',
            use_ssl: true,
            active: false,
            created_at: '',
            updated_at: ''
          },
          message: error instanceof Error ? error.message : 'Failed to load mail configuration'
        };
      }
    },

    async createMailConfig(data: CreateMailConfigRequest): Promise<MailConfigResponse> {
      try {
        const response = await api.post<MailConfig | MailConfigResponse>('/mail/configs', data);
        
        // Check if response is direct mail config object
        if (!('mail_config' in response)) {
          return {
            mail_config: response as MailConfig
          };
        }
        
        return response as MailConfigResponse;
      } catch (error: any) {
        console.error('❌ Error creating mail config:', error);
        throw error;
      }
    },

    async updateMailConfig(id: string, data: UpdateMailConfigRequest): Promise<MailConfigResponse> {
      try {
        const response = await api.put<MailConfig | MailConfigResponse>(`/mail/configs/${id}`, data);
        
        // Check if response is direct mail config object
        if (!('mail_config' in response)) {
          return {
            mail_config: response as MailConfig
          };
        }
        
        return response as MailConfigResponse;
      } catch (error: any) {
        console.error('❌ Error updating mail config:', error);
        throw error;
      }
    },

    async deleteMailConfig(id: string): Promise<{ message: string }> {
      try {
        const response = await api.del<{ message: string } | null>(`/mail/configs/${id}`);
        
        return response || { message: 'Mail configuration deleted successfully' };
      } catch (error: any) {
        console.error('❌ Error deleting mail config:', error);
        throw error;
      }
    },

    async testMailConfig(
      config: MailConfig, 
      testEmail: string
    ): Promise<{success: boolean, message?: string, error?: string}> {
      try {
        // Validate that the test email is provided and valid
        if (!testEmail || !testEmail.includes('@')) {
          return {
            success: false,
            error: 'A valid test email address is required'
          };
        }
        
        const response = await api.post<{success: boolean, message?: string, error?: string}>('/mail/configs/test', { 
          config,
          testEmail
        });
        
        return response;
      } catch (error: any) {
        console.error('❌ Error testing mail config:', error);
        return {
          success: false,
          error: error.message || 'Failed to test mail configuration',
        };
      }
    }
  };
}

// Direct export functions
export async function getMailConfigs(page?: number, limit?: number): Promise<MailConfigsListResponse> {
  const api = getApiMethods();
  try {
    let endpoint = '/mail/configs';
    if (page && limit) endpoint += `?page=${page}&limit=${limit}`;
    
    const response = await api.get<MailConfig[] | MailConfigsListResponse>(endpoint);
    
    // Check if response is an array (direct API response)
    if (Array.isArray(response)) {
      return {
        mail_configs: response,
        total: response.length
      };
    }
    
    // Otherwise return the response as is
    return response;
  } catch (error) {
    console.error('❌ Error fetching mail configs:', error);
    throw error;
  }
}

export async function getMailConfig(id: string): Promise<MailConfigResponse> {
  const api = getApiMethods();
  try {
    // Handle both direct object response and wrapped response
    const response = await api.get<MailConfig | MailConfigResponse>(`/mail/configs/${id}`);
    
    // Check if response is direct mail config object
    if (!('mail_config' in response)) {
      return {
        mail_config: response as MailConfig
      };
    }
    
    return response as MailConfigResponse;
  } catch (error) {
    console.error('❌ Error in getMailConfig:', error);
    return {
      mail_config: {
        id,
        company_id: '',
        sender_email: '',
        sender_name: '',
        smtp_host: '',
        smtp_port: 587,
        smtp_username: '',
        use_ssl: true,
        active: false,
        created_at: '',
        updated_at: ''
      },
      message: error instanceof Error ? error.message : 'Failed to load mail configuration'
    };
  }
}

export async function getMailConfigsByCompany(companyId: string): Promise<MailConfigsListResponse> {
  const api = getApiMethods();
  try {
    const response = await api.get<MailConfig[] | MailConfigsListResponse>(`/mail/configs/company/${companyId}`);
    
    // Check if response is an array (direct API response)
    if (Array.isArray(response)) {
      return {
        mail_configs: response,
        total: response.length
      };
    }
    
    return response;
  } catch (error) {
    console.error('❌ Error in getMailConfigsByCompany:', error);
    return {
      mail_configs: [],
      total: 0,
      message: error instanceof Error ? error.message : 'Failed to load mail configurations'
    };
  }
}

export async function createMailConfig(data: CreateMailConfigRequest): Promise<MailConfigResponse> {
  const api = getApiMethods();
  try {
    const response = await api.post<MailConfig | MailConfigResponse>('/mail/configs', data);
    
    // Check if response is direct mail config object
    if (!('mail_config' in response)) {
      return {
        mail_config: response as MailConfig
      };
    }
    
    return response as MailConfigResponse;
  } catch (error: any) {
    console.error('❌ Error creating mail config:', error);
    throw error;
  }
}

export async function updateMailConfig(id: string, data: UpdateMailConfigRequest): Promise<MailConfigResponse> {
  const api = getApiMethods();
  try {
    const response = await api.put<MailConfig | MailConfigResponse>(`/mail/configs/${id}`, data);
    
    // Check if response is direct mail config object
    if (!('mail_config' in response)) {
      return {
        mail_config: response as MailConfig
      };
    }
    
    return response as MailConfigResponse;
  } catch (error: any) {
    console.error('❌ Error updating mail config:', error);
    throw error;
  }
}

export async function deleteMailConfig(id: string): Promise<{ message: string }> {
  const api = getApiMethods();
  try {
    const response = await api.del<{ message: string } | null>(`/mail/configs/${id}`);
    
    return response || { message: 'Mail configuration deleted successfully' };
  } catch (error: any) {
    console.error('❌ Error deleting mail config:', error);
    throw error;
  }
}

export async function toggleMailConfigStatus(id: string, active: boolean): Promise<MailConfigResponse> {
  return updateMailConfig(id, { active });
}

export async function testMailConfig(
  config: MailConfig, 
  testEmail: string
): Promise<{success: boolean, message?: string, error?: string}> {
  const api = getApiMethods();
  try {
    // Validate that the test email is provided and valid
    if (!testEmail || !testEmail.includes('@')) {
      return {
        success: false,
        error: 'A valid test email address is required'
      };
    }
    
    const response = await api.post<{success: boolean, message?: string, error?: string}>('/mail/configs/test', { 
      config,
      testEmail
    });
    
    return response;
  } catch (error: any) {
    console.error('❌ Error testing mail config:', error);
    return {
      success: false,
      error: error.message || 'Failed to test mail configuration',
    };
  }
}