// lib/api/scraper.ts - Simplified fix with direct exports
import { useApiMethods } from './fetchAPI';

interface UserData {
  username: string;
  email: string;
  company_name: string;
}

interface EmailUser {
  email: string;
  template_id: string;
  data: any;
  company_id: string;
}

// Helper function to get API methods
function getApiMethods() {
  if (typeof window !== 'undefined') {
    return useApiMethods();
  }
  throw new Error('API methods not available in server-side environment');
}

/**
 * Hook for scraper API calls (use in React components)
 */
export function useScraperAPI() {
  const api = useApiMethods();

  return {
    async selectTemplatesForUsers(usersData: UserData[]): Promise<any> {
      try {
        const requestBody = {
          users: usersData.map(item => ({
            username: item.username,
            email: item.email,
            company_name: item.company_name,
          })),
        };

        console.log('🔍 Selecting templates for users:', requestBody);
        
        return await api.post<any>('/scraper/select-templates', requestBody);
      } catch (error: any) {
        console.error('❌ Error selecting templates:', error);
        throw error;
      }
    },

    async sendTemplateEmails(users: EmailUser[], sendToAll: boolean = false): Promise<any> {
      try {
        const requestBody = {
          users: users.map(user => ({
            email: user.email,
            template_id: user.template_id,
            data: user.data,
            company_id: user.company_id,
          })),
          send_to_all: sendToAll,
        };

        console.log('📧 Sending template emails:', requestBody);
        
        return await api.post<any>('/scraper/send-template-emails', requestBody);
      } catch (error: any) {
        console.error('❌ Error sending template emails:', error);
        
        if (error instanceof Error) {
          if (error.message.includes('timeout')) {
            throw new Error('The email sending process is taking longer than expected. Please check the results in a few minutes.');
          } else if (error.message.includes('csrf_validation_failed')) {
            throw new Error('Security token expired. Please refresh the page and try again.');
          } else if (error.message.includes('504') || error.message.includes('Gateway timeout')) {
            throw new Error('The server is processing your request. This may take a few minutes for large batches.');
          }
        }
        
        throw error;
      }
    },

    async getScraperStatus(): Promise<any> {
      try {
        return await api.get<any>('/scraper/status');
      } catch (error) {
        console.error('❌ Error getting scraper status:', error);
        throw error;
      }
    },

    async cancelScraperOperation(operationId: string): Promise<any> {
      try {
        return await api.post<any>(`/scraper/cancel/${operationId}`, {});
      } catch (error: any) {
        console.error('❌ Error canceling scraper operation:', error);
        throw error;
      }
    }
  };
}

// Direct export functions
export async function selectTemplatesForUsers(usersData: UserData[]): Promise<any> {
  const api = getApiMethods();
  try {
    const requestBody = {
      users: usersData.map(item => ({
        username: item.username,
        email: item.email,
        company_name: item.company_name,
      })),
    };

    console.log('🔍 Selecting templates for users:', requestBody);
    
    return await api.post<any>('/scraper/select-templates', requestBody);
  } catch (error: any) {
    console.error('❌ Error selecting templates:', error);
    throw error;
  }
}

export async function sendTemplateEmails(users: EmailUser[], sendToAll: boolean = false): Promise<any> {
  const api = getApiMethods();
  try {
    const requestBody = {
      users: users.map(user => ({
        email: user.email,
        template_id: user.template_id,
        data: user.data,
        company_id: user.company_id,
      })),
      send_to_all: sendToAll,
    };

    console.log('📧 Sending template emails:', requestBody);
    
    return await api.post<any>('/scraper/send-template-emails', requestBody);
  } catch (error: any) {
    console.error('❌ Error sending template emails:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        throw new Error('The email sending process is taking longer than expected. Please check the results in a few minutes.');
      } else if (error.message.includes('csrf_validation_failed')) {
        throw new Error('Security token expired. Please refresh the page and try again.');
      } else if (error.message.includes('504') || error.message.includes('Gateway timeout')) {
        throw new Error('The server is processing your request. This may take a few minutes for large batches.');
      }
    }
    
    throw error;
  }
}

export async function getScraperStatus(): Promise<any> {
  const api = getApiMethods();
  try {
    return await api.get<any>('/scraper/status');
  } catch (error) {
    console.error('❌ Error getting scraper status:', error);
    throw error;
  }
}

export async function cancelScraperOperation(operationId: string): Promise<any> {
  const api = getApiMethods();
  try {
    return await api.post<any>(`/scraper/cancel/${operationId}`, {});
  } catch (error: any) {
    console.error('❌ Error canceling scraper operation:', error);
    throw error;
  }
}

// Export types for external use
export type { UserData, EmailUser };