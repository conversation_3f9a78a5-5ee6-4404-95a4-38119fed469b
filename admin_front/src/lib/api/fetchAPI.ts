// lib/api/fetchAPI.ts - Fixed and cleaned up version

'use client';

import { useApiUrl } from './api-url-context';

const isBrowser = typeof window !== 'undefined';

// CSRF token management - only reads from cookies (no localStorage)
export const csrfTokenManager = {
  // Get CSRF token from cookie (primary and only source)
  getFromCookie: (): string | null => {
    if (!isBrowser) return null;
    
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'csrf_token') {
        return decodeURIComponent(value);
      }
    }
    return null;
  },
  
  // Get fresh CSRF token from server
  refreshFromServer: async (): Promise<string | null> => {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';
    
    try {
      if (process.env.NODE_ENV !== 'production') {
        console.log('🔄 [CSRF] Requesting fresh CSRF token from server');
      }
      
      const response = await fetch(`${API_BASE_URL}/auth/csrf`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Origin': isBrowser ? window.location.origin : 'http://localhost:3000'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.csrf_token) {
          if (process.env.NODE_ENV !== 'production') {
            console.log('✅ [CSRF] Fresh CSRF token obtained from server');
          }
          return data.csrf_token;
        }
      } else {
        if (process.env.NODE_ENV !== 'production') {
          console.warn('⚠️ [CSRF] Failed to get CSRF token from server:', response.status);
        }
      }
    } catch (error) {
      if (process.env.NODE_ENV !== 'production') {
        console.error('❌ [CSRF] Error getting CSRF token from server:', error);
      }
    }
    
    return null;
  },
  
  // Ensure we have a valid CSRF token
  ensureToken: async (): Promise<string | null> => {
    // First try to get from cookie
    let token = csrfTokenManager.getFromCookie();
    
    // If no token in cookie, get fresh one from server
    if (!token) {
      token = await csrfTokenManager.refreshFromServer();
    }
    
    return token;
  }
};

// Check if endpoint requires CSRF protection
function requiresCSRF(endpoint: string, method: string = 'GET'): boolean {
  // Methods that don't require CSRF
  if (['GET', 'HEAD', 'OPTIONS'].includes(method.toUpperCase())) {
    return false;
  }
  
  // Auth endpoints that don't require CSRF
  const csrfExemptEndpoints = [
    '/auth/login',
    '/auth/register',
    '/auth/refresh',
    '/auth/logout',
    '/auth/csrf',
    '/auth/debug',
    '/health',
    '/ping'
  ];
  
  return !csrfExemptEndpoints.some(exempt => endpoint.includes(exempt));
}

/**
 * Enhanced fetch API for making API requests with authentication and CSRF support
 * This version is for client components that can use the context
 */
export function useFetchAPI() {
  const apiUrl = useApiUrl();
  
  return async function fetchWithContext<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = new URL(
      endpoint.startsWith('/') ? endpoint.slice(1) : endpoint,
      apiUrl.endsWith('/') ? apiUrl : `${apiUrl}/`
    ).toString();

    if (process.env.NODE_ENV !== 'production') {
      console.log(`📡 [API] ${options.method || 'GET'} ${url}`);
    }
    
    const isRetry = (options.headers as any)?.__isRetryAfterRefresh === true;
    const isCSRFRetry = (options.headers as any)?.__isRetryAfterCSRF === true;
    const method = options.method || 'GET';
  
    try {
      // Setup headers
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...(options.headers || {})
      };

      // Add CSRF token if required
      if (requiresCSRF(endpoint, method)) {
        // Ensure we have a valid token before making the request
        if (!isCSRFRetry) {
          await csrfTokenManager.ensureToken();
        }
        
        let token = csrfTokenManager.getFromCookie();
        
        if (token) {
          (headers as any)['X-CSRF-Token'] = token;
          if (process.env.NODE_ENV !== 'production') {
            console.log(`🔒 [API] Adding CSRF token to ${method} ${endpoint}`);
          }
        } else {
          if (process.env.NODE_ENV !== 'production') {
            console.warn(`⚠️ [API] No CSRF token available for ${method} ${endpoint}`);
          }
        }
      }
    
      // Create request options
      const requestOptions: RequestInit = {
        ...options,
        headers,
        credentials: 'include',
        mode: 'cors',
      };
    
      // Make the request
      const response = await fetch(url, requestOptions);
    
      if (process.env.NODE_ENV !== 'production') {
        console.log(`📥 [API] ${response.status} ${response.statusText} from ${endpoint}`);
      }
    
      // Handle error responses
      if (!response.ok) {
        let errorData: any = {};
        let errorText = '';
        
        const contentType = response.headers.get('content-type');
        if (contentType?.includes('application/json')) {
          try {
            errorData = await response.json();
          } catch {
            errorText = await response.text();
          }
        } else {
          errorText = await response.text();
        }
        
        // Handle CSRF validation failures
        if (response.status === 403) {
          const isCSRFError = errorData.error === 'csrf_validation_failed' || 
                             errorData.error === 'csrf_token_missing' ||
                             errorText.includes('csrf');
                             
          if (isCSRFError && !isCSRFRetry) {
            if (process.env.NODE_ENV !== 'production') {
              console.log('🔄 [API] CSRF validation failed, attempting to get fresh token');
            }
            
            // Try to get a fresh CSRF token from server
            const freshToken = await csrfTokenManager.refreshFromServer();
            if (!freshToken) {
              // If we can't get a fresh token, try refreshing the session
              const refreshed = await refreshToken();
              if (!refreshed) {
                throw new Error('CSRF token validation failed. Please refresh the page.');
              }
            }
            
            // Retry the original request with fresh CSRF token
            return fetchWithContext<T>(endpoint, {
              ...options,
              headers: {
                ...(options.headers || {}),
                __isRetryAfterCSRF: String(true),
              }
            });
          }
          
          // If still CSRF error after retry
          if (isCSRFError) {
            throw new Error('CSRF token validation failed. Please refresh the page and try again.');
          }
        }
        
        // Handle token expiration
        const isTokenExpired = response.status === 401 && !isRetry &&
          (errorData.error === 'token_expired' ||
           errorData.error === 'authentication_required' ||
           errorData.message?.includes('expired') ||
           errorText.includes('expired')) &&
          !endpoint.includes('/auth/refresh');

        if (isTokenExpired) {
          if (process.env.NODE_ENV !== 'production') {
            console.log('🔄 [API] Token expired, attempting refresh...');
          }
          
          const refreshed = await refreshToken();
          if (refreshed) {
            if (process.env.NODE_ENV !== 'production') {
              console.log('✅ [API] Token refreshed, retrying request');
            }
            
            return fetchWithContext<T>(endpoint, {
              ...options,
              headers: {
                ...(options.headers || {}),
                __isRetryAfterRefresh: String(true),
              }
            });
          } else {
            if (isBrowser) {
              console.error('❌ [API] Token refresh failed, redirecting to login');
              window.location.href = '/login';
            }
            throw new Error('Session expired. Please log in again.');
          }
        }
        
        // Handle unauthorized requests (after refresh attempts)
        if (response.status === 401 && isBrowser && !endpoint.includes('/auth/')) {
          console.error('❌ [API] Unauthorized, redirecting to login');
          window.location.href = '/login';
          throw new Error('Unauthorized. Redirecting to login.');
        }
        
        // Handle other errors
        const errorMessage = errorData.message || errorData.error || errorText || `HTTP ${response.status}: ${response.statusText}`;
        console.error(`❌ [API] Error for ${endpoint}:`, errorMessage);
        throw new Error(errorMessage);
      }
    
      // Handle empty responses
      if (response.status === 204) {
        return {} as T;
      }
    
      // Parse successful response
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        return await response.json();
      } else {
        return await response.text() as unknown as T;
      }
    } catch (error: any) {
      if (process.env.NODE_ENV !== 'production') {
        console.error(`❌ [API] Error in fetchAPI for ${endpoint}:`, error);
      }
    
      // For network/CORS errors, provide user-friendly message
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.warn(`⚠️ [API] Network error for ${endpoint}`);
        throw new Error('Network error. Please check your connection and try again.');
      }
    
      throw error;
    }
  };
}

/**
 * Standard fetch API for non-client components
 */
export async function fetchAPI<T>(
  endpoint: string,  
  options: RequestInit & { timeout?: number } = {}
): Promise<T> {
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';
  const url = `${API_BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
  
  const { timeout, ...fetchOptions } = options;
  const method = fetchOptions.method || 'GET';
  
  try {
    // Setup headers
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...fetchOptions.headers,
    };

    // Add CSRF token if required
    if (requiresCSRF(endpoint, method) && isBrowser) {
      // Try to get token from cookie
      const token = csrfTokenManager.getFromCookie();
      if (token) {
        (headers as any)['X-CSRF-Token'] = token;
      }
    }
    
    // Create an AbortController for timeout handling
    const controller = new AbortController();
    const timeoutId = timeout ? setTimeout(() => controller.abort(), timeout) : null;
    
    const response = await fetch(url, {
      ...fetchOptions,
      headers,
      credentials: 'include',
      signal: timeout ? controller.signal : undefined,
    });
    
    // Clear timeout if it was set
    if (timeoutId) clearTimeout(timeoutId);
    
    // Handle response status
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error ${response.status}: ${errorText}`);
    }
    
    // Parse successful response
    const contentType = response.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      return await response.json();
    } else {
      return await response.text() as unknown as T;
    }
  } catch (error: any) {
    // Handle timeout errors specifically
    if (error.name === 'AbortError') {
      throw new Error('Request timed out. Please try again later.');
    }
    
    throw error;
  }
}

/**
 * Refreshes the authentication token
 */
async function refreshToken(): Promise<boolean> {
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      mode: 'cors',
    });
    
    if (!response.ok) {
      return false;
    }
    
    // Token refreshed successfully (CSRF token automatically updated in cookie)
    if (process.env.NODE_ENV !== 'production') {
      console.log('✅ [API] Tokens refreshed successfully');
    }
    return true;
  } catch (error) {
    if (process.env.NODE_ENV !== 'production') {
      console.error('❌ [API] Error refreshing token:', error);
    }
    return false;
  }
}

/**
 * Simple helper functions that use the context-aware fetch
 */
export function useApiMethods() {
  const fetchWithContext = useFetchAPI();
  
  return {
    get: async <T>(endpoint: string): Promise<T> => {
      return fetchWithContext<T>(endpoint, { method: 'GET' });
    },
    
    post: async <T>(endpoint: string, data: any): Promise<T> => {
      return fetchWithContext<T>(endpoint, {
        method: 'POST',
        body: JSON.stringify(data)
      });
    },
    
    put: async <T>(endpoint: string, data: any): Promise<T> => {
      return fetchWithContext<T>(endpoint, {
        method: 'PUT',
        body: JSON.stringify(data)
      });
    },
    
    del: async <T>(endpoint: string): Promise<T> => {
      return fetchWithContext<T>(endpoint, { method: 'DELETE' });
    }
  };
}

/**
 * Legacy helper functions for backward compatibility
 */
export async function get<T>(endpoint: string): Promise<T> {
  return fetchAPI<T>(endpoint, { method: 'GET' });
}

export async function post<T>(endpoint: string, data: any): Promise<T> {
  return fetchAPI<T>(endpoint, {
    method: 'POST',
    body: JSON.stringify(data)
  });
}

export async function put<T>(endpoint: string, data: any): Promise<T> {
  return fetchAPI<T>(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data)
  });
}

export async function del<T>(endpoint: string): Promise<T> {
  return fetchAPI<T>(endpoint, { method: 'DELETE' });
}

/**
 * Auth helper functions
 */
export function initializeAuth(loginResponse: any) {
  // CSRF token is automatically handled via cookies, no manual management needed
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔒 [AUTH] Authentication initialized');
  }
}

export function clearAuth() {
  // Tokens are in HTTP-only cookies, will be cleared by logout endpoint
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔓 [AUTH] Authentication cleared');
  }
}

/**
 * Initialize CSRF token - call this on app startup
 */
export async function initializeCSRF(): Promise<void> {
  if (!isBrowser) return;
  
  try {
    // Check if we already have a token
    const existingToken = csrfTokenManager.getFromCookie();
    if (!existingToken) {
      // If no token, get a fresh one
      await csrfTokenManager.refreshFromServer();
    }
  } catch (error) {
    console.error('Failed to initialize CSRF token:', error);
  }
}

// Default export
export default {
  useFetchAPI,
  useApiMethods,
  fetchAPI,
  get,
  post,
  put,
  delete: del,
  csrf: csrfTokenManager,
  initializeAuth,
  clearAuth,
  initializeCSRF
};
