'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  createMailConfig, 
  updateMailConfig, 
  testMailConfig 
} from '@/lib/api/mail-config';
import { getCompanies } from '@/lib/api/companies';
import { MailConfig } from '@/types/mail-config';
import { useToast } from '@/context/toast-context';

interface Company {
  id: string;
  name: string;
  connection_string: string;
  database_type: string;
  started_at: string;
  created_at: string;
  updated_at: string;
}

interface MailConfigFormProps {
  initialData?: MailConfig;
  isEditing?: boolean;
}

export default function MailConfigForm({ initialData, isEditing = false }: MailConfigFormProps) {
  const router = useRouter();
  const { showToast } = useToast();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(false);
  const [companiesLoading, setCompaniesLoading] = useState(true);
  const [testingConnection, setTestingConnection] = useState(false);
  const [showTestEmailInput, setShowTestEmailInput] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [useNoAuth, setUseNoAuth] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    sender_name: initialData?.sender_name || '',
    smtp_host: initialData?.smtp_host || '',
    smtp_port: initialData?.smtp_port || 587,
    smtp_username: initialData?.smtp_username || '',
    smtp_password: '',  // Never populate password
    sender_email: initialData?.sender_email || '',
    company_id: initialData?.company_id || '',
    use_ssl: initialData?.use_ssl !== undefined ? initialData.use_ssl : true,
    active: initialData?.active !== undefined ? initialData.active : true,
  });
  
  // Load companies for dropdown
  useEffect(() => {
    async function loadCompanies() {
      try {
        setCompaniesLoading(true);
        console.log('Loading companies for form...');
        
        // Fetch companies using the new direct export function
        const response = await getCompanies();
        console.log('Companies API response:', response);
        
        if (response && Array.isArray(response)) {
          setCompanies(response);
          console.log('Companies loaded successfully:', response);
        } else {
          console.error('Invalid companies response format:', response);
          showToast('Failed to load companies data');
          // Create empty array to prevent errors
          setCompanies([]);
        }
      } catch (error) {
        console.error('Error loading companies:', error);
        showToast('Failed to load companies');
      } finally {
        setCompaniesLoading(false);
      }
    }
    
    loadCompanies();
  }, [showToast]);
  
  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData({
        ...formData,
        [name]: checked,
      });
    } else if (name === 'smtp_port') {
      setFormData({
        ...formData,
        [name]: parseInt(value, 10) || 587, // Default to 587 if invalid
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check for required fields
    let requiredFields = ['smtp_host', 'smtp_port', 'sender_email', 'company_id'];
    
    // Only require username/password if not using No Auth mode
    if (!useNoAuth) {
      requiredFields.push('smtp_username');
    }
    
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);
    
    if (missingFields.length > 0) {
      showToast(`Please fill in all required fields: ${missingFields.join(', ')}`);
      return;
    }
    
    setLoading(true);
    
    try {
      // Prepare request data
      const requestData = {
        ...formData,
        // Only include password if it was changed (not empty)
        ...(formData.smtp_password ? { smtp_password: formData.smtp_password } : {}),
      };
      
      console.log('Submitting mail config data:', requestData);
      
      if (isEditing && initialData) {
        // Update existing configuration using direct export function
        const response = await updateMailConfig(initialData.id, requestData);
        console.log('Update response:', response);
        showToast(`SMTP Configuration "${response.mail_config.sender_name || 'Unnamed'}" updated successfully`);
      } else {
        // Create new configuration using direct export function
        const response = await createMailConfig(requestData);
        console.log('Create response:', response);
        showToast(`SMTP Configuration "${response.mail_config.sender_name || 'Unnamed'}" created successfully`);
      }
      
      // Redirect back to mail config list
      router.push('/mail-config');
    } catch (error) {
      console.error('Error saving mail configuration:', error);
      showToast('Failed to save SMTP configuration. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Test connection
  const handleTestConnection = async () => {
    // Check for required fields for testing
    let requiredFields = ['smtp_host', 'smtp_port', 'sender_email'];
    
    // Only require username for authenticated SMTP
    if (!useNoAuth) {
      requiredFields.push('smtp_username');
    }
    
    const missingFields = requiredFields.filter(field => !formData[field as keyof typeof formData]);
    
    if (missingFields.length > 0) {
      showToast(`Please fill in required fields before testing: ${missingFields.join(', ')}`);
      return;
    }
    
    // If no test email provided, use the sender email
    const emailToTest = testEmail.trim() || formData.sender_email;
    
    if (!emailToTest) {
      showToast('A test email address is required. Please provide one or set a sender email.');
      return;
    }
    
    setTestingConnection(true);
    
    try {
      // Prepare a config object for testing
      const testConfig = {
        ...initialData,
        ...formData,
        // If using no auth, send empty strings for username and password
        smtp_username: useNoAuth ? '' : formData.smtp_username,
        smtp_password: useNoAuth ? '' : (formData.smtp_password || (isEditing && initialData?.smtp_password ? "[UNCHANGED]" : "")),
      };
      
      // Test using the configuration with the test email using direct export function
      const result = await testMailConfig(testConfig, emailToTest);
      
      if (result.success) {
        showToast(`SMTP connection test successful! Test email sent to ${emailToTest}`);
        setShowTestEmailInput(false);
        setTestEmail('');
      } else {
        showToast(`SMTP connection test failed: ${result.message || result.error}`);
      }
    } catch (error: any) {
      console.error('Error testing SMTP connection:', error);
      showToast('SMTP connection test failed. Please check your settings and try again.');
    } finally {
      setTestingConnection(false);
    }
  };
  
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
            
            <div>
              <label htmlFor="sender_name" className="block text-sm font-medium text-gray-700">
                Sender Name
              </label>
              <input
                type="text"
                id="sender_name"
                name="sender_name"
                value={formData.sender_name}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="e.g., Company Name"
              />
            </div>
            
            <div>
              <label htmlFor="company_id" className="block text-sm font-medium text-gray-700">
                Company *
              </label>
              <select
                id="company_id"
                name="company_id"
                value={formData.company_id}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                disabled={companiesLoading}
                required
              >
                <option value="">Select a company</option>
                {companies && companies.length > 0 ? (
                  companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>No companies available</option>
                )}
              </select>
              {companiesLoading ? (
                <p className="mt-1 text-xs text-gray-500">Loading companies...</p>
              ) : companies.length === 0 ? (
                <p className="mt-1 text-xs text-red-500">Failed to load companies</p>
              ) : null}
            </div>
            
            <div>
              <label htmlFor="active" className="flex items-center">
                <input
                  type="checkbox"
                  id="active"
                  name="active"
                  checked={formData.active}
                  onChange={handleChange}
                  className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <span className="ml-2 text-sm text-gray-700">Active</span>
              </label>
              <p className="mt-1 text-xs text-gray-500">
                If disabled, this configuration won't be used for sending emails.
              </p>
            </div>
          </div>
          
          {/* SMTP Server Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">SMTP Server Settings</h3>
            
            <div>
              <label htmlFor="smtp_host" className="block text-sm font-medium text-gray-700">
                SMTP Host *
              </label>
              <input
                type="text"
                id="smtp_host"
                name="smtp_host"
                value={formData.smtp_host}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="e.g., smtp.gmail.com or mailhog"
                required
              />
            </div>
            
            <div>
              <label htmlFor="smtp_port" className="block text-sm font-medium text-gray-700">
                SMTP Port *
              </label>
              <input
                type="number"
                id="smtp_port"
                name="smtp_port"
                value={formData.smtp_port}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                min="1"
                max="65535"
                placeholder="587"
                required
              />
            </div>
            
            <div>
              <label htmlFor="use_ssl" className="flex items-center">
                <input
                  type="checkbox"
                  id="use_ssl"
                  name="use_ssl"
                  checked={formData.use_ssl}
                  onChange={handleChange}
                  className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <span className="ml-2 text-sm text-gray-700">Use SSL/TLS</span>
              </label>
              <p className="mt-1 text-xs text-gray-500">
                Enable for secure connections (recommended for production).
              </p>
            </div>
            
            <div>
              <label htmlFor="use_no_auth" className="flex items-center">
                <input
                  type="checkbox"
                  id="use_no_auth"
                  name="use_no_auth"
                  checked={useNoAuth}
                  onChange={(e) => setUseNoAuth(e.target.checked)}
                  className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <span className="ml-2 text-sm text-gray-700">No Authentication</span>
              </label>
              <p className="mt-1 text-xs text-gray-500">
                Enable for testing with servers that don't require authentication (like MailHog).
              </p>
            </div>
          </div>
          
          {/* Authentication */}
          <div className={`space-y-4 ${useNoAuth ? 'opacity-50' : ''}`}>
            <h3 className="text-lg font-medium text-gray-900">Authentication</h3>
            
            <div>
              <label htmlFor="smtp_username" className="block text-sm font-medium text-gray-700">
                SMTP Username {useNoAuth ? '' : '*'}
              </label>
              <input
                type="text"
                id="smtp_username"
                name="smtp_username"
                value={formData.smtp_username}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="e.g., <EMAIL>"
                required={!useNoAuth}
                disabled={useNoAuth}
              />
            </div>
            
            <div>
              <label htmlFor="smtp_password" className="block text-sm font-medium text-gray-700">
                SMTP Password {isEditing ? '(leave blank to keep current)' : useNoAuth ? '' : '*'}
              </label>
              <input
                type="password"
                id="smtp_password"
                name="smtp_password"
                value={formData.smtp_password}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder={isEditing ? "Leave blank to keep current password" : "Enter SMTP password"}
                required={!isEditing && !useNoAuth}
                disabled={useNoAuth}
              />
            </div>
          </div>
          
          {/* Email Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Email Settings</h3>
            
            <div>
              <label htmlFor="sender_email" className="block text-sm font-medium text-gray-700">
                From Email Address *
              </label>
              <input
                type="email"
                id="sender_email"
                name="sender_email"
                value={formData.sender_email}
                onChange={handleChange}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="e.g., <EMAIL>"
                required
              />
            </div>
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="mt-8 flex justify-end space-x-3">
          <button
            type="button"
            onClick={() => router.push('/mail-config')}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </button>
          
          {isEditing && (
            <div className="relative">
              {showTestEmailInput ? (
                <div className="absolute bottom-12 right-0 w-72 p-3 bg-white shadow-lg rounded-md border border-gray-200">
                  <div className="mb-2">
                    <label htmlFor="test_email" className="block text-sm font-medium text-gray-700">
                      Test Email Address
                    </label>
                    <input 
                      type="email"
                      id="test_email"
                      value={testEmail}
                      onChange={(e) => setTestEmail(e.target.value)}
                      placeholder={formData.sender_email || "Enter email for testing"}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Leave empty to use the sender email.
                    </p>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <button
                      type="button"
                      onClick={() => setShowTestEmailInput(false)}
                      className="px-2 py-1 text-xs text-gray-700 hover:text-gray-900"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={handleTestConnection}
                      disabled={testingConnection}
                      className="px-2 py-1 text-xs text-white bg-indigo-600 rounded hover:bg-indigo-700"
                    >
                      Send Test
                    </button>
                  </div>
                </div>
              ) : null}
              
              <button
                type="button"
                onClick={() => setShowTestEmailInput(!showTestEmailInput)}
                disabled={testingConnection}
                className="inline-flex items-center px-4 py-2 border border-indigo-300 shadow-sm text-sm font-medium rounded-md text-indigo-700 bg-indigo-50 hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                {testingConnection ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Testing...
                  </>
                ) : (
                  <>
                    <i className="fas fa-vial mr-2"></i>
                    Test Connection
                  </>
                )}
              </button>
            </div>
          )}
          
          <button
            type="submit"
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              <>
                <i className="fas fa-save mr-2"></i>
                {isEditing ? 'Update Configuration' : 'Create Configuration'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}