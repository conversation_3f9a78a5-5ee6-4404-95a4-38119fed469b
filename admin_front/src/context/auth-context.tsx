// contexts/auth-context.tsx - Fixed to use proper API URL handling

'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface User {
  id: number;
  email: string;
  username: string;
  role: string;
  first_name: string;
  last_name: string;
  active: boolean;
  last_login: string;
  created_at: string;
  updated_at: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [sessionExpiresAt, setSessionExpiresAt] = useState<number | null>(null);
  const router = useRouter();
  const pathname = usePathname();

  // Get API URL consistently
  const getApiUrl = () => {
    return process.env.NEXT_PUBLIC_API_URL || '/api';
  };

  // Handle authentication errors consistently
  const handleAuthError = () => {
    setUser(null);
    localStorage.removeItem('user');
    localStorage.removeItem('session_expires_at');
    
    // Only redirect if we're not already on the login page
    if (pathname !== '/login') {
      router.push('/login');
    }
  };

  // Function to refresh token - improved error handling
  const refreshToken = async (): Promise<boolean> => {
    try {
      console.log('🔄 [AUTH] Refreshing token...');
      
      const apiUrl = getApiUrl();
      const response = await fetch(`${apiUrl}/auth/refresh`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors'
      });
      
      if (!response.ok) {
        console.error('❌ [AUTH] Token refresh failed:', response.status);
        return false;
      }
      
      const data = await response.json();
      console.log('✅ [AUTH] Token refreshed successfully');
      
      // Save new expiry time
      if (data.expires_in) {
        const expiresAt = Math.floor(Date.now() / 1000) + data.expires_in;
        setSessionExpiresAt(expiresAt);
        localStorage.setItem('session_expires_at', expiresAt.toString());
        
        // Setup next refresh
        setupAutoRefresh(expiresAt);
      }
      
      return true;
    } catch (error) {
      console.error('❌ [AUTH] Token refresh error:', error);
      return false;
    }
  };

  // Setup automatic token refresh before expiry
  const setupAutoRefresh = (expiresAt: number) => {
    const now = Math.floor(Date.now() / 1000);
    const timeToRefresh = Math.max(0, (expiresAt - now - 300) * 1000); // Refresh 5 minutes before expiry
    
    console.log(`⏰ [AUTH] Setting up token refresh in ${timeToRefresh / 1000} seconds`);
    
    if (timeToRefresh <= 0) {
      // Token already expired or about to expire, refresh now
      refreshToken();
      return;
    }
    
    // Clean up any existing timer
    if (typeof window !== 'undefined' && window.__refreshTimer) {
      clearTimeout(window.__refreshTimer);
    }
    
    // Set timer to refresh token before it expires
    if (typeof window !== 'undefined') {
      window.__refreshTimer = setTimeout(() => {
        refreshToken();
      }, timeToRefresh);
    }
  };

  // Generic fetch with auth and proper error handling
  const fetchWithAuth = async (endpoint: string, options: RequestInit = {}) => {
    const apiUrl = getApiUrl();
    const url = `${apiUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };
    
    try {
      console.log(`📡 [AUTH] Fetching ${url} with method ${options.method || 'GET'}`);
      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include',
        mode: 'cors',
      });
      
      // If unauthorized, try to refresh token
      if (response.status === 401) {
        // Check if token refresh already attempted
        if (options.headers && (options.headers as any).__refreshAttempted) {
          throw new Error('Authentication failed even after token refresh');
        }
        
        const refreshSuccessful = await refreshToken();
        if (refreshSuccessful) {
          // Retry the original request
          return fetchWithAuth(endpoint, {
            ...options,
            headers: {
              ...options.headers,
              __refreshAttempted: true,
            },
          });
        } else {
          throw new Error('Token refresh failed');
        }
      }
      
      return response;
    } catch (error) {
      console.error(`❌ [AUTH] Fetch error for ${endpoint}:`, error);
      throw error;
    }
  };

  const checkAuth = async (): Promise<boolean> => {
    try {
      console.log('🔍 [AUTH] Checking authentication with server...');
      
      const response = await fetchWithAuth('/auth/status', {
        method: 'GET',
      });

      console.log('📥 [AUTH] Auth check response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ [AUTH] Auth status:', data);
        
        if (data.authenticated && data.user) {
          setUser(data.user);
          localStorage.setItem('user', JSON.stringify(data.user));
          
          // Update session expiry if provided
          if (data.expires_in) {
            const expiresAt = Math.floor(Date.now() / 1000) + data.expires_in;
            setSessionExpiresAt(expiresAt);
            localStorage.setItem('session_expires_at', expiresAt.toString());
            
            // Setup auto-refresh
            setupAutoRefresh(expiresAt);
          }
          
          return true;
        } else {
          console.log('❌ [AUTH] Auth failed: not authenticated');
          setUser(null);
          localStorage.removeItem('user');
          localStorage.removeItem('session_expires_at');
          return false;
        }
      } else {
        console.log('❌ [AUTH] Auth check failed');
        setUser(null);
        localStorage.removeItem('user');
        localStorage.removeItem('session_expires_at');
        return false;
      }
    } catch (error) {
      console.error('❌ [AUTH] Auth check error:', error);
      return false;
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      console.log('🔐 [AUTH] Attempting login...');
      
      const apiUrl = getApiUrl();
      const response = await fetch(`${apiUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        credentials: 'include',
        mode: 'cors'
      });

      console.log('📥 [AUTH] Login response status:', response.status);
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ [AUTH] Login failed:', errorData.message || errorData.error);
        return false;
      }
      
      const data = await response.json();
      console.log('✅ [AUTH] Login successful');
      
      // Store user data
      setUser(data.user);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Store session expiry
      if (data.expires_in) {
        const expiresAt = Math.floor(Date.now() / 1000) + data.expires_in;
        setSessionExpiresAt(expiresAt);
        localStorage.setItem('session_expires_at', expiresAt.toString());
        
        // Setup auto-refresh
        setupAutoRefresh(expiresAt);
      }
      
      return true;
    } catch (error) {
      console.error('❌ [AUTH] Login error:', error);
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      // Clear refresh timer
      if (typeof window !== 'undefined' && window.__refreshTimer) {
        clearTimeout(window.__refreshTimer);
      }
      
      const apiUrl = getApiUrl();
      await fetch(`${apiUrl}/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        mode: 'cors'
      });
      
      console.log('✅ [AUTH] Logout successful');
    } catch (error) {
      console.error('❌ [AUTH] Logout error:', error);
    } finally {
      // Always clear local state regardless of API response
      setUser(null);
      setSessionExpiresAt(null);
      localStorage.removeItem('user');
      localStorage.removeItem('session_expires_at');
      router.push('/login');
    }
  };

  // Debug API URL information
  const debugApiUrl = () => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'production') {
      console.log('🔧 [AUTH] Debug API URL information:');
      console.log('- NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
      console.log('- Resolved API URL:', getApiUrl());
      console.log('- Window origin:', window.location.origin);
      console.log('- Current pathname:', window.location.pathname);
    }
  };

  // Check authentication status on mount
  useEffect(() => {
    debugApiUrl();
    
    const initAuth = async () => {
      try {
        // First check localStorage for a user
        const storedUser = localStorage.getItem('user');
        const storedExpiresAt = localStorage.getItem('session_expires_at');
        
        let isValidSession = false;
        
        if (storedUser && storedExpiresAt) {
          try {
            const userData = JSON.parse(storedUser);
            const expiresAt = parseInt(storedExpiresAt, 10);
            const now = Math.floor(Date.now() / 1000);
            
            console.log('👤 [AUTH] Found user in localStorage:', userData.email);
            console.log(`⏰ [AUTH] Session expires at: ${new Date(expiresAt * 1000).toLocaleString()}`);
            
            if (expiresAt > now) {
              // User session is still valid according to localStorage
              setUser(userData);
              setSessionExpiresAt(expiresAt);
              isValidSession = true;
              
              // Setup auto-refresh
              setupAutoRefresh(expiresAt);
              
              // If we're on the login page but have a valid user session, redirect to dashboard
              if (pathname === '/login') {
                console.log('🔄 [AUTH] Redirecting from login to dashboard');
                router.push('/dashboard');
              }
            } else {
              console.log('⏰ [AUTH] Stored session has expired, trying to refresh');
              // Session expired, attempt to refresh
              const refreshSuccessful = await refreshToken();
              if (refreshSuccessful) {
                // If refresh worked, verify with server
                isValidSession = await checkAuth();
              } else {
                isValidSession = false;
              }
            }
          } catch (e) {
            console.error('❌ [AUTH] Error parsing stored user', e);
            localStorage.removeItem('user');
            localStorage.removeItem('session_expires_at');
          }
        }
        
        // If we couldn't establish a valid session from localStorage, check with the server
        if (!isValidSession) {
          console.log('🔍 [AUTH] No valid session found in localStorage, checking with server');
          isValidSession = await checkAuth();
        }
        
        if (!isValidSession) {
          console.log('❌ [AUTH] Could not establish a valid session');
          handleAuthError();
        }
      } catch (error) {
        console.error('❌ [AUTH] Error during initial auth check:', error);
        handleAuthError();
      } finally {
        setLoading(false);
      }
    };
    
    initAuth();
    
    // Setup event listeners for window focus/visibility to check auth
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        checkAuth();
      }
    };
    
    const handleFocus = () => {
      checkAuth();
    };
    
    if (typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('focus', handleFocus);
    
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('focus', handleFocus);
        
        // Clear refresh timer
        if (window.__refreshTimer) {
          clearTimeout(window.__refreshTimer);
        }
      };
    }
  }, [router, pathname]);

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        checkAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Type declaration for refresh timer
declare global {
  interface Window {
    __refreshTimer: ReturnType<typeof setTimeout>;
  }
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}